package main

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// ConnectionInfo Binance连接信息
type ConnectionInfo struct {
	conn           *websocket.Conn
	state          ConnectionState
	lastPingTime   time.Time
	lastPongTime   time.Time
	reconnectCount int
	heartbeatStop  chan struct{}

	// 连接质量监控
	connectionQuality   float64       // 连接质量评分 (0-1)
	avgPongDelay        time.Duration // 平均pong延迟
	consecutiveTimeouts int           // 连续超时次数
	lastQualityCheck    time.Time     // 上次质量检查时间
	messageCount        int64         // 消息计数
	lastMessageTime     time.Time     // 最后消息时间

	mutex sync.RWMutex
}

// BinanceClient 币安客户端
type BinanceClient struct {
	logger        *logrus.Logger
	symbols       []string
	wsConns       map[string]*ConnectionInfo
	mu            sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	config        *Config        // 统一配置
	kafkaProducer *KafkaProducer // Kafka生产者

	// 数据统计
	tradeCount   int64
	tickerCount  int64
	fundingCount int64
	statsMu      sync.RWMutex
}

// SymbolInfo 交易对信息
type SymbolInfo struct {
	Symbol string `json:"symbol"`
	Status string `json:"status"`
}

// ExchangeInfo 交易所信息
type ExchangeInfo struct {
	Symbols []SymbolInfo `json:"symbols"`
}

// TradeData 交易数据
type TradeData struct {
	EventType string `json:"e"`
	EventTime int64  `json:"E"`
	Symbol    string `json:"s"`
	TradeID   int64  `json:"t"`
	Price     string `json:"p"`
	Quantity  string `json:"q"`
	BuyerID   int64  `json:"b"`
	SellerID  int64  `json:"a"`
	TradeTime int64  `json:"T"`
	IsBuyer   bool   `json:"m"`
}

// BookTickerData 盘口数据
type BookTickerData struct {
	UpdateID     int64  `json:"u"`
	Symbol       string `json:"s"`
	BestBidPrice string `json:"b"`
	BestBidQty   string `json:"B"`
	BestAskPrice string `json:"a"`
	BestAskQty   string `json:"A"`
}

// MarkPriceData 标记价格数据（包含资金费率）
type MarkPriceData struct {
	EventType       string `json:"e"` // 事件类型
	EventTime       int64  `json:"E"` // 事件时间
	Symbol          string `json:"s"` // 交易对
	MarkPrice       string `json:"p"` // 标记价格
	IndexPrice      string `json:"i"` // 现货指数价格
	EstimatedPrice  string `json:"P"` // 预估结算价
	FundingRate     string `json:"r"` // 资金费率
	NextFundingTime int64  `json:"T"` // 下次资金时间
}

// NewBinanceClient 创建新的币安客户端
func NewBinanceClient() *BinanceClient {
	ctx, cancel := context.WithCancel(context.Background())

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	config, err := LoadConfig("config.json")
	if err != nil {
		logger.Warnf("⚠️ 加载配置文件失败，使用默认配置: %v", err)
		config = DefaultConfig()
	}

	// 初始化Kafka生产者
	kafkaProducer, err := NewKafkaProducer(&config.Kafka, logger)
	if err != nil {
		logger.Errorf("❌ 初始化Kafka生产者失败: %v", err)
		kafkaProducer = nil
	}

	return &BinanceClient{
		logger:        logger,
		wsConns:       make(map[string]*ConnectionInfo),
		ctx:           ctx,
		cancel:        cancel,
		config:        config,
		kafkaProducer: kafkaProducer,
	}
}

// GetAllSymbols 获取所有永续合约交易对
func (bc *BinanceClient) GetAllSymbols() error {
	bc.logger.Info("🔄 正在获取所有永续合约交易对...")

	resp, err := http.Get(bc.config.APIBaseURL + "/fapi/v1/exchangeInfo")
	if err != nil {
		return fmt.Errorf("获取交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var exchangeInfo ExchangeInfo
	if err := json.NewDecoder(resp.Body).Decode(&exchangeInfo); err != nil {
		return fmt.Errorf("解析交易对信息失败: %v", err)
	}

	bc.symbols = make([]string, 0)
	for _, symbol := range exchangeInfo.Symbols {
		if symbol.Status == "TRADING" {
			bc.symbols = append(bc.symbols, symbol.Symbol)
		}
	}

	bc.logger.Infof("✅ 成功获取 %d 个活跃交易对", len(bc.symbols))
	return nil
}

// SubscribeData 同时订阅交易数据和盘口数据
func (bc *BinanceClient) SubscribeData() error {
	bc.logger.Info("🚀 开始分批订阅交易数据和盘口数据...")

	// 分批处理，使用配置的批次大小
	batchSize := bc.config.BatchSize
	totalBatches := (len(bc.symbols) + batchSize - 1) / batchSize

	bc.logger.Infof("📊 分批订阅策略 - 总交易对: %d, 批次大小: %d, 总批次: %d",
		len(bc.symbols), batchSize, totalBatches)

	batchCount := 0

	for i := 0; i < len(bc.symbols); i += batchSize {
		end := i + batchSize
		if end > len(bc.symbols) {
			end = len(bc.symbols)
		}

		batch := bc.symbols[i:end]
		batchCount++

		bc.logger.Infof("📡 处理批次 %d/%d - 交易对数量: %d", batchCount, totalBatches, len(batch))

		// 为每批创建三个连接：交易数据、盘口数据、资金费率数据
		tradeStreamName := fmt.Sprintf("trade_batch_%d", batchCount)
		tickerStreamName := fmt.Sprintf("ticker_batch_%d", batchCount)
		fundingStreamName := fmt.Sprintf("funding_batch_%d", batchCount)

		// 订阅交易数据
		bc.logger.Infof("🔗 启动交易数据连接: %s", tradeStreamName)
		bc.wg.Add(1)
		go bc.subscribeTradeStreams(tradeStreamName, batch)

		// 增加连接间延迟，避免触发Binance的频率限制
		time.Sleep(5 * time.Second) // 增加到5秒

		// 订阅盘口数据
		bc.logger.Infof("🔗 启动盘口数据连接: %s", tickerStreamName)
		bc.wg.Add(1)
		go bc.subscribeTickerStreams(tickerStreamName, batch)

		// 增加连接间延迟
		time.Sleep(5 * time.Second) // 增加到5秒

		// 订阅资金费率数据
		bc.logger.Infof("💰 启动资金费率连接: %s", fundingStreamName)
		bc.wg.Add(1)
		go bc.subscribeFundingStreams(fundingStreamName, batch)

		// 批次间延迟，避免同时创建太多连接
		if batchCount < totalBatches {
			batchDelay := 3 * time.Second // 调整批次间延迟为3秒
			bc.logger.Infof("⏰ 批次 %d 完成，等待 %v 后继续下一批次...", batchCount, batchDelay)
			time.Sleep(batchDelay)
		}
	}

	bc.logger.Infof("✅ 所有分批订阅已启动，共创建 %d 批连接 (%d 个连接)", totalBatches, totalBatches*3)
	return nil
}

// subscribeTradeStreams 订阅交易数据流
func (bc *BinanceClient) subscribeTradeStreams(connName string, symbols []string) {
	defer bc.wg.Done()

	// 构建交易数据流
	streams := make([]string, len(symbols))
	for i, symbol := range symbols {
		streams[i] = strings.ToLower(symbol) + "@aggTrade"
	}

	bc.subscribeStreams(connName, streams, bc.handleTradeData)
}

// subscribeTickerStreams 订阅盘口数据流
func (bc *BinanceClient) subscribeTickerStreams(connName string, symbols []string) {
	defer bc.wg.Done()

	// 构建盘口数据流
	streams := make([]string, len(symbols))
	for i, symbol := range symbols {
		streams[i] = strings.ToLower(symbol) + "@bookTicker"
	}

	bc.subscribeStreams(connName, streams, bc.handleBookTickerData)
}

// subscribeFundingStreams 订阅资金费率数据流
func (bc *BinanceClient) subscribeFundingStreams(connName string, symbols []string) {
	defer bc.wg.Done()

	// 构建资金费率数据流（使用markPrice流）
	streams := make([]string, len(symbols))
	for i, symbol := range symbols {
		streams[i] = strings.ToLower(symbol) + "@markPrice"
	}

	bc.subscribeStreams(connName, streams, bc.handleFundingData)
}

// subscribeStreams 通用流订阅方法（带重连机制）
func (bc *BinanceClient) subscribeStreams(connName string, streams []string, handler func([]byte)) {
	streamParam := strings.Join(streams, "/")
	wsURL := bc.config.WSBaseURL + streamParam

	reconnectAttempts := 0
	currentDelay := bc.config.ReconnectDelay

	for {
		select {
		case <-bc.ctx.Done():
			bc.logger.Infof("🛑 连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if bc.connectAndListen(connName, wsURL, streams, handler, &reconnectAttempts) {
				// 如果是正常退出（收到停止信号），则结束
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++

			// 根据重连策略决定是否继续重连
			shouldContinue, delay := bc.shouldReconnect(connName, reconnectAttempts, currentDelay)
			if !shouldContinue {
				return
			}

			currentDelay = delay
			bc.logger.Warnf("⚠️ 连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			// 等待重连延迟
			select {
			case <-bc.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// shouldReconnect 根据重连策略决定是否应该重连
func (bc *BinanceClient) shouldReconnect(connName string, attempts int, currentDelay time.Duration) (bool, time.Duration) {
	switch bc.config.ReconnectStrategy {
	case ReconnectStrategyLimited:
		// 有限重连策略
		if attempts >= bc.config.MaxReconnectAttempts {
			bc.logger.Errorf("❌ 连接 %s 重连次数超过限制 (%d)，停止重连", connName, bc.config.MaxReconnectAttempts)
			return false, currentDelay
		}
		return true, bc.config.ReconnectDelay

	case ReconnectStrategyInfinite:
		// 无限重连策略
		if attempts%bc.config.MaxReconnectAttempts == 0 {
			bc.logger.Warnf("🔄 连接 %s 已重连 %d 次，继续尝试...", connName, attempts)
		}
		return true, bc.config.ReconnectDelay

	case ReconnectStrategyExponential:
		// 指数退避重连策略
		if attempts >= bc.config.MaxReconnectAttempts*3 {
			bc.logger.Errorf("❌ 连接 %s 指数退避重连次数超过限制 (%d)，停止重连", connName, bc.config.MaxReconnectAttempts*3)
			return false, currentDelay
		}

		// 计算指数退避延迟
		newDelay := currentDelay * 2
		if newDelay > bc.config.MaxReconnectDelay {
			newDelay = bc.config.MaxReconnectDelay
		}

		bc.logger.Infof("📈 连接 %s 使用指数退避，延迟从 %v 增加到 %v", connName, currentDelay, newDelay)
		return true, newDelay

	default:
		// 默认使用有限重连
		return bc.shouldReconnect(connName, attempts, currentDelay)
	}
}

// connectAndListen 建立连接并监听消息
func (bc *BinanceClient) connectAndListen(connName, wsURL string, streams []string, handler func([]byte), reconnectAttempts *int) bool {
	bc.logger.Infof("🔗 正在连接 %s，订阅 %d 个流 (尝试 %d)", connName, len(streams), *reconnectAttempts+1)

	// 检查流数量限制（Binance单连接最多1024个流）
	if len(streams) > 1024 {
		bc.logger.Errorf("❌ 流数量超过Binance限制 %s: %d > 1024", connName, len(streams))
		return false
	}

	// 配置WebSocket拨号器，使用更保守的设置
	dialer := websocket.Dialer{
		HandshakeTimeout:  60 * time.Second, // 增加握手超时到60秒
		ReadBufferSize:    32768,            // 增加读缓冲区到32KB
		WriteBufferSize:   32768,            // 增加写缓冲区到32KB
		EnableCompression: false,            // 禁用压缩减少CPU负载
	}

	// 建立WebSocket连接前添加随机延迟，避免同时连接
	randomDelay := time.Duration(rand.Intn(5000)) * time.Millisecond // 增加到0-5秒
	bc.logger.Debugf("🔗 连接 %s 随机延迟 %v 后建立连接", connName, randomDelay)
	time.Sleep(randomDelay)

	// 建立WebSocket连接，增加重试机制
	var conn *websocket.Conn
	var err error
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		conn, _, err = dialer.Dial(wsURL, nil)
		if err == nil {
			break
		}

		bc.logger.Warnf("⚠️ WebSocket连接失败 %s (尝试 %d/%d): %v", connName, i+1, maxRetries, err)
		if i < maxRetries-1 {
			retryDelay := time.Duration(i+1) * 5 * time.Second
			bc.logger.Infof("🔄 等待 %v 后重试连接 %s", retryDelay, connName)
			time.Sleep(retryDelay)
		}
	}

	if err != nil {
		bc.logger.Errorf("❌ WebSocket连接最终失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器（连接成功）
	*reconnectAttempts = 0

	// 创建连接信息
	connInfo := &ConnectionInfo{
		conn:          conn,
		state:         StateConnected,
		lastPingTime:  time.Now(),
		lastPongTime:  time.Now(),
		heartbeatStop: make(chan struct{}, 1),
	}

	bc.mu.Lock()
	bc.wsConns[connName] = connInfo
	bc.mu.Unlock()

	defer func() {
		bc.mu.Lock()
		delete(bc.wsConns, connName)
		bc.mu.Unlock()
		conn.Close()
		bc.logger.Infof("🔌 连接 %s 已关闭", connName)
	}()

	bc.logger.Infof("✅ 连接 %s 建立成功", connName)

	// 设置增强的pong处理器 - 被动响应服务器ping
	conn.SetPongHandler(func(appData string) error {
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		bc.logger.Debugf("💓 [Binance] 连接 %s 收到WebSocket pong响应", connName)
		return conn.SetReadDeadline(time.Now().Add(bc.config.PongTimeout))
	})

	// 设置ping处理器 - 被动响应服务器ping
	conn.SetPingHandler(func(appData string) error {
		// 立即响应服务器的ping
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			bc.logger.Warnf("💔 [Binance] 连接 %s 响应服务器ping失败: %v", connName, err)
			return err
		}
		// 更新最后pong时间
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		bc.logger.Debugf("💓 [Binance] 连接 %s 响应服务器ping", connName)
		return nil
	})

	// 启动增强心跳机制
	go bc.startEnhancedHeartbeat(connName, connInfo)

	// 设置初始读取超时
	conn.SetReadDeadline(time.Now().Add(bc.config.PongTimeout))

	// 监听消息
	messageCount := 0
	lastMessageTime := time.Now()

	for {
		select {
		case <-bc.ctx.Done():
			bc.logger.Infof("🛑 连接 %s 收到停止信号，共处理 %d 条消息", connName, messageCount)
			return true // 正常退出
		default:
			// 设置更长的读取超时，避免误判
			conn.SetReadDeadline(time.Now().Add(300 * time.Second)) // 增加到5分钟

			_, message, err := conn.ReadMessage()
			if err != nil {
				// 更详细的错误分类处理
				if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
					bc.logger.Infof("ℹ️ WebSocket正常关闭 %s: %v", connName, err)
					return false // 需要重连
				} else if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNoStatusReceived) {
					bc.logger.Errorf("❌ WebSocket异常关闭 %s: %v", connName, err)
					return false // 需要重连
				} else {
					bc.logger.Warnf("⚠️ WebSocket连接中断 %s: %v", connName, err)
					return false // 需要重连
				}
			}

			messageCount++
			lastMessageTime = time.Now()

			// 每5000条消息记录一次，减少日志输出
			if messageCount%5000 == 0 {
				bc.logger.Debugf("📊 连接 %s 已处理 %d 条消息", connName, messageCount)
			}

			handler(message)

			// 检查消息间隔，如果超过10分钟没有消息，可能连接有问题
			if time.Since(lastMessageTime) > 10*time.Minute {
				bc.logger.Warnf("⚠️ 连接 %s 超过10分钟没有收到消息，可能连接异常", connName)
				return false // 需要重连
			}
		}
	}
}

// handleTradeData 处理交易数据
func (bc *BinanceClient) handleTradeData(message []byte) {
	// 解析交易数据
	symbol := gjson.GetBytes(message, "s").String()
	if symbol == "" {
		return
	}

	// 发送到Kafka
	if bc.kafkaProducer != nil {
		// 解析完整的交易数据
		var tradeData TradeData
		if err := json.Unmarshal(message, &tradeData); err == nil {
			bc.kafkaProducer.SendTradeData(symbol, tradeData)
		}
	}

	// 更新统计
	bc.statsMu.Lock()
	bc.tradeCount++
	bc.statsMu.Unlock()
}

// handleBookTickerData 处理盘口数据
func (bc *BinanceClient) handleBookTickerData(message []byte) {
	// 解析盘口数据
	symbol := gjson.GetBytes(message, "s").String()
	if symbol == "" {
		return
	}

	// 发送到Kafka
	if bc.kafkaProducer != nil {
		// 解析完整的盘口数据
		var bookTickerData BookTickerData
		if err := json.Unmarshal(message, &bookTickerData); err == nil {
			bc.kafkaProducer.SendOrderbookData(symbol, bookTickerData)
		}
	}

	// 更新统计
	bc.statsMu.Lock()
	bc.tickerCount++
	bc.statsMu.Unlock()
}

// handleFundingData 处理资金费率数据
func (bc *BinanceClient) handleFundingData(message []byte) {
	// 解析JSON数据
	eventType := gjson.GetBytes(message, "e").String()
	if eventType != "markPriceUpdate" {
		return
	}

	// 解析交易对
	symbol := gjson.GetBytes(message, "s").String()
	if symbol == "" {
		return
	}

	// 发送到Kafka
	if bc.kafkaProducer != nil {
		// 解析完整的资金费率数据
		var markPriceData MarkPriceData
		if err := json.Unmarshal(message, &markPriceData); err == nil {
			bc.kafkaProducer.SendFundingData(symbol, markPriceData)
		}
	}

	// 更新统计
	bc.statsMu.Lock()
	bc.fundingCount++
	bc.statsMu.Unlock()
}

// PrintStats 打印统计信息
func (bc *BinanceClient) PrintStats() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒显示一次统计
	defer ticker.Stop()

	startTime := time.Now()

	for {
		select {
		case <-bc.ctx.Done():
			return
		case <-ticker.C:
			bc.statsMu.RLock()
			tradeCount := bc.tradeCount
			tickerCount := bc.tickerCount
			fundingCount := bc.fundingCount
			bc.statsMu.RUnlock()

			bc.mu.RLock()
			connCount := len(bc.wsConns)
			connNames := make([]string, 0, len(bc.wsConns))
			for name := range bc.wsConns {
				connNames = append(connNames, name)
			}
			bc.mu.RUnlock()

			runtime := time.Since(startTime)
			expectedConnections := (len(bc.symbols) + bc.config.BatchSize - 1) / bc.config.BatchSize * 3 // 每批3个连接

			// 详细的统计信息
			bc.logger.Infof("📊 [Binance] 全局统计 - 运行时间: %v, 活跃连接: %d/%d, 交易消息: %d, 盘口消息: %d, 资金费率: %d, 总消息: %d",
				runtime.Round(time.Second),
				connCount, expectedConnections,
				tradeCount, tickerCount, fundingCount, tradeCount+tickerCount+fundingCount)

			// 连接状态详情
			if connCount > 0 {
				tradeConnections := 0
				tickerConnections := 0
				fundingConnections := 0
				for name := range bc.wsConns {
					if strings.Contains(name, "trade") {
						tradeConnections++
					} else if strings.Contains(name, "ticker") {
						tickerConnections++
					} else if strings.Contains(name, "funding") {
						fundingConnections++
					}
				}
				bc.logger.Infof("🔗 [Binance] 连接状态 - 交易连接: %d, 盘口连接: %d, 资金费率连接: %d",
					tradeConnections, tickerConnections, fundingConnections)
			}

			// 数据流速率统计（每秒消息数）
			if runtime.Seconds() > 0 {
				tradeRate := float64(tradeCount) / runtime.Seconds()
				tickerRate := float64(tickerCount) / runtime.Seconds()
				fundingRate := float64(fundingCount) / runtime.Seconds()
				bc.logger.Infof("📈 [Binance] 数据流速率 - 交易: %.1f msg/s, 盘口: %.1f msg/s, 资金费率: %.1f msg/s",
					tradeRate, tickerRate, fundingRate)
			}

			// 如果连接数异常少，发出警告
			if connCount < expectedConnections/2 {
				bc.logger.Warnf("⚠️ [Binance] 连接数异常：当前 %d，预期 %d，活跃连接: %v",
					connCount, expectedConnections, connNames)
			}

			// 数据流健康检查
			if connCount > 0 && tradeCount == 0 && tickerCount == 0 && fundingCount == 0 {
				bc.logger.Warnf("⚠️ [Binance] 数据流异常：有连接但没有数据")
			}
		}
	}
}

// MonitorConnections 监控连接健康状态
func (bc *BinanceClient) MonitorConnections() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-bc.ctx.Done():
			return
		case <-ticker.C:
			bc.mu.RLock()
			connCount := len(bc.wsConns)
			connDetails := make(map[string]bool)
			for name := range bc.wsConns {
				connDetails[name] = true
			}
			bc.mu.RUnlock()

			expectedConnections := (len(bc.symbols) + bc.config.BatchSize - 1) / bc.config.BatchSize * 3 // 每批3个连接

			// 详细的连接监控
			bc.logger.Infof("🔍 [Binance] 连接监控 - 总连接: %d/%d, 交易对数: %d, 批次大小: %d",
				connCount, expectedConnections, len(bc.symbols), bc.config.BatchSize)

			// 连接类型统计
			tradeConnections := 0
			tickerConnections := 0
			fundingConnections := 0
			for name := range connDetails {
				if strings.Contains(name, "trade") {
					tradeConnections++
				} else if strings.Contains(name, "ticker") {
					tickerConnections++
				} else if strings.Contains(name, "funding") {
					fundingConnections++
				}
			}

			bc.logger.Infof("🔗 [Binance] 连接类型分布 - 交易连接: %d, 盘口连接: %d, 资金费率连接: %d",
				tradeConnections, tickerConnections, fundingConnections)

			// 如果连接数严重不足，记录警告
			if connCount < expectedConnections/3 {
				bc.logger.Errorf("🚨 [Binance] 严重警告：连接数严重不足！当前: %d, 预期: %d", connCount, expectedConnections)
			}

			// 检查数据流是否正常
			bc.statsMu.RLock()
			currentTradeCount := bc.tradeCount
			currentTickerCount := bc.tickerCount
			currentFundingCount := bc.fundingCount
			bc.statsMu.RUnlock()

			// 数据流健康检查
			if connCount > 0 && currentTradeCount == 0 && currentTickerCount == 0 && currentFundingCount == 0 {
				bc.logger.Warnf("⚠️ [Binance] 数据流异常：有连接但没有数据")
			}

			// WebSocket连接健康状态
			healthyConnections := connCount
			if healthyConnections == expectedConnections {
				bc.logger.Infof("✅ [Binance] 所有WebSocket连接健康")
			} else if healthyConnections >= expectedConnections*2/3 {
				bc.logger.Warnf("⚠️ [Binance] 部分WebSocket连接异常")
			} else {
				bc.logger.Errorf("❌ [Binance] 大量WebSocket连接异常")
			}
		}
	}
}

// Start 启动客户端
func (bc *BinanceClient) Start() error {
	// 获取所有交易对
	if err := bc.GetAllSymbols(); err != nil {
		return err
	}

	// 启动统计信息打印
	go bc.PrintStats()

	// 启动连接健康监控
	go bc.MonitorConnections()

	// 订阅数据
	if err := bc.SubscribeData(); err != nil {
		return err
	}

	bc.logger.Info("🎉 所有订阅已启动，等待数据...")
	bc.logger.Info("💡 程序具备以下健壮性保障：")
	bc.logger.Infof("   • 自动重连机制：连接断开后自动重连（最多%d次）", bc.config.MaxReconnectAttempts)
	bc.logger.Infof("   • 心跳检测：每%v发送心跳，检测连接状态", bc.config.PingInterval)
	bc.logger.Info("   • 连接监控：实时监控连接健康状态")
	bc.logger.Info("   • 数据流检测：监控数据流是否正常")
	bc.logger.Info("   • 24小时断线重连：应对交易所定期断线")
	bc.logger.Infof("   • 批次处理：每批%d个交易对，减少连接压力", bc.config.BatchSize)

	return nil
}

// Stop 停止客户端
func (bc *BinanceClient) Stop() {
	bc.logger.Info("🛑 正在停止客户端...")
	bc.cancel()

	// 关闭所有WebSocket连接
	bc.mu.Lock()
	for name, conn := range bc.wsConns {
		bc.logger.Infof("🔌 关闭连接: %s", name)
		if conn.conn != nil {
			conn.conn.Close()
		}
	}
	bc.mu.Unlock()

	// 等待所有goroutine结束
	bc.wg.Wait()

	// 关闭Kafka生产者
	if bc.kafkaProducer != nil {
		bc.kafkaProducer.Close()
	}

	// 打印最终统计
	bc.statsMu.RLock()
	tradeCount := bc.tradeCount
	tickerCount := bc.tickerCount
	bc.statsMu.RUnlock()

	bc.logger.Infof("✅ 客户端已停止 - 总交易数据: %d 条, 总盘口数据: %d 条", tradeCount, tickerCount)
}

func main() {
	// 创建币安客户端
	client := NewBinanceClient()

	// 启动客户端
	if err := client.Start(); err != nil {
		client.logger.Fatalf("❌ 启动客户端失败: %v", err)
	}

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan
	client.logger.Info("🛑 收到停止信号")

	// 停止客户端
	client.Stop()
}
