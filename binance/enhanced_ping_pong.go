package main

import (
	"math/rand"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// startEnhancedHeartbeat 启动增强心跳机制
func (bc *BinanceClient) startEnhancedHeartbeat(connName string, connInfo *ConnectionInfo) {
	// 主动ping间隔
	pingTicker := time.NewTicker(bc.config.PingInterval)
	defer pingTicker.Stop()

	// 连接健康检查间隔（更频繁）
	healthTicker := time.NewTicker(bc.config.PingInterval / 2)
	defer healthTicker.Stop()

	bc.logger.Infof("💓 [Binance] 连接 %s 增强心跳启动 - ping间隔: %v, 健康检查间隔: %v",
		connName, bc.config.PingInterval, bc.config.PingInterval/2)

	for {
		select {
		case <-pingTicker.C:
			// 主动发送ping保持连接活跃
			bc.sendActivePing(connName, connInfo)

		case <-healthTicker.C:
			// 连接健康检查
			if !bc.checkConnectionHealth(connName, connInfo) {
				bc.logger.Warnf("💔 [Binance] 连接 %s 健康检查失败，关闭连接", connName)
				bc.cleanupConnection(connName, connInfo)
				return
			}

		case <-connInfo.heartbeatStop:
			bc.logger.Infof("💓 [Binance] 连接 %s 心跳停止", connName)
			return
		case <-bc.ctx.Done():
			bc.logger.Infof("💓 [Binance] 连接 %s 心跳因上下文取消而停止", connName)
			return
		}
	}
}

// sendActivePing 主动发送ping
func (bc *BinanceClient) sendActivePing(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state == StateDisconnected || state == StateStopped {
		return
	}

	// 发送WebSocket ping帧，增加超时时间
	conn.SetWriteDeadline(time.Now().Add(30 * time.Second)) // 增加写入超时
	if err := conn.WriteMessage(websocket.PingMessage, []byte("ping")); err != nil {
		bc.logger.Warnf("💔 [Binance] 连接 %s WebSocket ping发送失败: %v", connName, err)
		// 不立即清理连接，给一次机会
		connInfo.mutex.Lock()
		connInfo.consecutiveTimeouts++
		timeouts := connInfo.consecutiveTimeouts
		connInfo.mutex.Unlock()

		if timeouts >= 3 { // 连续3次ping失败才清理连接
			bc.logger.Errorf("💔 [Binance] 连接 %s 连续%d次ping失败，清理连接", connName, timeouts)
			bc.cleanupConnection(connName, connInfo)
		}
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	bc.logger.Debugf("💓 [Binance] 连接 %s 发送ping", connName)
}

// checkConnectionHealth 检查连接健康状态
func (bc *BinanceClient) checkConnectionHealth(connName string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	connInfo.mutex.RUnlock()

	if conn == nil {
		bc.logger.Debugf("🔍 [Binance] 连接 %s 健康检查：连接为空", connName)
		return false
	}

	if state == StateDisconnected || state == StateStopped {
		bc.logger.Debugf("🔍 [Binance] 连接 %s 健康检查：状态为 %v", connName, state)
		return false
	}

	// 只有在Connected状态下才进行严格的超时检查
	if state == StateConnected {
		now := time.Now()
		pongDelay := now.Sub(lastPongTime)
		pingDelay := now.Sub(lastPingTime)

		// 使用更宽松的超时策略，避免误判
		maxPongDelay := bc.config.PingInterval * 20 // 20倍ping间隔 (10分钟)
		maxPingDelay := bc.config.PingInterval * 10 // 10倍ping间隔 (5分钟)

		if pongDelay > maxPongDelay {
			bc.logger.Warnf("💔 [Binance] 连接 %s pong超时: %.1fs (最大允许: %.1fs)",
				connName, pongDelay.Seconds(), maxPongDelay.Seconds())
			return false
		}

		if pingDelay > maxPingDelay {
			bc.logger.Warnf("⚠️ [Binance] 连接 %s ping间隔过长: %.1fs", connName, pingDelay.Seconds())
		}

		// 更新连接质量评估
		bc.updateConnectionQuality(connName, connInfo, pongDelay, pingDelay)
	}

	return true
}

// updateConnectionQuality 更新连接质量评估
func (bc *BinanceClient) updateConnectionQuality(connName string, connInfo *ConnectionInfo, pongDelay, pingDelay time.Duration) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	// 更新平均pong延迟（使用指数移动平均）
	if connInfo.avgPongDelay == 0 {
		connInfo.avgPongDelay = pongDelay
	} else {
		// 使用0.2的权重进行平滑
		connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*0.8 + float64(pongDelay)*0.2)
	}

	// 检查是否超时
	isTimeout := pongDelay > bc.config.PingInterval*4
	if isTimeout {
		connInfo.consecutiveTimeouts++
		bc.logger.Warnf("⚠️ [Binance] 连接 %s 超时 (第%d次): pong延迟=%.1fs",
			connName, connInfo.consecutiveTimeouts, pongDelay.Seconds())
	} else {
		connInfo.consecutiveTimeouts = 0
	}

	// 计算连接质量评分 (0-1)
	quality := 1.0

	// 基于pong延迟的评分
	idealDelay := bc.config.PingInterval
	if pongDelay > idealDelay {
		delayPenalty := float64(pongDelay-idealDelay) / float64(idealDelay*3)
		if delayPenalty > 1.0 {
			delayPenalty = 1.0
		}
		quality -= delayPenalty * 0.4 // 延迟占40%权重
	}

	// 基于连续超时的评分
	if connInfo.consecutiveTimeouts > 0 {
		timeoutPenalty := float64(connInfo.consecutiveTimeouts) / 5.0 // 5次超时为满分惩罚
		if timeoutPenalty > 1.0 {
			timeoutPenalty = 1.0
		}
		quality -= timeoutPenalty * 0.3 // 超时占30%权重
	}

	// 基于消息活跃度的评分
	messageAge := now.Sub(connInfo.lastMessageTime)
	if messageAge > 30*time.Second {
		agePenalty := float64(messageAge-30*time.Second) / float64(60*time.Second)
		if agePenalty > 1.0 {
			agePenalty = 1.0
		}
		quality -= agePenalty * 0.3 // 消息活跃度占30%权重
	}

	// 确保质量评分在合理范围内
	if quality < 0 {
		quality = 0
	}

	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	// 根据质量评分输出不同级别的日志
	if quality < 0.3 {
		bc.logger.Warnf("💔 [Binance] 连接 %s 质量极差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.6 {
		bc.logger.Warnf("⚠️ [Binance] 连接 %s 质量较差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.8 {
		bc.logger.Infof("📊 [Binance] 连接 %s 质量一般: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	} else {
		bc.logger.Infof("✅ [Binance] 连接 %s 质量良好: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	}
}

// cleanupConnection 清理连接
func (bc *BinanceClient) cleanupConnection(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	bc.logger.Infof("🧹 [Binance] 清理连接 %s", connName)

	if connInfo.conn != nil {
		// 发送关闭消息
		connInfo.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		connInfo.conn.Close()
		connInfo.conn = nil
	}

	// 停止心跳，使用非阻塞方式
	select {
	case connInfo.heartbeatStop <- struct{}{}:
	default:
	}

	connInfo.state = StateDisconnected
}

// calculateSmartReconnectDelay 智能重连延迟计算
func (bc *BinanceClient) calculateSmartReconnectDelay(connName string, reconnectCount int) time.Duration {
	// 基础延迟
	baseDelay := time.Duration(1<<uint(min(reconnectCount, 6))) * 3 * time.Second // 3s, 6s, 12s, 24s, 48s, 96s, 192s

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(5000)) * time.Millisecond // 0-5秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connName, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高，延迟更短
	} else if strings.Contains(connName, "ticker") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connName, "funding") {
		typeMultiplier = 1.2 // 资金费率数据优先级较低
	}

	// 根据重连次数动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连的连接增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	bc.logger.Infof("🧮 [Binance] 连接 %s 重连延迟计算: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connName, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
