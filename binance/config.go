package main

import (
	"encoding/json"
	"os"
	"time"
)

// ReconnectStrategy 重连策略
type ReconnectStrategy string

const (
	// ReconnectStrategyLimited 有限重连：达到最大次数后停止该连接
	ReconnectStrategyLimited ReconnectStrategy = "limited"
	// ReconnectStrategyInfinite 无限重连：永远尝试重连
	ReconnectStrategyInfinite ReconnectStrategy = "infinite"
	// ReconnectStrategyExponential 指数退避重连：重连间隔逐渐增加
	ReconnectStrategyExponential ReconnectStrategy = "exponential"
)

// Config 配置结构
type Config struct {
	// API配置
	APIBaseURL string `json:"api_base_url"`
	WSBaseURL  string `json:"ws_base_url"`

	// 连接配置
	BatchSize            int               `json:"batch_size"`
	MaxReconnectAttempts int               `json:"max_reconnect_attempts"`
	ReconnectDelay       time.Duration     `json:"-"`
	ReconnectDelayStr    string            `json:"reconnect_delay"`
	ReconnectStrategy    ReconnectStrategy `json:"reconnect_strategy"`
	MaxReconnectDelay    time.Duration     `json:"-"`
	MaxReconnectDelayStr string            `json:"max_reconnect_delay"`
	PingInterval         time.Duration     `json:"-"`
	PingIntervalStr      string            `json:"ping_interval"`
	PongTimeout          time.Duration     `json:"-"`
	PongTimeoutStr       string            `json:"pong_timeout"`
	ConnectionTimeout    time.Duration     `json:"-"`
	ConnectionTimeoutStr string            `json:"connection_timeout"`
	ReadTimeout          time.Duration     `json:"-"`
	ReadTimeoutStr       string            `json:"read_timeout"`
	WriteTimeout         time.Duration     `json:"-"`
	WriteTimeoutStr      string            `json:"write_timeout"`

	// 日志配置
	LogLevel string `json:"log_level"`

	// 数据订阅配置
	EnableTradeData      bool `json:"enable_trade_data"`
	EnableBookTicker     bool `json:"enable_book_ticker"`
	EnableDepthData      bool `json:"enable_depth_data"`
	EnableDataValidation bool `json:"enable_data_validation"`

	// 过滤配置
	SymbolFilter []string `json:"symbol_filter"` // 空数组表示订阅所有交易对

	// Kafka配置
	Kafka KafkaConfig `json:"kafka"`
}

// KafkaConfig Kafka配置结构
type KafkaConfig struct {
	Enabled          bool                `json:"enabled"`
	Brokers          []string            `json:"brokers"`
	SecurityProtocol string              `json:"security_protocol"`
	SASLMechanism    string              `json:"sasl_mechanism"`
	SASLUsername     string              `json:"sasl_username"`
	SASLPassword     string              `json:"sasl_password"`
	Topics           KafkaTopics         `json:"topics"`
	ProducerConfig   KafkaProducerConfig `json:"producer_config"`
}

// KafkaTopics Kafka主题配置
type KafkaTopics struct {
	Trades     string `json:"trades"`
	Orderbooks string `json:"orderbooks"`
	Funding    string `json:"funding"`
}

// KafkaProducerConfig Kafka生产者配置
type KafkaProducerConfig struct {
	Acks         string `json:"acks"`
	Retries      int    `json:"retries"`
	BatchSize    int    `json:"batch_size"`
	LingerMs     int    `json:"linger_ms"`
	BufferMemory int    `json:"buffer_memory"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	config := &Config{
		// API配置
		APIBaseURL: "https://fapi.binance.com",
		WSBaseURL:  "wss://fstream.binance.com/ws/",

		// 连接配置
		BatchSize:            100,                       // 每批处理的交易对数量
		MaxReconnectAttempts: 10,                        // 最大重连次数
		ReconnectDelay:       5 * time.Second,           // 重连延迟
		ReconnectStrategy:    ReconnectStrategyInfinite, // 默认无限重连
		MaxReconnectDelay:    5 * time.Minute,           // 最大重连延迟（指数退避用）
		PingInterval:         30 * time.Second,          // 心跳间隔
		PongTimeout:          10 * time.Second,          // 心跳超时
		ConnectionTimeout:    10 * time.Second,          // 连接超时
		ReadTimeout:          60 * time.Second,          // 读取超时
		WriteTimeout:         10 * time.Second,          // 写入超时

		// 日志配置
		LogLevel: "info",

		// 数据订阅配置
		EnableTradeData:      true,  // 启用交易数据
		EnableBookTicker:     true,  // 启用盘口数据
		EnableDepthData:      false, // 禁用深度数据
		EnableDataValidation: true,  // 启用数据验证

		// 过滤配置
		SymbolFilter: []string{}, // 空数组表示订阅所有交易对
	}

	// 设置时间字符串字段
	config.setDurationStrings()
	return config
}

// LoadConfig 从文件加载配置
func LoadConfig(filename string) (*Config, error) {
	config := DefaultConfig()

	if _, err := os.Stat(filename); os.IsNotExist(err) {
		// 如果文件不存在，保存默认配置
		if err := SaveConfig(config, filename); err != nil {
			return nil, err
		}
		return config, nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	// 解析配置
	if err := json.Unmarshal(data, config); err != nil {
		return nil, err
	}

	// 解析时间字符串
	if err := config.parseDurations(); err != nil {
		return nil, err
	}

	return config, nil
}

// SaveConfig 保存配置文件
func SaveConfig(config *Config, filename string) error {
	// 设置时间字符串
	config.setDurationStrings()

	// 序列化配置
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(filename, data, 0644)
}

// parseDurations 解析时间字符串
func (c *Config) parseDurations() error {
	var err error

	if c.ReconnectDelayStr != "" {
		c.ReconnectDelay, err = time.ParseDuration(c.ReconnectDelayStr)
		if err != nil {
			return err
		}
	}

	if c.MaxReconnectDelayStr != "" {
		c.MaxReconnectDelay, err = time.ParseDuration(c.MaxReconnectDelayStr)
		if err != nil {
			return err
		}
	}

	if c.PingIntervalStr != "" {
		c.PingInterval, err = time.ParseDuration(c.PingIntervalStr)
		if err != nil {
			return err
		}
	}

	if c.PongTimeoutStr != "" {
		c.PongTimeout, err = time.ParseDuration(c.PongTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.ConnectionTimeoutStr != "" {
		c.ConnectionTimeout, err = time.ParseDuration(c.ConnectionTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.ReadTimeoutStr != "" {
		c.ReadTimeout, err = time.ParseDuration(c.ReadTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.WriteTimeoutStr != "" {
		c.WriteTimeout, err = time.ParseDuration(c.WriteTimeoutStr)
		if err != nil {
			return err
		}
	}

	return nil
}

// setDurationStrings 设置时间字符串
func (c *Config) setDurationStrings() {
	c.ReconnectDelayStr = c.ReconnectDelay.String()
	c.MaxReconnectDelayStr = c.MaxReconnectDelay.String()
	c.PingIntervalStr = c.PingInterval.String()
	c.PongTimeoutStr = c.PongTimeout.String()
	c.ConnectionTimeoutStr = c.ConnectionTimeout.String()
	c.ReadTimeoutStr = c.ReadTimeout.String()
	c.WriteTimeoutStr = c.WriteTimeout.String()
}
