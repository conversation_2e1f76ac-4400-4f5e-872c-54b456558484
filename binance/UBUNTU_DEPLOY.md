# Ubuntu 22.04 部署指南

## 1. 快速部署

### 一键部署脚本

```bash
# 下载并运行部署脚本
wget -O deploy.sh https://your-server/deploy.sh
chmod +x deploy.sh
./deploy.sh
```

## 2. 手动部署步骤

### 步骤1：安装Go环境

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install wget curl git make -y

# 下载并安装Go 1.21
cd /tmp
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo rm -rf /usr/local/go
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

### 步骤2：上传项目文件

```bash
# 创建项目目录
mkdir -p ~/binance-futures-data
cd ~/binance-futures-data

# 方式1：使用scp从本地上传
# scp -r /local/path/to/project/* username@server-ip:~/binance-futures-data/

# 方式2：手动创建文件（复制所有项目文件）
```

### 步骤3：构建和运行

```bash
cd ~/binance-futures-data

# 安装依赖
go mod tidy

# 构建项目
go build -o build/binance-data-subscriber .

# 运行程序
./build/binance-data-subscriber
```

## 3. 作为系统服务运行

### 创建systemd服务

```bash
# 复制服务文件
sudo cp binance-data.service /etc/systemd/system/

# 修改服务文件中的用户和路径（如果需要）
sudo nano /etc/systemd/system/binance-data.service

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable binance-data.service

# 启动服务
sudo systemctl start binance-data.service

# 查看服务状态
sudo systemctl status binance-data.service
```

### 服务管理命令

```bash
# 启动服务
sudo systemctl start binance-data

# 停止服务
sudo systemctl stop binance-data

# 重启服务
sudo systemctl restart binance-data

# 查看日志
sudo journalctl -u binance-data -f

# 查看最近的日志
sudo journalctl -u binance-data --since "1 hour ago"
```

## 4. 防火墙配置

```bash
# 如果使用ufw防火墙，确保允许出站连接
sudo ufw allow out 443/tcp  # HTTPS
sudo ufw allow out 80/tcp   # HTTP
sudo ufw allow out 53/tcp   # DNS
sudo ufw allow out 53/udp   # DNS
```

## 5. 监控和日志

### 查看实时日志

```bash
# 查看服务日志
sudo journalctl -u binance-data -f

# 查看系统资源使用
htop

# 查看网络连接
ss -tulpn | grep binance
```

### 日志轮转配置

创建 `/etc/logrotate.d/binance-data`：

```
/var/log/binance-data/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        systemctl reload binance-data
    endscript
}
```

## 6. 性能优化

### 系统参数调优

```bash
# 增加文件描述符限制
echo "ubuntu soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "ubuntu hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 网络参数优化
echo "net.core.rmem_max = 16777216" | sudo tee -a /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_rmem = 4096 87380 16777216" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_wmem = 4096 65536 16777216" | sudo tee -a /etc/sysctl.conf

# 应用配置
sudo sysctl -p
```

## 7. 故障排除

### 常见问题

1. **Go环境问题**
   ```bash
   # 检查Go版本
   go version
   
   # 检查环境变量
   echo $PATH
   echo $GOPATH
   ```

2. **网络连接问题**
   ```bash
   # 测试币安API连接
   curl -s "https://fapi.binance.com/fapi/v1/ping"
   
   # 检查DNS解析
   nslookup fstream.binance.com
   ```

3. **权限问题**
   ```bash
   # 检查文件权限
   ls -la ~/binance-futures-data/
   
   # 修复权限
   chmod +x ~/binance-futures-data/build/binance-data-subscriber
   ```

4. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   
   # 检查进程内存使用
   ps aux | grep binance
   ```

### 调试模式运行

```bash
# 前台运行查看详细日志
cd ~/binance-futures-data
./build/binance-data-subscriber

# 或者使用Go直接运行
go run main.go
```

## 8. 安全建议

1. **使用非root用户运行**
2. **定期更新系统和依赖**
3. **监控系统资源使用**
4. **设置日志轮转**
5. **配置防火墙规则**

## 9. 备份和恢复

```bash
# 备份配置文件
tar -czf binance-data-backup.tar.gz ~/binance-futures-data/

# 恢复
tar -xzf binance-data-backup.tar.gz -C ~/
``` 