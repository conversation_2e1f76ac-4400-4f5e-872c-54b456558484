#!/bin/bash

# 币安永续合约数据订阅器 - Ubuntu 22.04 部署脚本

set -e  # 遇到错误立即退出

echo "=========================================="
echo "币安永续合约数据订阅器 - Ubuntu 部署脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "请不要使用root用户运行此脚本"
    exit 1
fi

# 检查Go是否安装
if ! command -v go &> /dev/null; then
    echo "正在安装Go语言环境..."
    
    # 下载Go
    cd /tmp
    wget -q https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
    
    # 安装Go（需要sudo权限）
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
    
    # 设置环境变量
    echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
    echo 'export GOPATH=$HOME/go' >> ~/.bashrc
    echo 'export GOBIN=$GOPATH/bin' >> ~/.bashrc
    
    # 立即生效
    export PATH=$PATH:/usr/local/go/bin
    export GOPATH=$HOME/go
    export GOBIN=$GOPATH/bin
    
    echo "Go语言环境安装完成"
else
    echo "Go语言环境已存在: $(go version)"
fi

# 创建项目目录
PROJECT_DIR="$HOME/binance-futures-data"
echo "创建项目目录: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# 检查网络连接
echo "检查网络连接..."
if ! ping -c 1 8.8.8.8 &> /dev/null; then
    echo "警告: 网络连接可能有问题"
fi

# 测试币安API连接
echo "测试币安API连接..."
if curl -s --connect-timeout 10 "https://fapi.binance.com/fapi/v1/ping" | grep -q "{}"; then
    echo "币安API连接正常"
else
    echo "警告: 无法连接到币安API，请检查网络或防火墙设置"
fi

echo "项目部署准备完成！"
echo "请将项目文件上传到: $PROJECT_DIR"
echo "然后运行: cd $PROJECT_DIR && ./start.sh" 