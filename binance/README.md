# 币安永续合约数据订阅器

这是一个用Go语言实现的币安永续合约交易数据和盘口数据订阅器，可以实时获取所有活跃交易对的数据。

## 功能特性

- 🚀 自动获取所有活跃的永续合约交易对
- 📊 实时订阅交易数据（aggTrade）
- 📈 实时订阅盘口数据（bookTicker）
- 🔄 支持多连接并发订阅，提高性能
- 📝 详细的日志记录
- 🛡️ 优雅的错误处理和连接管理
- ⚡ 支持优雅停止

## 项目结构

```
.
├── go.mod          # Go模块文件
├── main.go         # 主程序文件
└── README.md       # 项目说明文档
```

## 安装依赖

```bash
go mod tidy
```

## 运行程序

### 方式一：使用启动脚本（推荐）
```bash
./start.sh
```

### 方式二：使用Makefile
```bash
make run
```

### 方式三：直接运行
```bash
go run main.go
```

### 方式四：构建后运行
```bash
go build -o build/binance-data-subscriber .
./build/binance-data-subscriber
```

## 程序说明

### 主要组件

1. **BinanceClient**: 主要的客户端结构体，管理所有WebSocket连接
2. **GetAllSymbols**: 从币安API获取所有活跃的永续合约交易对
3. **SubscribeTradeData**: 订阅交易数据流
4. **SubscribeBookTicker**: 订阅盘口数据流

### 数据类型

#### 交易数据 (aggTrade)
- 交易对 (Symbol)
- 价格 (Price)
- 数量 (Quantity)
- 交易方向 (Buy/Sell)
- 交易时间 (TradeTime)

#### 盘口数据 (bookTicker)
- 交易对 (Symbol)
- 最佳买价和数量 (BestBid)
- 最佳卖价和数量 (BestAsk)

### 性能优化

- 每个WebSocket连接最多订阅200个数据流
- 使用goroutine并发处理多个连接
- 分批订阅避免单个连接过载

## 使用示例

程序启动后会自动：

1. 获取所有活跃的永续合约交易对
2. 建立多个WebSocket连接
3. 开始接收实时数据
4. 在控制台输出数据日志

### 示例输出

```
INFO[2024-01-01T12:00:00Z] 正在获取所有永续合约交易对...
INFO[2024-01-01T12:00:01Z] 成功获取 200 个活跃交易对
INFO[2024-01-01T12:00:01Z] 开始订阅交易数据...
INFO[2024-01-01T12:00:01Z] 开始订阅盘口数据...
INFO[2024-01-01T12:00:02Z] 连接 trade_batch_0 建立成功
INFO[2024-01-01T12:00:02Z] 连接 ticker_batch_0 建立成功
INFO[2024-01-01T12:00:03Z] 交易数据 - 交易对: BTCUSDT, 价格: 45000.00, 数量: 0.001, 方向: BUY, 时间: 1704110403000
INFO[2024-01-01T12:00:03Z] 盘口数据 - 交易对: ETHUSDT, 最佳买价: 2500.00(1.5), 最佳卖价: 2500.50(2.0)
```

## 停止程序

使用 `Ctrl+C` 发送中断信号，程序会优雅地关闭所有连接并退出。

## 注意事项

1. 确保网络连接稳定
2. 币安API有连接限制，请合理使用
3. 程序会产生大量日志输出，建议在生产环境中调整日志级别
4. 可以根据需要修改数据处理逻辑

## 扩展功能

可以在此基础上添加：

- 数据存储（数据库、文件等）
- 数据分析和处理
- 告警机制
- Web界面展示
- 更多数据流类型（深度数据、K线数据等）

## 依赖包

- `github.com/gorilla/websocket`: WebSocket客户端
- `github.com/sirupsen/logrus`: 日志库
- `github.com/tidwall/gjson`: JSON解析库 