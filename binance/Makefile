# 项目名称
PROJECT_NAME := binance-futures-data
BINARY_NAME := binance-data-subscriber

# Go相关变量
GO := go
GOMOD := $(GO) mod
GOBUILD := $(GO) build
GORUN := $(GO) run
GOCLEAN := $(GO) clean
GOTEST := $(GO) test
GOGET := $(GO) get

# 构建目录
BUILD_DIR := build

.PHONY: all build run clean test deps help

# 默认目标
all: deps build

# 安装依赖
deps:
	@echo "正在安装依赖..."
	$(GOMOD) tidy
	$(GOMOD) download

# 构建项目
build: deps
	@echo "正在构建项目..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -o $(BUILD_DIR)/$(BINARY_NAME) .

# 运行项目
run: deps
	@echo "正在运行项目..."
	$(GORUN) .

# 清理构建文件
clean:
	@echo "正在清理构建文件..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)

# 运行测试
test:
	@echo "正在运行测试..."
	$(GOTEST) -v ./...

# 格式化代码
fmt:
	@echo "正在格式化代码..."
	$(GO) fmt ./...

# 检查代码
vet:
	@echo "正在检查代码..."
	$(GO) vet ./...

# 安装到系统
install: build
	@echo "正在安装到系统..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/

# 显示帮助信息
help:
	@echo "可用的命令:"
	@echo "  make deps     - 安装依赖"
	@echo "  make build    - 构建项目"
	@echo "  make run      - 运行项目"
	@echo "  make clean    - 清理构建文件"
	@echo "  make test     - 运行测试"
	@echo "  make fmt      - 格式化代码"
	@echo "  make vet      - 检查代码"
	@echo "  make install  - 安装到系统"
	@echo "  make help     - 显示此帮助信息" 