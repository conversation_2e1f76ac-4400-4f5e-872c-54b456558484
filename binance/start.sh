#!/bin/bash

# 币安永续合约数据订阅器管理脚本

PROGRAM_NAME="binance-subscriber"
BUILD_DIR="."
EXECUTABLE="$BUILD_DIR/$PROGRAM_NAME"
LOG_DIR="logs"
PID_FILE="$LOG_DIR/$PROGRAM_NAME.pid"
LOG_FILE="$LOG_DIR/$PROGRAM_NAME.log"

# 显示使用说明
show_usage() {
    echo "==================================="
    echo "币安永续合约数据订阅器管理脚本"
echo "==================================="
    echo "使用方法: $0 {start|stop|restart|status|logs|build}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动程序"
    echo "  stop    - 停止程序"
    echo "  restart - 重启程序"
    echo "  status  - 查看程序状态"
    echo "  logs    - 查看程序日志"
    echo "  build   - 构建程序"
echo "==================================="
}

# 检查Go环境
check_go() {
if ! command -v go &> /dev/null; then
        echo "❌ 错误: 未找到Go环境，请先安装Go"
    exit 1
fi
}

# 检查网络连接
check_network() {
    echo "🌐 检查网络连接..."
if ! ping -c 1 google.com &> /dev/null; then
        echo "⚠️  警告: 网络连接可能有问题，请确保网络正常"
fi
}

# 构建程序
build_program() {
    echo "🔨 构建程序..."
    check_go
    
    # 创建构建目录和日志目录
    mkdir -p $BUILD_DIR
    mkdir -p $LOG_DIR

# 安装依赖
    echo "📦 安装项目依赖..."
go mod tidy

# 构建项目
    if ! go build -o $EXECUTABLE .; then
        echo "❌ 错误: 项目构建失败"
    exit 1
fi

    echo "✅ 项目构建成功！"
}

# 启动程序
start_program() {
    # 确保日志目录存在
    mkdir -p $LOG_DIR
    
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️  程序已在运行 (PID: $PID)"
            return 1
        else
            rm -f $PID_FILE
        fi
    fi
    
    echo "==================================="
    echo "币安永续合约数据订阅器"
    echo "==================================="
    
    check_network
    
    # 如果可执行文件不存在，先构建
    if [ ! -f $EXECUTABLE ]; then
        build_program
    fi
    
    echo "🚀 启动币安数据订阅器..."
    echo "📋 日志文件: $LOG_FILE"
    echo "🆔 PID文件: $PID_FILE"
echo "按 Ctrl+C 停止程序"
echo "==================================="

    # 后台启动程序
    nohup $EXECUTABLE > $LOG_FILE 2>&1 &
    echo $! > $PID_FILE
    
    echo "✅ 程序已启动 (PID: $(cat $PID_FILE))"
}

# 停止程序
stop_program() {
    local stopped=false
    
    # 方法1：通过PID文件停止
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "🛑 通过PID文件停止程序 (PID: $PID)..."
            
            # 发送SIGTERM信号
            if kill -TERM $PID 2>/dev/null; then
                # 等待程序停止
                for i in {1..10}; do
                    if ! ps -p $PID > /dev/null 2>&1; then
                        stopped=true
                        break
                    fi
                    sleep 1
                done
                
                # 如果程序仍在运行，强制停止
                if [ "$stopped" = false ] && ps -p $PID > /dev/null 2>&1; then
                    echo "⚡ 强制停止程序..."
                    kill -KILL $PID 2>/dev/null && stopped=true
                fi
            fi
        fi
        rm -f $PID_FILE
    fi
    
    # 方法2：如果PID文件方法失败，按进程名查找并停止
    if [ "$stopped" = false ]; then
        echo "🔍 按进程名查找并停止程序..."
        local pids=$(pgrep -f "$PROGRAM_NAME" 2>/dev/null)
        if [ -n "$pids" ]; then
            for pid in $pids; do
                echo "🛑 停止进程 (PID: $pid)..."
                if kill -TERM $pid 2>/dev/null; then
                    sleep 2
                    if ps -p $pid > /dev/null 2>&1; then
                        kill -KILL $pid 2>/dev/null
                    fi
                    stopped=true
                fi
            done
        fi
    fi
    
    # 方法3：最后尝试killall
    if [ "$stopped" = false ]; then
        echo "🔍 尝试killall停止程序..."
        if command -v killall &> /dev/null; then
            killall "$PROGRAM_NAME" 2>/dev/null && stopped=true
        fi
    fi
    
    if [ "$stopped" = true ]; then
        echo "✅ 程序已停止"
    else
        echo "⚠️  程序未运行或停止失败"
        return 1
    fi
}

# 查看程序状态
show_status() {
    if [ ! -f $PID_FILE ]; then
        echo "📊 程序状态: 未运行"
        return 1
    fi
    
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "📊 程序状态: 运行中 (PID: $PID)"
        echo "📋 日志文件: $LOG_FILE"
        echo "🆔 PID文件: $PID_FILE"
        
        # 显示进程信息
        echo "📈 进程信息:"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
    else
        echo "📊 程序状态: 未运行 (PID文件存在但进程不存在)"
        rm -f $PID_FILE
        return 1
    fi
}

# 查看日志
show_logs() {
    if [ ! -f $LOG_FILE ]; then
        echo "⚠️  日志文件不存在: $LOG_FILE"
        return 1
    fi
    
    echo "📋 显示最近50行日志:"
    echo "==================================="
    tail -50 $LOG_FILE
    echo "==================================="
    echo "💡 实时查看日志: tail -f $LOG_FILE"
}

# 重启程序
restart_program() {
    echo "🔄 重启程序..."
    stop_program
    sleep 2
    start_program
}

# 主逻辑
case "$1" in
    start)
        start_program
        ;;
    stop)
        stop_program
        ;;
    restart)
        restart_program
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    build)
        build_program
        ;;
    *)
        show_usage
        exit 1
        ;;
esac 