package main

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/tidwall/gjson"
)

// BybitTradeData 交易数据结构
type BybitTradeData struct {
	Symbol    string `json:"symbol"`
	Price     string `json:"price"`
	Quantity  string `json:"quantity"`
	Side      string `json:"side"`
	Timestamp int64  `json:"timestamp"`
	TradeID   string `json:"trade_id"`
}

// BybitOrderbookData 盘口数据结构
type BybitOrderbookData struct {
	Symbol       string `json:"symbol"`
	BestBidPrice string `json:"best_bid_price"`
	BestBidSize  string `json:"best_bid_size"`
	BestAskPrice string `json:"best_ask_price"`
	BestAskSize  string `json:"best_ask_size"`
	Timestamp    int64  `json:"timestamp"`
	UpdateID     int64  `json:"update_id"`
}

// DataHandler 数据处理器接口
type DataHandler interface {
	HandleTradeMessage(message []byte) error
	HandleOrderbookMessage(message []byte) error
	GetTradeChannels(symbols []string) []string
	GetOrderbookChannels(symbols []string) []string
}

// BybitDataHandler Bybit数据处理器
type BybitDataHandler struct {
	tradeCount     int64
	orderbookCount int64
	lastLogTime    time.Time
}

// NewBybitDataHandler 创建新的Bybit数据处理器
func NewBybitDataHandler() *BybitDataHandler {
	return &BybitDataHandler{
		lastLogTime: time.Now(),
	}
}

// HandleTradeMessage 处理交易数据
func (h *BybitDataHandler) HandleTradeMessage(message []byte) error {
	h.tradeCount++

	// 解析消息
	topic := gjson.GetBytes(message, "topic").String()
	dataArray := gjson.GetBytes(message, "data").Array()

	if len(dataArray) == 0 {
		return nil
	}

	// 从topic中提取symbol
	symbol := extractSymbolFromTopic(topic)

	for _, data := range dataArray {
		_ = BybitTradeData{
			Symbol:    symbol,
			Price:     data.Get("p").String(),
			Quantity:  data.Get("v").String(),
			Side:      data.Get("S").String(),
			Timestamp: data.Get("T").Int(),
			TradeID:   data.Get("i").String(),
		}

		// 记录统计日志
		h.logStatistics()
	}

	return nil
}

// HandleOrderbookMessage 处理盘口数据
func (h *BybitDataHandler) HandleOrderbookMessage(message []byte) error {
	h.orderbookCount++

	// 解析消息
	topic := gjson.GetBytes(message, "topic").String()
	data := gjson.GetBytes(message, "data")

	// 从topic中提取symbol
	symbol := extractSymbolFromTopic(topic)

	// 获取最优买卖价
	bids := data.Get("b").Array()
	asks := data.Get("a").Array()

	orderbook := BybitOrderbookData{
		Symbol:    symbol,
		Timestamp: gjson.GetBytes(message, "ts").Int(),
		UpdateID:  data.Get("u").Int(),
	}

	if len(bids) > 0 {
		orderbook.BestBidPrice = bids[0].Get("0").String()
		orderbook.BestBidSize = bids[0].Get("1").String()
	}

	if len(asks) > 0 {
		orderbook.BestAskPrice = asks[0].Get("0").String()
		orderbook.BestAskSize = asks[0].Get("1").String()
	}

	// 记录统计日志
	h.logStatistics()

	return nil
}

// GetTradeChannels 获取交易数据订阅频道
func (h *BybitDataHandler) GetTradeChannels(symbols []string) []string {
	channels := make([]string, len(symbols))
	for i, symbol := range symbols {
		channels[i] = fmt.Sprintf("publicTrade.%s", symbol)
	}
	return channels
}

// GetOrderbookChannels 获取盘口数据订阅频道
func (h *BybitDataHandler) GetOrderbookChannels(symbols []string) []string {
	channels := make([]string, len(symbols))
	for i, symbol := range symbols {
		channels[i] = fmt.Sprintf("orderbook.1.%s", symbol)
	}
	return channels
}

// logStatistics 记录统计信息 - 移除详细统计，统一在全局统计中显示
func (h *BybitDataHandler) logStatistics() {
	// 不再输出详细的数据统计，统一在main.go的全局统计中显示
}

// extractSymbolFromTopic 从topic中提取symbol
func extractSymbolFromTopic(topic string) string {
	// topic格式: "publicTrade.BTCUSDT" 或 "orderbook.1.BTCUSDT"
	if len(topic) == 0 {
		return ""
	}

	// 找到最后一个点的位置
	lastDot := -1
	for i := len(topic) - 1; i >= 0; i-- {
		if topic[i] == '.' {
			lastDot = i
			break
		}
	}

	if lastDot == -1 || lastDot == len(topic)-1 {
		return ""
	}

	return topic[lastDot+1:]
}

// BybitSymbol Bybit交易对信息
type BybitSymbol struct {
	Symbol string `json:"symbol"`
	Status string `json:"status"`
}

// BybitInstrumentsResponse Bybit交易对API响应
type BybitInstrumentsResponse struct {
	RetCode int    `json:"retCode"`
	RetMsg  string `json:"retMsg"`
	Result  struct {
		Category string        `json:"category"`
		List     []BybitSymbol `json:"list"`
	} `json:"result"`
}

// GetActiveSymbols 获取活跃的永续合约交易对
func GetActiveSymbols(restAPIURL string) ([]string, error) {
	url := fmt.Sprintf("%s/v5/market/instruments-info?category=linear", restAPIURL)

	resp, err := makeHTTPRequest(url)
	if err != nil {
		return nil, fmt.Errorf("请求交易对信息失败: %v", err)
	}
	defer resp.Body.Close()

	var response BybitInstrumentsResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if response.RetCode != 0 {
		return nil, fmt.Errorf("API返回错误: %s", response.RetMsg)
	}

	var activeSymbols []string
	for _, symbol := range response.Result.List {
		if symbol.Status == "Trading" {
			activeSymbols = append(activeSymbols, symbol.Symbol)
		}
	}

	log.Printf("🔍 获取到 %d 个活跃的永续合约交易对", len(activeSymbols))
	return activeSymbols, nil
}
