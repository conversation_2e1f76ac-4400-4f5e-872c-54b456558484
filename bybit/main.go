package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	state          ConnectionState
	lastPingTime   time.Time
	lastPongTime   time.Time
	reconnectCount int
	mutex          sync.RWMutex
}

// makeHTTPRequest 发送HTTP请求
func makeHTTPRequest(url string) (*http.Response, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	return client.Get(url)
}

// BybitClient Bybit客户端
type BybitClient struct {
	config      *Config
	dataHandler DataHandler
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 数据管理器
	tradeManager     *TradeDataManager
	orderbookManager *OrderbookDataManager
	tickerManager    *TickerDataManager
	fundingManager   *FundingDataManager

	// Kafka生产者
	kafkaProducer *KafkaProducer

	// 全局统计
	totalTradeCount     int64
	totalOrderbookCount int64
	totalTickerCount    int64
	totalFundingCount   int64
	startTime           time.Time
}

// TradeDataManager 交易数据管理器
type TradeDataManager struct {
	client      *BybitClient
	connections map[string]*websocket.Conn
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type OrderbookDataManager struct {
	client         *BybitClient
	connections    map[string]*websocket.Conn
	connMutex      sync.RWMutex
	orderbookCount int64
}

// TickerDataManager Ticker数据管理器（包含资金费率）
type TickerDataManager struct {
	client      *BybitClient
	connections map[string]*websocket.Conn
	connMutex   sync.RWMutex
	tickerCount int64
}

// FundingDataManager 资金费率数据管理器
type FundingDataManager struct {
	client       *BybitClient
	fundingCount int64
}

// NewBybitClient 创建Bybit客户端
func NewBybitClient(config *Config, dataHandler DataHandler) *BybitClient {
	ctx, cancel := context.WithCancel(context.Background())

	client := &BybitClient{
		config:      config,
		dataHandler: dataHandler,
		ctx:         ctx,
		cancel:      cancel,
		startTime:   time.Now(),
	}

	client.tradeManager = &TradeDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.orderbookManager = &OrderbookDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.tickerManager = &TickerDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.fundingManager = &FundingDataManager{
		client: client,
	}

	// 初始化Kafka生产者
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 设置logrus输出到同一个日志文件
	if logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666); err == nil {
		logger.SetOutput(logFile)
	}

	kafkaProducer, err := NewKafkaProducer(&config.Kafka, logger)
	if err != nil {
		log.Printf("❌ [Bybit] 初始化Kafka生产者失败: %v", err)
	} else if kafkaProducer != nil {
		client.kafkaProducer = kafkaProducer
		log.Printf("✅ [Bybit] Kafka生产者初始化成功")
	}

	return client
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetAllSymbols 获取所有活跃交易对
func (c *BybitClient) GetAllSymbols() error {
	symbols, err := getActiveSymbols(c.config)
	if err != nil {
		return err
	}
	c.symbols = symbols
	return nil
}

// SubscribeData 订阅数据
func (c *BybitClient) SubscribeData() error {
	log.Printf("🚀 开始分批订阅交易数据和盘口数据...")

	batchSize := 15 // Bybit建议每批15个交易对
	totalBatches := (len(c.symbols) + batchSize - 1) / batchSize

	log.Printf("📊 分批订阅策略 - 总交易对: %d, 批次大小: %d, 总批次: %d",
		len(c.symbols), batchSize, totalBatches)

	batchCount := 0

	for i := 0; i < len(c.symbols); i += batchSize {
		end := i + batchSize
		if end > len(c.symbols) {
			end = len(c.symbols)
		}

		batch := c.symbols[i:end]
		batchCount++

		log.Printf("📡 处理批次 %d/%d - 交易对数量: %d", batchCount, totalBatches, len(batch))

		// 为每批创建三个连接：交易数据、盘口数据、Ticker数据（包含资金费率）
		tradeConnName := fmt.Sprintf("trade_batch_%d", batchCount)
		orderbookConnName := fmt.Sprintf("orderbook_batch_%d", batchCount)
		tickerConnName := fmt.Sprintf("ticker_batch_%d", batchCount)

		// 订阅交易数据
		log.Printf("🔗 启动交易数据连接: %s", tradeConnName)
		c.wg.Add(1)
		go c.tradeManager.subscribeTradeData(tradeConnName, batch)

		// 等待一点时间再订阅盘口数据
		time.Sleep(200 * time.Millisecond)

		// 订阅盘口数据
		log.Printf("🔗 启动盘口数据连接: %s", orderbookConnName)
		c.wg.Add(1)
		go c.orderbookManager.subscribeOrderbookData(orderbookConnName, batch)

		// 等待一点时间再订阅Ticker数据
		time.Sleep(200 * time.Millisecond)

		// 订阅Ticker数据（包含资金费率）
		log.Printf("🔗 启动Ticker数据连接: %s", tickerConnName)
		c.wg.Add(1)
		go c.tickerManager.subscribeTickerData(tickerConnName, batch)

		// 批次间延迟，避免同时创建太多连接
		if batchCount < totalBatches {
			log.Printf("⏰ 批次 %d 完成，等待 1 秒后继续下一批次...", batchCount)
			time.Sleep(1 * time.Second)
		}
	}

	log.Printf("✅ 所有分批订阅已启动，共创建 %d 批连接 (%d 个连接)", totalBatches, totalBatches*3)

	// 注意：资金费率数据已通过ticker数据获取，无需额外的REST API调用

	// 启动全局统计
	c.wg.Add(1)
	go c.printGlobalStatistics()

	// 启动连接监控
	c.wg.Add(1)
	go c.monitorConnections()

	return nil
}

// subscribeTradeData 订阅交易数据
func (tm *TradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.client.wg.Done()

	// 构建交易数据订阅参数
	args := make([]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = fmt.Sprintf("publicTrade.%s", symbol)
	}

	tm.subscribeWithRetry(connName, args, tm.handleTradeMessage)
}

// subscribeOrderbookData 订阅盘口数据
func (om *OrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.client.wg.Done()

	// 构建盘口数据订阅参数
	args := make([]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = fmt.Sprintf("orderbook.1.%s", symbol)
	}

	om.subscribeWithRetry(connName, args, om.handleOrderbookMessage)
}

// subscribeTickerData 订阅Ticker数据（包含资金费率）
func (tm *TickerDataManager) subscribeTickerData(connName string, symbols []string) {
	defer tm.client.wg.Done()

	// 构建Ticker数据订阅参数
	args := make([]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = fmt.Sprintf("tickers.%s", symbol)
	}

	tm.subscribeWithRetry(connName, args, tm.handleTickerMessage)
}

// subscribeWithRetry 通用订阅方法（带重连机制）
func (tm *TradeDataManager) subscribeWithRetry(connName string, args []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := tm.client.config.GetReconnectDelay()

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 交易数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if tm.connectAndListen(connName, args, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 交易数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 交易数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-tm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 盘口数据版本
func (om *OrderbookDataManager) subscribeWithRetry(connName string, args []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := om.client.config.GetReconnectDelay()

	for {
		select {
		case <-om.client.ctx.Done():
			log.Printf("🛑 盘口数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if om.connectAndListen(connName, args, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 盘口数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 盘口数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-om.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry Ticker数据版本
func (tm *TickerDataManager) subscribeWithRetry(connName string, args []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := tm.client.config.GetReconnectDelay()

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 Ticker数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if tm.connectAndListen(connName, args, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ Ticker数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ Ticker数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-tm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// connectAndListen 交易数据连接和监听
func (tm *TradeDataManager) connectAndListen(connName string, args []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接交易数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(tm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 交易数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	tm.connMutex.Lock()
	tm.connections[connName] = conn
	tm.connMutex.Unlock()

	defer func() {
		tm.connMutex.Lock()
		delete(tm.connections, connName)
		tm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 交易数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 交易数据连接 %s 建立成功", connName)

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送交易数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 交易数据订阅消息已发送 %s", connName)

	// 启动心跳
	go tm.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-tm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 交易数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// connectAndListen 盘口数据连接和监听
func (om *OrderbookDataManager) connectAndListen(connName string, args []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接盘口数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(om.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 盘口数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	om.connMutex.Lock()
	om.connections[connName] = conn
	om.connMutex.Unlock()

	defer func() {
		om.connMutex.Lock()
		delete(om.connections, connName)
		om.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 盘口数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 盘口数据连接 %s 建立成功", connName)

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送盘口数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 盘口数据订阅消息已发送 %s", connName)

	// 启动心跳
	go om.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-om.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 盘口数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// connectAndListen Ticker数据连接和监听
func (tm *TickerDataManager) connectAndListen(connName string, args []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接Ticker数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(tm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ Ticker数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	tm.connMutex.Lock()
	tm.connections[connName] = conn
	tm.connMutex.Unlock()

	defer func() {
		tm.connMutex.Lock()
		delete(tm.connections, connName)
		tm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 Ticker数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ Ticker数据连接 %s 建立成功", connName)

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送Ticker数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 Ticker数据订阅消息已发送 %s", connName)

	// 启动心跳
	go tm.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-tm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ Ticker数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// startPing Ticker数据心跳
func (tm *TickerDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(tm.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(tm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Bybit] Ticker数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Bybit] Ticker数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Bybit] Ticker数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-tm.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("bybit-ticker-ping")); err != nil {
				log.Printf("💔 [Bybit] Ticker数据WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 同时发送应用层ping（双重保障）
			pingMsg := map[string]interface{}{
				"op": "ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Bybit] Ticker数据应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Bybit] Ticker数据连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := tm.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Bybit] Ticker数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Bybit] Ticker数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// startPing 交易数据心跳
func (tm *TradeDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(tm.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(tm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Bybit] 交易数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Bybit] 交易数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Bybit] 交易数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-tm.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("bybit-trade-ping")); err != nil {
				log.Printf("💔 [Bybit] 交易数据WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 同时发送应用层ping（双重保障）
			pingMsg := map[string]interface{}{
				"op": "ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Bybit] 交易数据应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Bybit] 交易数据连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := tm.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Bybit] 交易数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Bybit] 交易数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// startPing 盘口数据心跳
func (om *OrderbookDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(om.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(om.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Bybit] 盘口数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Bybit] 盘口数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Bybit] 盘口数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-om.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("bybit-orderbook-ping")); err != nil {
				log.Printf("💔 [Bybit] 盘口数据WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 同时发送应用层ping（双重保障）
			pingMsg := map[string]interface{}{
				"op": "ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Bybit] 盘口数据应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Bybit] 盘口数据连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := om.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Bybit] 盘口数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Bybit] 盘口数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// handleTradeMessage 处理交易数据
func (tm *TradeDataManager) handleTradeMessage(message []byte) {
	// 检查是否是心跳响应
	op := gjson.GetBytes(message, "op").String()
	if op == "pong" {
		return
	}

	// 检查是否是订阅确认
	if op == "subscribe" {
		success := gjson.GetBytes(message, "success").Bool()
		if success {
			log.Printf("✅ 交易数据订阅确认成功")
		} else {
			log.Printf("❌ 交易数据订阅失败: %s", string(message))
		}
		return
	}

	// 获取主题信息
	topic := gjson.GetBytes(message, "topic").String()

	if strings.Contains(topic, "publicTrade") {
		// 检查是否有实际的交易数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() && data.IsArray() && len(data.Array()) > 0 {
			atomic.AddInt64(&tm.tradeCount, 1)
			atomic.AddInt64(&tm.client.totalTradeCount, 1)

			// 发送到Kafka
			if tm.client.kafkaProducer != nil {
				// 提取交易对信息
				symbol := gjson.GetBytes(message, "topic").String()
				if strings.Contains(symbol, ".") {
					parts := strings.Split(symbol, ".")
					if len(parts) >= 2 {
						symbol = parts[1] // 提取交易对名称
					}
				}
				tm.client.kafkaProducer.SendTradeData(symbol, data.Value())
			}

			// 调用数据处理器
			tm.client.dataHandler.HandleTradeMessage(message)
		}
	}
}

// handleOrderbookMessage 处理盘口数据
func (om *OrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查是否是心跳响应
	op := gjson.GetBytes(message, "op").String()
	if op == "pong" {
		return
	}

	// 检查是否是订阅确认
	if op == "subscribe" {
		success := gjson.GetBytes(message, "success").Bool()
		if success {
			log.Printf("✅ 盘口数据订阅确认成功")
		} else {
			log.Printf("❌ 盘口数据订阅失败: %s", string(message))
		}
		return
	}

	// 获取主题信息
	topic := gjson.GetBytes(message, "topic").String()

	if strings.Contains(topic, "orderbook") {
		// 检查是否有实际的盘口数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() {
			atomic.AddInt64(&om.orderbookCount, 1)
			atomic.AddInt64(&om.client.totalOrderbookCount, 1)

			// 发送到Kafka
			if om.client.kafkaProducer != nil {
				// 提取交易对信息
				symbol := gjson.GetBytes(message, "topic").String()
				if strings.Contains(symbol, ".") {
					parts := strings.Split(symbol, ".")
					if len(parts) >= 2 {
						symbol = parts[1] // 提取交易对名称
					}
				}
				om.client.kafkaProducer.SendOrderbookData(symbol, data.Value())
			}

			// 调用数据处理器
			om.client.dataHandler.HandleOrderbookMessage(message)
		}
	}
}

// handleTickerMessage 处理Ticker数据（包含资金费率）
func (tm *TickerDataManager) handleTickerMessage(message []byte) {
	// 检查是否是心跳响应
	op := gjson.GetBytes(message, "op").String()
	if op == "pong" {
		return
	}

	// 检查是否是订阅确认
	if op == "subscribe" {
		success := gjson.GetBytes(message, "success").Bool()
		if success {
			log.Printf("✅ Ticker数据订阅确认成功")
		} else {
			log.Printf("❌ Ticker数据订阅失败: %s", string(message))
		}
		return
	}

	// 获取主题信息
	topic := gjson.GetBytes(message, "topic").String()

	if strings.Contains(topic, "tickers") {
		// 检查是否有实际的ticker数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() {
			atomic.AddInt64(&tm.tickerCount, 1)
			atomic.AddInt64(&tm.client.totalTickerCount, 1)

			// 提取交易对信息
			symbol := gjson.GetBytes(message, "topic").String()
			if strings.Contains(symbol, ".") {
				parts := strings.Split(symbol, ".")
				if len(parts) >= 2 {
					symbol = parts[1] // 提取交易对名称
				}
			}

			// 根据Bybit官方文档，ticker数据包含完整的资金费率信息
			// 构建完整的资金费率数据（包含所有相关字段）
			fundingData := map[string]interface{}{
				"symbol":            symbol,
				"fundingRate":       data.Get("fundingRate").String(),
				"nextFundingTime":   data.Get("nextFundingTime").String(),
				"markPrice":         data.Get("markPrice").String(),
				"indexPrice":        data.Get("indexPrice").String(),
				"bid1Price":         data.Get("bid1Price").String(),
				"bid1Size":          data.Get("bid1Size").String(),
				"ask1Price":         data.Get("ask1Price").String(),
				"ask1Size":          data.Get("ask1Size").String(),
				"lastPrice":         data.Get("lastPrice").String(),
				"tickDirection":     data.Get("tickDirection").String(),
				"price24hPcnt":      data.Get("price24hPcnt").String(),
				"prevPrice24h":      data.Get("prevPrice24h").String(),
				"highPrice24h":      data.Get("highPrice24h").String(),
				"lowPrice24h":       data.Get("lowPrice24h").String(),
				"openInterest":      data.Get("openInterest").String(),
				"openInterestValue": data.Get("openInterestValue").String(),
				"volume24h":         data.Get("volume24h").String(),
				"turnover24h":       data.Get("turnover24h").String(),
				"timestamp":         time.Now().UnixMilli(),
			}

			// 发送到Kafka的funding topic
			if tm.client.kafkaProducer != nil {
				tm.client.kafkaProducer.SendFundingData(symbol, fundingData)
				atomic.AddInt64(&tm.client.totalFundingCount, 1)
			}

			// 调用数据处理器
			tm.client.dataHandler.HandleTradeMessage(message)
		}
	}
}

// printGlobalStatistics 打印全局统计
func (c *BybitClient) printGlobalStatistics() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			runtime := time.Since(c.startTime)
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalTicker := atomic.LoadInt64(&c.totalTickerCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)

			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			c.orderbookManager.connMutex.RUnlock()

			c.tickerManager.connMutex.RLock()
			tickerConnections := len(c.tickerManager.connections)
			c.tickerManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 14) / 15 // 每批15个交易对

			log.Printf("📊 [Bybit] 全局统计 - 运行时间: %v, 交易连接: %d/%d, 盘口连接: %d/%d, Ticker连接: %d/%d, 交易消息: %d, 盘口消息: %d, Ticker消息: %d, 资金费率消息: %d, 总消息: %d",
				runtime.Round(time.Second),
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				tickerConnections, expectedConnections,
				totalTrade,
				totalOrderbook,
				totalTicker,
				totalFunding,
				totalTrade+totalOrderbook+totalTicker+totalFunding,
			)

			// 数据流速率统计（每秒消息数）
			if runtime.Seconds() > 0 {
				tradeRate := float64(totalTrade) / runtime.Seconds()
				orderbookRate := float64(totalOrderbook) / runtime.Seconds()
				tickerRate := float64(totalTicker) / runtime.Seconds()
				log.Printf("📈 [Bybit] 数据流速率 - 交易: %.1f msg/s, 盘口: %.1f msg/s, Ticker: %.1f msg/s",
					tradeRate, orderbookRate, tickerRate)
			}

			// WebSocket连接健康状态
			totalConnections := tradeConnections + orderbookConnections + tickerConnections
			expectedTotal := expectedConnections * 3
			if totalConnections == expectedTotal {
				log.Printf("✅ [Bybit] 所有WebSocket连接健康")
			} else if totalConnections >= expectedTotal*2/3 {
				log.Printf("⚠️ [Bybit] 部分WebSocket连接异常")
			} else {
				log.Printf("❌ [Bybit] 大量WebSocket连接异常")
			}
		}
	}
}

// monitorConnections 监控连接状态
func (c *BybitClient) monitorConnections() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			tradeConnNames := make([]string, 0, len(c.tradeManager.connections))
			for name := range c.tradeManager.connections {
				tradeConnNames = append(tradeConnNames, name)
			}
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			orderbookConnNames := make([]string, 0, len(c.orderbookManager.connections))
			for name := range c.orderbookManager.connections {
				orderbookConnNames = append(orderbookConnNames, name)
			}
			c.orderbookManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 14) / 15 // 每批15个交易对

			log.Printf("🔍 [Bybit] 连接监控 - 交易连接: %d/%d, 盘口连接: %d/%d, 交易对数: %d, 批次大小: 15",
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				len(c.symbols))

			// 详细连接状态
			log.Printf("🔗 [Bybit] 连接详情 - 交易连接: %v", tradeConnNames)
			log.Printf("🔗 [Bybit] 连接详情 - 盘口连接: %v", orderbookConnNames)

			// 检查连接健康状态
			if tradeConnections < expectedConnections/2 {
				log.Printf("⚠️ [Bybit] 交易数据连接数异常：当前 %d，预期 %d", tradeConnections, expectedConnections)
			}
			if orderbookConnections < expectedConnections/2 {
				log.Printf("⚠️ [Bybit] 盘口数据连接数异常：当前 %d，预期 %d", orderbookConnections, expectedConnections)
			}

			// 数据流健康检查
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalConnections := tradeConnections + orderbookConnections

			if totalConnections > 0 && totalTrade == 0 && totalOrderbook == 0 {
				log.Printf("⚠️ [Bybit] 数据流异常：有连接但没有数据")
			}

			// 连接效率检查
			if totalConnections > 0 {
				avgTradePerConn := float64(totalTrade) / float64(tradeConnections)
				avgOrderbookPerConn := float64(totalOrderbook) / float64(orderbookConnections)
				log.Printf("📊 [Bybit] 连接效率 - 平均交易消息/连接: %.1f, 平均盘口消息/连接: %.1f",
					avgTradePerConn, avgOrderbookPerConn)
			}
		}
	}
}

// Stop 停止客户端
func (c *BybitClient) Stop() {
	log.Printf("🛑 正在停止Bybit客户端...")
	c.cancel()

	// 关闭所有交易数据连接
	c.tradeManager.connMutex.Lock()
	for name, conn := range c.tradeManager.connections {
		log.Printf("🔌 关闭交易数据连接: %s", name)
		conn.Close()
	}
	c.tradeManager.connMutex.Unlock()

	// 关闭所有盘口数据连接
	c.orderbookManager.connMutex.Lock()
	for name, conn := range c.orderbookManager.connections {
		log.Printf("🔌 关闭盘口数据连接: %s", name)
		conn.Close()
	}
	c.orderbookManager.connMutex.Unlock()

	// 关闭Kafka生产者
	if c.kafkaProducer != nil {
		c.kafkaProducer.Close()
	}

	// 等待所有goroutine结束
	c.wg.Wait()

	// 打印最终统计
	totalTrade := atomic.LoadInt64(&c.totalTradeCount)
	totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)

	log.Printf("✅ Bybit客户端已停止 - 总交易数据: %d 条, 总盘口数据: %d 条", totalTrade, totalOrderbook)
}

// getActiveSymbols 获取活跃的永续合约交易对
func getActiveSymbols(config *Config) ([]string, error) {
	url := fmt.Sprintf("%s/v5/market/instruments-info?category=linear", config.RestAPIURL)

	resp, err := makeHTTPRequest(url)
	if err != nil {
		return nil, fmt.Errorf("请求交易对列表失败: %v", err)
	}
	defer resp.Body.Close()

	var result struct {
		RetCode int `json:"retCode"`
		Result  struct {
			List []struct {
				Symbol string `json:"symbol"`
				Status string `json:"status"`
			} `json:"list"`
		} `json:"result"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if result.RetCode != 0 {
		return nil, fmt.Errorf("API返回错误代码: %d", result.RetCode)
	}

	var symbols []string
	for _, instrument := range result.Result.List {
		if instrument.Status == "Trading" {
			symbols = append(symbols, instrument.Symbol)
		}
	}

	log.Printf("📊 获取到 %d 个活跃的永续合约交易对", len(symbols))
	return symbols, nil
}

// setupLogging 设置日志
func setupLogging(config *Config) error {
	// 创建日志目录
	logDir := filepath.Dir(config.LogFile)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开日志文件
	logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 设置日志输出到文件
	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	return nil
}

// startFundingDataCollection 启动资金费率数据收集
func (fm *FundingDataManager) startFundingDataCollection() {
	defer fm.client.wg.Done()

	// 每5分钟获取一次资金费率数据
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	// 立即执行一次
	fm.fetchFundingRates()

	for {
		select {
		case <-fm.client.ctx.Done():
			log.Printf("🛑 资金费率数据收集收到停止信号")
			return
		case <-ticker.C:
			fm.fetchFundingRates()
		}
	}
}

// fetchFundingRates 获取资金费率数据
func (fm *FundingDataManager) fetchFundingRates() {
	log.Printf("📊 [Bybit] 开始获取资金费率数据...")

	// 分批获取，每批50个交易对
	batchSize := 50
	totalBatches := (len(fm.client.symbols) + batchSize - 1) / batchSize

	for i := 0; i < len(fm.client.symbols); i += batchSize {
		end := i + batchSize
		if end > len(fm.client.symbols) {
			end = len(fm.client.symbols)
		}

		batch := fm.client.symbols[i:end]
		fm.fetchFundingRatesBatch(batch, i/batchSize+1, totalBatches)

		// 批次间延迟，避免API限制
		if end < len(fm.client.symbols) {
			time.Sleep(100 * time.Millisecond)
		}
	}
}

// fetchFundingRatesBatch 批量获取资金费率
func (fm *FundingDataManager) fetchFundingRatesBatch(symbols []string, batchNum, totalBatches int) {
	for _, symbol := range symbols {
		select {
		case <-fm.client.ctx.Done():
			return
		default:
			fm.fetchSingleFundingRate(symbol)
			time.Sleep(10 * time.Millisecond) // 避免API限制
		}
	}

	log.Printf("✅ [Bybit] 资金费率批次 %d/%d 完成，获取了 %d 个交易对",
		batchNum, totalBatches, len(symbols))
}

// fetchSingleFundingRate 获取单个交易对的资金费率
func (fm *FundingDataManager) fetchSingleFundingRate(symbol string) {
	url := fmt.Sprintf("%s/v5/market/tickers?category=linear&symbol=%s",
		fm.client.config.RestAPIURL, symbol)

	resp, err := makeHTTPRequest(url)
	if err != nil {
		log.Printf("❌ [Bybit] 获取资金费率失败 %s: %v", symbol, err)
		return
	}
	defer resp.Body.Close()

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		log.Printf("❌ [Bybit] 解析资金费率响应失败 %s: %v", symbol, err)
		return
	}

	// 检查响应状态
	retCode, ok := response["retCode"].(float64)
	if !ok || retCode != 0 {
		log.Printf("❌ [Bybit] 资金费率API返回错误 %s: %v", symbol, response["retMsg"])
		return
	}

	// 解析数据
	result, ok := response["result"].(map[string]interface{})
	if !ok {
		return
	}

	list, ok := result["list"].([]interface{})
	if !ok || len(list) == 0 {
		return
	}

	tickerData, ok := list[0].(map[string]interface{})
	if !ok {
		return
	}

	// 提取资金费率信息
	fundingRate, fundingRateOk := tickerData["fundingRate"].(string)
	nextFundingTime, nextFundingTimeOk := tickerData["nextFundingTime"].(string)

	if fundingRateOk && nextFundingTimeOk && fundingRate != "" && nextFundingTime != "" {
		// 构建资金费率数据
		fundingData := map[string]interface{}{
			"symbol":          symbol,
			"fundingRate":     fundingRate,
			"nextFundingTime": nextFundingTime,
			"markPrice":       tickerData["markPrice"],
			"indexPrice":      tickerData["indexPrice"],
			"timestamp":       time.Now().UnixMilli(),
		}

		// 发送到Kafka
		if fm.client.kafkaProducer != nil {
			fm.client.kafkaProducer.SendFundingData(symbol, fundingData)
			atomic.AddInt64(&fm.fundingCount, 1)
			atomic.AddInt64(&fm.client.totalFundingCount, 1)
		}

		log.Printf("📈 [Bybit] 获取资金费率成功 %s: %s", symbol, fundingRate)
	}
}

func main() {
	// 加载配置
	config, err := loadConfig("config.json")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 设置日志
	if err := setupLogging(config); err != nil {
		log.Fatalf("❌ 设置日志失败: %v", err)
	}

	// 创建数据处理器
	dataHandler := NewBybitDataHandler()

	// 创建Bybit客户端
	client := NewBybitClient(config, dataHandler)

	log.Printf("🚀 启动Bybit数据订阅程序")
	log.Printf("📡 WebSocket地址: %s", config.WebSocketURL)
	log.Printf("🔧 心跳间隔: %v", config.GetPingInterval())

	// 获取活跃交易对
	if err := client.GetAllSymbols(); err != nil {
		log.Fatalf("❌ 获取交易对失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据订阅
	if err := client.SubscribeData(); err != nil {
		log.Fatalf("❌ 启动订阅失败: %v", err)
	}

	log.Printf("🎉 所有订阅已启动，等待数据...")
	log.Printf("💡 程序具备以下健壮性保障：")
	log.Printf("   • 数据类型分离：交易数据和盘口数据使用独立连接")
	log.Printf("   • 自动重连机制：连接断开后自动重连")
	log.Printf("   • 心跳检测：定期发送心跳，检测连接状态")
	log.Printf("   • 连接监控：实时监控连接健康状态")
	log.Printf("   • 批次处理：每批15个交易对，减少连接压力")

	// 等待信号
	<-sigChan
	log.Printf("📡 收到停止信号")

	// 停止客户端
	client.Stop()
}
