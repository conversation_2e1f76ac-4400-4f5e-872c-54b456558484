package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type Config struct {
	Exchange       string `json:"exchange"`
	WebSocketURL   string `json:"websocket_url"`
	RestAPIURL     string `json:"rest_api_url"`
	ReconnectDelay string `json:"reconnect_delay"`
	PingInterval   string `json:"ping_interval"`
	LogFile        string `json:"log_file"`
	LogLevel       string `json:"log_level"`

	// Kafka配置
	Kafka KafkaConfig `json:"kafka"`

	// 运行时解析的时间字段
	reconnectDelay time.Duration
	pingInterval   time.Duration
}

// KafkaConfig Kafka配置结构
type KafkaConfig struct {
	Enabled          bool                `json:"enabled"`
	Brokers          []string            `json:"brokers"`
	SecurityProtocol string              `json:"security_protocol"`
	SASLMechanism    string              `json:"sasl_mechanism"`
	SASLUsername     string              `json:"sasl_username"`
	SASLPassword     string              `json:"sasl_password"`
	Topics           KafkaTopics         `json:"topics"`
	ProducerConfig   KafkaProducerConfig `json:"producer_config"`
}

// KafkaTopics Kafka主题配置
type KafkaTopics struct {
	Trades     string `json:"trades"`
	Orderbooks string `json:"orderbooks"`
	Funding    string `json:"funding"`
}

// KafkaProducerConfig Kafka生产者配置
type KafkaProducerConfig struct {
	Acks         string `json:"acks"`
	Retries      int    `json:"retries"`
	BatchSize    int    `json:"batch_size"`
	LingerMs     int    `json:"linger_ms"`
	BufferMemory int    `json:"buffer_memory"`
}

func loadConfig(filename string) (*Config, error) {
	// 默认配置
	config := &Config{
		Exchange:       "bybit",
		WebSocketURL:   "wss://stream.bybit.com/v5/public/linear",
		RestAPIURL:     "https://api.bybit.com",
		ReconnectDelay: "5s",
		PingInterval:   "20s",
		LogFile:        "logs/bybit.log",
		LogLevel:       "info",
	}

	// 尝试加载配置文件
	if _, err := os.Stat(filename); err == nil {
		data, err := os.ReadFile(filename)
		if err != nil {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}

		if err := json.Unmarshal(data, config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
	}

	// 解析时间字段
	if err := config.parseDurations(); err != nil {
		return nil, fmt.Errorf("解析时间配置失败: %v", err)
	}

	return config, nil
}

func (c *Config) parseDurations() error {
	var err error

	c.reconnectDelay, err = time.ParseDuration(c.ReconnectDelay)
	if err != nil {
		return fmt.Errorf("解析reconnect_delay失败: %v", err)
	}

	c.pingInterval, err = time.ParseDuration(c.PingInterval)
	if err != nil {
		return fmt.Errorf("解析ping_interval失败: %v", err)
	}

	return nil
}

func (c *Config) GetReconnectDelay() time.Duration {
	return c.reconnectDelay
}

func (c *Config) GetPingInterval() time.Duration {
	return c.pingInterval
}

func (c *Config) setDurationStrings() {
	c.ReconnectDelay = c.reconnectDelay.String()
	c.PingInterval = c.pingInterval.String()
}

func (c *Config) saveConfig(filename string) error {
	// 设置字符串字段用于JSON序列化
	c.setDurationStrings()

	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}
