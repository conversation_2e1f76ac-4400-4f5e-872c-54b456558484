#!/bin/bash

# 停止所有交易所数据订阅程序 - 统一版本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_message $BLUE "🛑 停止所有交易所数据订阅程序..."
echo ""

# 定义统一的停止函数
stop_exchange() {
    local exchange=$1
    local stopped=false
    
    print_message $YELLOW "📈 停止: $exchange"
    
    # 检查交易所目录是否存在
    if [ ! -d "$exchange" ]; then
        print_message $RED "❌ 交易所目录不存在: $exchange"
        return 1
    fi
    
    # 进入交易所目录
    cd "$exchange"
    
    # 使用交易所自己的停止脚本
    if [ -f "start.sh" ]; then
        ./start.sh stop
        stopped=true
    else
        print_message $YELLOW "⚠️  启动脚本不存在: $exchange/start.sh"
        
        # 备用方案：手动查找和停止进程
        local program_name="${exchange}-subscriber"
        
        # 方法1：查找PID文件
        local pid_files=("${program_name}.pid" "logs/${program_name}.pid" "../logs/${exchange}.pid")
        
        for pid_file in "${pid_files[@]}"; do
            if [ -f "$pid_file" ]; then
                local pid=$(cat "$pid_file")
                if ps -p "$pid" > /dev/null 2>&1; then
                    print_message $BLUE "🛑 停止程序 (PID: $pid)..."
                    kill "$pid" 2>/dev/null
                    
                    # 等待进程结束
                    local count=0
                    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
                        sleep 1
                        count=$((count + 1))
                    done
                    
                    if ps -p "$pid" > /dev/null 2>&1; then
                        print_message $YELLOW "⚡ 强制停止程序..."
                        kill -9 "$pid" 2>/dev/null
                    fi
                    
                    rm -f "$pid_file"
                    stopped=true
                    break
                else
                    rm -f "$pid_file"
                fi
            fi
        done
        
        # 方法2：按进程名查找
        if [ "$stopped" = false ]; then
            print_message $BLUE "🔍 按进程名查找并停止程序..."
            local pids=$(pgrep -f "$program_name" 2>/dev/null)
            if [ -n "$pids" ]; then
                for pid in $pids; do
                    print_message $BLUE "🛑 停止进程 (PID: $pid)..."
                    kill "$pid" 2>/dev/null
                    sleep 2
                    if ps -p "$pid" > /dev/null 2>&1; then
                        kill -9 "$pid" 2>/dev/null
                    fi
                    stopped=true
                done
            fi
        fi
        
        # 方法3：使用killall
        if [ "$stopped" = false ]; then
            print_message $BLUE "🔍 尝试killall停止程序..."
            if command -v killall &> /dev/null; then
                killall "$program_name" 2>/dev/null && stopped=true
            fi
        fi
    fi
    
    if [ "$stopped" = true ]; then
        print_message $GREEN "✅ 程序已停止"
    else
        print_message $YELLOW "⚠️  程序未运行或停止失败"
    fi
    
    # 返回上级目录
    cd ..
    echo ""
}

# 停止各个交易所
print_message $CYAN "📡 开始停止各交易所程序..."
echo ""

# 定义交易所列表
EXCHANGES=("binance" "bitget" "bybit" "okx" "hyperliquid" "gateio")

for exchange in "${EXCHANGES[@]}"; do
    stop_exchange "$exchange"
done

print_message $GREEN "✅ 所有交易所程序已停止！"
echo ""

# 显示最终状态
print_message $CYAN "📋 最终状态："

for exchange in "${EXCHANGES[@]}"; do
    if [ ! -d "$exchange" ]; then
        print_message $YELLOW "   ⚠️ $exchange - 目录不存在"
        continue
    fi
    
    local running=false
    local program_name="${exchange}-subscriber"
    
    # 检查各种可能的PID文件位置
    local pid_files=(
        "${exchange}/${program_name}.pid"
        "${exchange}/logs/${program_name}.pid" 
        "logs/${exchange}.pid"
    )
    
    for pid_file in "${pid_files[@]}"; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if ps -p "$pid" > /dev/null 2>&1; then
                print_message $YELLOW "   ⚠️ $exchange (PID: $pid) - 仍在运行"
                running=true
                break
            fi
        fi
    done
    
    # 如果没有PID文件，检查进程是否还在运行
    if [ "$running" = false ]; then
        local pids=$(pgrep -f "$program_name" 2>/dev/null)
        if [ -n "$pids" ]; then
            print_message $YELLOW "   ⚠️ $exchange (PID: $pids) - 仍在运行"
            running=true
        fi
    fi
    
    if [ "$running" = false ]; then
        print_message $GREEN "   ✅ $exchange - 已停止"
    fi
done

echo ""
print_message $BLUE "💡 提示："
print_message $BLUE "   • 使用 './start_all.sh start-all' 启动所有交易所"
print_message $BLUE "   • 使用 './start_all.sh status-all' 查看运行状态"
print_message $BLUE "   • 使用 'make stop-all' 也可以停止所有程序" 