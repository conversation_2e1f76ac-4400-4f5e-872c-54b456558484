# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directories
build/
dist/
*/build/
*/dist/

# Log files
logs/
*.log
*/logs/
*/*.log

# PID files
*.pid
*/*.pid

# Configuration files (keep templates)
# config.json
# */config.json

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Runtime data
pids/
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Environment variables file
.env
.env.test
.env.production
.env.local

# Exchange specific ignores
binance/binance-subscriber
bitget/bitget-subscriber

# Backup files
*.bak
*.backup

# Archive files
*.tar.gz
*.zip
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.parcel-cache/

# Node modules (if any)
node_modules/

# Python cache (if any)
__pycache__/
*.pyc
*.pyo
*.pyd

# Rust target (if any)
target/

# Java class files (if any)
*.class

# Go sum files are usually committed, but can be ignored if needed
# go.sum
# */go.sum 