#!/bin/bash

# 加密货币交易所数据订阅程序集合 - 总启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 支持的交易所
EXCHANGES=("binance" "bitget" "bybit" "okx" "hyperliquid" "gateio")

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：打印标题
print_title() {
    local title=$1
    echo ""
    print_message $CYAN "🏗️  =================================="
    print_message $CYAN "   $title"
    print_message $CYAN "🏗️  =================================="
    echo ""
}

# 函数：检查交易所目录是否存在
check_exchange() {
    local exchange=$1
    if [ ! -d "$exchange" ]; then
        print_message $RED "❌ 交易所目录不存在: $exchange"
        return 1
    fi
    return 0
}

# 函数：执行交易所命令
execute_exchange_command() {
    local exchange=$1
    local command=$2
    local description=$3
    
    if ! check_exchange "$exchange"; then
        return 1
    fi
    
    print_message $BLUE "📈 $description: $exchange"
    cd "$exchange"
    
    if [ -f "start.sh" ]; then
        ./start.sh "$command"
    else
        print_message $YELLOW "⚠️  启动脚本不存在: $exchange/start.sh"
    fi
    
    cd ..
    echo ""
}

# 函数：启动所有交易所
start_all() {
    print_title "启动所有交易所程序"
    
    for exchange in "${EXCHANGES[@]}"; do
        execute_exchange_command "$exchange" "start" "启动"
    done
    
    print_message $GREEN "✅ 所有交易所启动完成"
}

# 函数：停止所有交易所
stop_all() {
    print_title "停止所有交易所程序"
    
    for exchange in "${EXCHANGES[@]}"; do
        execute_exchange_command "$exchange" "stop" "停止"
    done
    
    print_message $GREEN "✅ 所有交易所停止完成"
}

# 函数：重启所有交易所
restart_all() {
    print_title "重启所有交易所程序"
    
    for exchange in "${EXCHANGES[@]}"; do
        execute_exchange_command "$exchange" "restart" "重启"
    done
    
    print_message $GREEN "✅ 所有交易所重启完成"
}

# 函数：查看所有交易所状态
status_all() {
    print_title "查看所有交易所状态"
    
    for exchange in "${EXCHANGES[@]}"; do
        execute_exchange_command "$exchange" "status" "状态检查"
    done
}

# 函数：构建所有交易所
build_all() {
    print_title "构建所有交易所程序"
    
    for exchange in "${EXCHANGES[@]}"; do
        if ! check_exchange "$exchange"; then
            continue
        fi
        
        print_message $BLUE "🔨 构建: $exchange"
        cd "$exchange"
        
        if [ -f "Makefile" ]; then
            make build
        elif [ -f "start.sh" ]; then
            ./start.sh build
        else
            print_message $YELLOW "⚠️  无法构建: $exchange (缺少Makefile或start.sh)"
        fi
        
        cd ..
        echo ""
    done
    
    print_message $GREEN "✅ 所有交易所构建完成"
}

# 函数：启动特定交易所
start_exchange() {
    local exchange=$1
    local executable=$2
    local config_file=$3
    
    echo "🔗 启动 $exchange..."
    
    if [ ! -f "$exchange/$executable" ]; then
        echo "❌ $exchange 可执行文件不存在: $exchange/$executable"
        return 1
    fi
    
    if [ ! -f "$exchange/$config_file" ]; then
        echo "❌ $exchange 配置文件不存在: $exchange/$config_file"
        return 1
    fi
    
    cd $exchange
    nohup ./$executable > ../logs/${exchange}.log 2>&1 &
    local pid=$!
    echo $pid > ../logs/${exchange}.pid
    cd ..
    
    echo "✅ $exchange 已启动 (PID: $pid)"
    sleep 2
}

# 函数：停止特定交易所
stop_exchange() {
    local exchange=$1
    if [ -z "$exchange" ]; then
        print_message $RED "❌ 请指定交易所名称"
        show_help
        return 1
    fi
    
    print_title "停止交易所: $exchange"
    execute_exchange_command "$exchange" "stop" "停止"
}

# 函数：重启特定交易所
restart_exchange() {
    local exchange=$1
    if [ -z "$exchange" ]; then
        print_message $RED "❌ 请指定交易所名称"
        show_help
        return 1
    fi
    
    print_title "重启交易所: $exchange"
    execute_exchange_command "$exchange" "restart" "重启"
}

# 函数：查看特定交易所状态
status_exchange() {
    local exchange=$1
    if [ -z "$exchange" ]; then
        print_message $RED "❌ 请指定交易所名称"
        show_help
        return 1
    fi
    
    print_title "查看交易所状态: $exchange"
    execute_exchange_command "$exchange" "status" "状态检查"
}

# 函数：查看特定交易所日志
logs_exchange() {
    local exchange=$1
    local follow=$2
    
    if [ -z "$exchange" ]; then
        print_message $RED "❌ 请指定交易所名称"
        show_help
        return 1
    fi
    
    if ! check_exchange "$exchange"; then
        return 1
    fi
    
    print_title "查看交易所日志: $exchange"
    cd "$exchange"
    
    if [ -f "start.sh" ]; then
        if [ "$follow" = "-f" ]; then
            ./start.sh logs -f
        else
            ./start.sh logs
        fi
    else
        print_message $YELLOW "⚠️  启动脚本不存在: $exchange/start.sh"
    fi
    
    cd ..
}

# 函数：显示项目信息
show_info() {
    print_title "项目信息"
    
    print_message $BLUE "📊 基本信息:"
    echo "  项目名称: 加密货币交易所数据订阅程序集合"
    echo "  项目目录: $(pwd)"
    echo "  Go版本: $(go version 2>/dev/null || echo '未安装')"
    echo ""
    
    print_message $BLUE "📈 支持的交易所:"
    for exchange in "${EXCHANGES[@]}"; do
        if [ -d "$exchange" ]; then
            echo "  ✅ $exchange - 已实现"
        else
            echo "  ❌ $exchange - 未实现"
        fi
    done
    echo ""
    
    print_message $BLUE "📁 目录结构:"
    ls -la | grep "^d" | awk '{print "  " $9 "/"}'
    echo ""
    
    print_message $BLUE "📊 各交易所状态:"
    for exchange in "${EXCHANGES[@]}"; do
        if [ -d "$exchange" ]; then
            echo "  📈 $exchange:"
            cd "$exchange"
            if [ -f "start.sh" ]; then
                ./start.sh status 2>/dev/null | grep -E "(运行|停止|PID)" | sed 's/^/    /'
            else
                echo "    ⚠️  无启动脚本"
            fi
            cd ..
        fi
    done
}

# 函数：显示帮助
show_help() {
    print_title "加密货币交易所数据订阅程序集合"
    
    echo "用法: $0 {command} [exchange] [options]"
    echo ""
    echo "📋 全局命令:"
    echo "  start-all        - 启动所有交易所程序"
    echo "  stop-all         - 停止所有交易所程序"
    echo "  force-stop-all   - 强制停止所有交易所程序"
    echo "  restart-all      - 重启所有交易所程序"
    echo "  status-all       - 查看所有交易所状态"
    echo "  build-all        - 构建所有交易所程序"
    echo "  info             - 显示项目信息"
    echo ""
    echo "📋 单个交易所命令:"
    echo "  start <exchange>    - 启动指定交易所"
    echo "  stop <exchange>     - 停止指定交易所"
    echo "  restart <exchange>  - 重启指定交易所"
    echo "  status <exchange>   - 查看指定交易所状态"
    echo "  logs <exchange>     - 查看指定交易所日志"
    echo "  logs <exchange> -f  - 实时查看指定交易所日志"
    echo ""
    echo "📈 支持的交易所:"
    for exchange in "${EXCHANGES[@]}"; do
        echo "  - $exchange"
    done
    echo ""
    echo "📋 示例:"
    echo "  $0 start-all           # 启动所有交易所"
    echo "  $0 stop-all            # 停止所有交易所"
    echo "  $0 force-stop-all      # 强制停止所有交易所"
    echo "  $0 start binance       # 启动币安"
    echo "  $0 status bitget       # 查看Bitget状态"
    echo "  $0 logs binance -f     # 实时查看币安日志"
    echo "  $0 info                # 显示项目信息"
    echo ""
}

# 函数：强制停止所有交易所
force_stop_all() {
    print_title "强制停止所有交易所程序"
    
    # 先尝试正常停止
    for exchange in "${EXCHANGES[@]}"; do
        execute_exchange_command "$exchange" "stop" "停止"
    done
    
    # 强制杀死所有相关进程
    print_message $YELLOW "🔍 查找并强制停止所有相关进程..."
    
    # 查找并停止所有相关进程
    local process_names=("binance-subscriber" "bitget-subscriber" "bybit-subscriber" "okx-subscriber" "hyperliquid-subscriber" "gateio-subscriber")
    for process_name in "${process_names[@]}"; do
        local pids=$(pgrep -f "$process_name" 2>/dev/null)
        if [ -n "$pids" ]; then
            print_message $BLUE "🛑 发现 $process_name 进程: $pids"
            for pid in $pids; do
                print_message $BLUE "🛑 强制停止进程 (PID: $pid)..."
                kill -KILL "$pid" 2>/dev/null || true
            done
        fi
    done
    
    # 清理所有PID文件
    print_message $BLUE "🧹 清理PID文件..."
    find . -name "*.pid" -delete 2>/dev/null || true
    
    print_message $GREEN "✅ 强制停止完成"
}

# 主逻辑
case "$1" in
    start-all)
        start_all
        ;;
    stop-all)
        stop_all
        ;;
    force-stop-all)
        force_stop_all
        ;;
    restart-all)
        restart_all
        ;;
    status-all)
        status_all
        ;;
    build-all)
        build_all
        ;;
    start)
        start_exchange "$2" "$3" "$4"
        ;;
    stop)
        stop_exchange "$2"
        ;;
    restart)
        restart_exchange "$2"
        ;;
    status)
        status_exchange "$2"
        ;;
    logs)
        logs_exchange "$2" "$3"
        ;;
    info)
        show_info
        ;;
    help|--help|-h|"")
        show_help
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

exit 0 