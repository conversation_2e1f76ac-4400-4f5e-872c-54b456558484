package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// makeHTTPRequest 发送HTTP请求
func makeHTTPRequest(url string) (*http.Response, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	return client.Get(url)
}

// OKXClient OKX客户端
// FundingData 资金费率数据结构
type FundingData struct {
	InstType        string `json:"instType"`
	InstId          string `json:"instId"`
	Method          string `json:"method"`
	FormulaType     string `json:"formulaType"`
	FundingRate     string `json:"fundingRate"`
	FundingTime     string `json:"fundingTime"`
	NextFundingRate string `json:"nextFundingRate"`
	NextFundingTime string `json:"nextFundingTime"`
	MinFundingRate  string `json:"minFundingRate"`
	MaxFundingRate  string `json:"maxFundingRate"`
	InterestRate    string `json:"interestRate"`
	ImpactValue     string `json:"impactValue"`
	SettState       string `json:"settState"`
	SettFundingRate string `json:"settFundingRate"`
	Premium         string `json:"premium"`
	Ts              string `json:"ts"`
}

type OKXClient struct {
	config        *Config
	dataHandler   DataHandler
	symbols       []string
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	kafkaProducer *KafkaProducer // Kafka生产者

	// 数据管理器
	tradeManager     *TradeDataManager
	orderbookManager *OrderbookDataManager
	fundingManager   *FundingDataManager

	// 全局统计
	totalTradeCount     int64
	totalOrderbookCount int64
	totalFundingCount   int64
	startTime           time.Time
}

// ConnectionInfo OKX连接信息
type ConnectionInfo struct {
	conn           *websocket.Conn
	state          ConnectionState
	lastPingTime   time.Time
	lastPongTime   time.Time
	reconnectCount int
	heartbeatStop  chan struct{}

	// 连接质量监控
	connectionQuality   float64       // 连接质量评分 (0-1)
	avgPongDelay        time.Duration // 平均pong延迟
	consecutiveTimeouts int           // 连续超时次数
	lastQualityCheck    time.Time     // 上次质量检查时间
	messageCount        int64         // 消息计数
	lastMessageTime     time.Time     // 最后消息时间

	mutex sync.RWMutex
}

// TradeDataManager 交易数据管理器
type TradeDataManager struct {
	client      *OKXClient
	connections map[string]*ConnectionInfo
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type OrderbookDataManager struct {
	client         *OKXClient
	connections    map[string]*ConnectionInfo
	connMutex      sync.RWMutex
	orderbookCount int64
}

// FundingDataManager 资金费率数据管理器
type FundingDataManager struct {
	client       *OKXClient
	connections  map[string]*ConnectionInfo
	connMutex    sync.RWMutex
	fundingCount int64
}

// NewOKXClient 创建OKX客户端
func NewOKXClient(config *Config, dataHandler DataHandler) *OKXClient {
	ctx, cancel := context.WithCancel(context.Background())

	// 初始化Kafka生产者
	kafkaProducer, err := NewKafkaProducer(&config.Kafka)
	if err != nil {
		fmt.Printf("❌ 初始化Kafka生产者失败: %v\n", err)
		kafkaProducer = nil
	}

	client := &OKXClient{
		config:        config,
		dataHandler:   dataHandler,
		ctx:           ctx,
		cancel:        cancel,
		startTime:     time.Now(),
		kafkaProducer: kafkaProducer,
	}

	client.tradeManager = &TradeDataManager{
		client:      client,
		connections: make(map[string]*ConnectionInfo),
	}

	client.orderbookManager = &OrderbookDataManager{
		client:      client,
		connections: make(map[string]*ConnectionInfo),
	}

	client.fundingManager = &FundingDataManager{
		client:      client,
		connections: make(map[string]*ConnectionInfo),
	}

	return client
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetAllSymbols 获取所有活跃交易对
func (c *OKXClient) GetAllSymbols() error {
	symbols, err := getActiveSymbols(c.config)
	if err != nil {
		return err
	}
	c.symbols = symbols
	return nil
}

// SubscribeData 订阅数据
func (c *OKXClient) SubscribeData() error {
	log.Printf("🚀 开始分批订阅交易数据和盘口数据...")

	batchSize := 20 // OKX建议每批20个交易对
	totalBatches := (len(c.symbols) + batchSize - 1) / batchSize

	log.Printf("📊 分批订阅策略 - 总交易对: %d, 批次大小: %d, 总批次: %d",
		len(c.symbols), batchSize, totalBatches)

	batchCount := 0

	for i := 0; i < len(c.symbols); i += batchSize {
		end := i + batchSize
		if end > len(c.symbols) {
			end = len(c.symbols)
		}

		batch := c.symbols[i:end]
		batchCount++

		log.Printf("📡 处理批次 %d/%d - 交易对数量: %d", batchCount, totalBatches, len(batch))

		// 为每批创建三个连接：交易数据、盘口数据、资金费率数据
		tradeConnName := fmt.Sprintf("trade_batch_%d", batchCount)
		orderbookConnName := fmt.Sprintf("orderbook_batch_%d", batchCount)
		fundingConnName := fmt.Sprintf("funding_batch_%d", batchCount)

		// 订阅交易数据
		log.Printf("🔗 启动交易数据连接: %s", tradeConnName)
		c.wg.Add(1)
		go c.tradeManager.subscribeTradeData(tradeConnName, batch)

		// 等待一点时间再订阅盘口数据
		time.Sleep(200 * time.Millisecond)

		// 订阅盘口数据
		log.Printf("🔗 启动盘口数据连接: %s", orderbookConnName)
		c.wg.Add(1)
		go c.orderbookManager.subscribeOrderbookData(orderbookConnName, batch)

		// 等待一点时间再订阅资金费率数据
		time.Sleep(200 * time.Millisecond)

		// 订阅资金费率数据
		log.Printf("🔗 启动资金费率连接: %s", fundingConnName)
		c.wg.Add(1)
		go c.fundingManager.subscribeFundingData(fundingConnName, batch)

		// 批次间延迟，避免同时创建太多连接
		if batchCount < totalBatches {
			log.Printf("⏰ 批次 %d 完成，等待 1 秒后继续下一批次...", batchCount)
			time.Sleep(1 * time.Second)
		}
	}

	log.Printf("✅ 所有分批订阅已启动，共创建 %d 批连接 (%d 个连接)", totalBatches, totalBatches*3)

	// 启动全局统计
	c.wg.Add(1)
	go c.printGlobalStatistics()

	// 启动连接监控
	c.wg.Add(1)
	go c.monitorConnections()

	return nil
}

// subscribeTradeData 订阅交易数据
func (tm *TradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.client.wg.Done()

	// 构建交易数据订阅参数
	args := make([]map[string]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = map[string]string{
			"channel": "trades",
			"instId":  symbol,
		}
	}

	tm.subscribeWithRetry(connName, args, tm.handleTradeMessage)
}

// subscribeOrderbookData 订阅盘口数据
func (om *OrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.client.wg.Done()

	// 构建盘口数据订阅参数 - 使用bbo-tbt获得1档深度数据，每10毫秒推送，实时性最高
	args := make([]map[string]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = map[string]string{
			"channel": "bbo-tbt",
			"instId":  symbol,
		}
	}

	om.subscribeWithRetry(connName, args, om.handleOrderbookMessage)
}

// subscribeFundingData 订阅资金费率数据
func (fm *FundingDataManager) subscribeFundingData(connName string, symbols []string) {
	defer fm.client.wg.Done()

	// 构建资金费率数据订阅参数
	args := make([]map[string]string, len(symbols))
	for i, symbol := range symbols {
		args[i] = map[string]string{
			"channel": "funding-rate",
			"instId":  symbol,
		}
	}

	fm.subscribeWithRetry(connName, args, fm.handleFundingMessage)
}

// subscribeWithRetry 通用订阅方法（带重连机制）
func (tm *TradeDataManager) subscribeWithRetry(connName string, args []map[string]string, handler func([]byte)) {
	reconnectAttempts := 0

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 交易数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if tm.connectAndListen(connName, args, handler, &reconnectAttempts) {
				log.Printf("✅ [OKX] 交易数据连接 %s 重连成功", connName)
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 15 {
				log.Printf("❌ 交易数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			// 智能重连延迟
			currentDelay := tm.calculateSmartReconnectDelay(connName, reconnectAttempts)
			log.Printf("⚠️ 交易数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-tm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 盘口数据版本
func (om *OrderbookDataManager) subscribeWithRetry(connName string, args []map[string]string, handler func([]byte)) {
	reconnectAttempts := 0

	for {
		select {
		case <-om.client.ctx.Done():
			log.Printf("🛑 盘口数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if om.connectAndListen(connName, args, handler, &reconnectAttempts) {
				log.Printf("✅ [OKX] 盘口数据连接 %s 重连成功", connName)
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 15 {
				log.Printf("❌ 盘口数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			// 智能重连延迟
			currentDelay := om.calculateSmartReconnectDelay(connName, reconnectAttempts)
			log.Printf("⚠️ 盘口数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-om.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 资金费率数据版本
func (fm *FundingDataManager) subscribeWithRetry(connName string, args []map[string]string, handler func([]byte)) {
	reconnectAttempts := 0

	for {
		select {
		case <-fm.client.ctx.Done():
			log.Printf("🛑 资金费率连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if fm.connectAndListen(connName, args, handler, &reconnectAttempts) {
				log.Printf("✅ [OKX] 资金费率连接 %s 重连成功", connName)
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 15 {
				log.Printf("❌ 资金费率连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			// 智能重连延迟
			currentDelay := fm.calculateSmartReconnectDelay(connName, reconnectAttempts)
			log.Printf("⚠️ 资金费率连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-fm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// connectAndListen 交易数据连接和监听
func (tm *TradeDataManager) connectAndListen(connName string, args []map[string]string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接交易数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 设置连接超时
	dialer := websocket.Dialer{
		HandshakeTimeout: 30 * time.Second,
		ReadBufferSize:   4096,
		WriteBufferSize:  4096,
	}

	// 建立WebSocket连接
	conn, _, err := dialer.Dial(tm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 交易数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	// 创建连接信息
	connInfo := &ConnectionInfo{
		conn:          conn,
		state:         StateConnected,
		lastPingTime:  time.Now(),
		lastPongTime:  time.Now(),
		heartbeatStop: make(chan struct{}, 1),
	}

	tm.connMutex.Lock()
	tm.connections[connName] = connInfo
	tm.connMutex.Unlock()

	defer func() {
		tm.connMutex.Lock()
		delete(tm.connections, connName)
		tm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 交易数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 交易数据连接 %s 建立成功", connName)

	// 设置增强的pong处理器 - 被动响应服务器ping
	conn.SetPongHandler(func(appData string) error {
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 交易数据收到WebSocket pong响应 %s", connName)
		return conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	})

	// 设置ping处理器 - 被动响应服务器ping
	conn.SetPingHandler(func(appData string) error {
		// 立即响应服务器的ping
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [OKX] 交易数据响应服务器ping失败 %s: %v", connName, err)
			return err
		}
		// 更新最后pong时间
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 交易数据响应服务器ping %s", connName)
		return nil
	})

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送交易数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 交易数据订阅消息已发送 %s", connName)

	// 启动增强心跳机制
	go tm.startEnhancedHeartbeat(connName, connInfo)

	// 设置初始读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))

	// 监听消息
	messageCount := 0

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 交易数据连接 %s 收到停止信号，共处理 %d 条消息", connName, messageCount)
			return true
		default:
			// 设置读取超时
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 检查是否是网络错误
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("❌ 交易数据WebSocket异常关闭 %s: %v", connName, err)
				} else {
					log.Printf("⚠️ 交易数据WebSocket连接中断 %s: %v", connName, err)
				}
				return false
			}

			messageCount++

			// 每1000条消息记录一次
			if messageCount%1000 == 0 {
				log.Printf("📊 [OKX] 交易数据连接 %s 已处理 %d 条消息", connName, messageCount)
			}

			// 更新连接信息的消息统计
			connInfo.mutex.Lock()
			connInfo.messageCount++
			connInfo.lastMessageTime = time.Now()
			connInfo.mutex.Unlock()

			handler(message)
		}
	}
}

// connectAndListen 盘口数据连接和监听
func (om *OrderbookDataManager) connectAndListen(connName string, args []map[string]string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接盘口数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 设置连接超时
	dialer := websocket.Dialer{
		HandshakeTimeout: 30 * time.Second,
		ReadBufferSize:   4096,
		WriteBufferSize:  4096,
	}

	// 建立WebSocket连接
	conn, _, err := dialer.Dial(om.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 盘口数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	// 创建连接信息
	connInfo := &ConnectionInfo{
		conn:          conn,
		state:         StateConnected,
		lastPingTime:  time.Now(),
		lastPongTime:  time.Now(),
		heartbeatStop: make(chan struct{}, 1),
	}

	om.connMutex.Lock()
	om.connections[connName] = connInfo
	om.connMutex.Unlock()

	defer func() {
		om.connMutex.Lock()
		delete(om.connections, connName)
		om.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 盘口数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 盘口数据连接 %s 建立成功", connName)

	// 设置增强的pong处理器 - 被动响应服务器ping
	conn.SetPongHandler(func(appData string) error {
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 盘口数据收到WebSocket pong响应 %s", connName)
		return conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	})

	// 设置ping处理器 - 被动响应服务器ping
	conn.SetPingHandler(func(appData string) error {
		// 立即响应服务器的ping
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [OKX] 盘口数据响应服务器ping失败 %s: %v", connName, err)
			return err
		}
		// 更新最后pong时间
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 盘口数据响应服务器ping %s", connName)
		return nil
	})

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送盘口数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 盘口数据订阅消息已发送 %s", connName)

	// 启动增强心跳机制
	go om.startEnhancedHeartbeat(connName, connInfo)

	// 设置初始读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))

	// 监听消息
	messageCount := 0

	for {
		select {
		case <-om.client.ctx.Done():
			log.Printf("🛑 盘口数据连接 %s 收到停止信号，共处理 %d 条消息", connName, messageCount)
			return true
		default:
			// 设置读取超时
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 检查是否是网络错误
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("❌ 盘口数据WebSocket异常关闭 %s: %v", connName, err)
				} else {
					log.Printf("⚠️ 盘口数据WebSocket连接中断 %s: %v", connName, err)
				}
				return false
			}

			messageCount++

			// 每1000条消息记录一次
			if messageCount%1000 == 0 {
				log.Printf("📊 [OKX] 盘口数据连接 %s 已处理 %d 条消息", connName, messageCount)
			}

			// 更新连接信息的消息统计
			connInfo.mutex.Lock()
			connInfo.messageCount++
			connInfo.lastMessageTime = time.Now()
			connInfo.mutex.Unlock()

			handler(message)
		}
	}
}

// connectAndListen 资金费率数据连接和监听
func (fm *FundingDataManager) connectAndListen(connName string, args []map[string]string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接资金费率数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(args), *reconnectAttempts+1)

	// 设置连接超时
	dialer := websocket.Dialer{
		HandshakeTimeout: 30 * time.Second,
		ReadBufferSize:   4096,
		WriteBufferSize:  4096,
	}

	// 建立WebSocket连接
	conn, _, err := dialer.Dial(fm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 资金费率数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	// 创建连接信息
	connInfo := &ConnectionInfo{
		conn:          conn,
		state:         StateConnected,
		lastPingTime:  time.Now(),
		lastPongTime:  time.Now(),
		heartbeatStop: make(chan struct{}, 1),
	}

	fm.connMutex.Lock()
	fm.connections[connName] = connInfo
	fm.connMutex.Unlock()

	defer func() {
		fm.connMutex.Lock()
		delete(fm.connections, connName)
		fm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 资金费率数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 资金费率数据连接 %s 建立成功", connName)

	// 设置增强的pong处理器 - 被动响应服务器ping
	conn.SetPongHandler(func(appData string) error {
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 资金费率数据收到WebSocket pong响应 %s", connName)
		return conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	})

	// 设置ping处理器 - 被动响应服务器ping
	conn.SetPingHandler(func(appData string) error {
		// 立即响应服务器的ping
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [OKX] 资金费率数据响应服务器ping失败 %s: %v", connName, err)
			return err
		}
		// 更新最后pong时间
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		log.Printf("💓 [OKX] 资金费率数据响应服务器ping %s", connName)
		return nil
	})

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送资金费率数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 资金费率数据订阅消息已发送 %s", connName)

	// 启动增强心跳机制
	go fm.startEnhancedHeartbeat(connName, connInfo)

	// 设置初始读取超时
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))

	// 监听消息
	messageCount := 0

	for {
		select {
		case <-fm.client.ctx.Done():
			log.Printf("🛑 资金费率数据连接 %s 收到停止信号，共处理 %d 条消息", connName, messageCount)
			return true
		default:
			// 设置读取超时
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))
			_, message, err := conn.ReadMessage()
			if err != nil {
				// 检查是否是网络错误
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("❌ 资金费率数据WebSocket异常关闭 %s: %v", connName, err)
				} else {
					log.Printf("⚠️ 资金费率数据WebSocket连接中断 %s: %v", connName, err)
				}
				return false
			}

			messageCount++

			// 每100条消息记录一次（资金费率消息较少）
			if messageCount%100 == 0 {
				log.Printf("📊 [OKX] 资金费率数据连接 %s 已处理 %d 条消息", connName, messageCount)
			}

			// 更新连接信息的消息统计
			connInfo.mutex.Lock()
			connInfo.messageCount++
			connInfo.lastMessageTime = time.Now()
			connInfo.mutex.Unlock()

			handler(message)
		}
	}
}

// startPing 交易数据心跳
func (tm *TradeDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(tm.client.config.GetPingInterval())
	defer ticker.Stop()

	for {
		select {
		case <-tm.client.ctx.Done():
			return
		case <-ticker.C:
			// 设置写入超时
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
				log.Printf("💔 交易数据发送心跳失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [OKX] 交易数据心跳发送成功 %s", connName)
		}
	}
}

// startPing 盘口数据心跳
func (om *OrderbookDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(om.client.config.GetPingInterval())
	defer ticker.Stop()

	for {
		select {
		case <-om.client.ctx.Done():
			return
		case <-ticker.C:
			// 设置写入超时
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
				log.Printf("💔 盘口数据发送心跳失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [OKX] 盘口数据心跳发送成功 %s", connName)
		}
	}
}

// startPing 资金费率数据心跳
func (fm *FundingDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(fm.client.config.GetPingInterval())
	defer ticker.Stop()

	for {
		select {
		case <-fm.client.ctx.Done():
			return
		case <-ticker.C:
			// 设置写入超时
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
				log.Printf("💔 资金费率数据发送心跳失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [OKX] 资金费率数据心跳发送成功 %s", connName)
		}
	}
}

// handleTradeMessage 处理交易数据
func (tm *TradeDataManager) handleTradeMessage(message []byte) {
	// 检查是否是pong响应
	if string(message) == "pong" {
		return
	}

	// 检查是否是订阅确认
	event := gjson.GetBytes(message, "event").String()
	if event == "subscribe" {
		log.Printf("✅ 交易数据订阅确认成功")
		return
	}

	// 处理实际的交易数据
	data := gjson.GetBytes(message, "data")
	if data.Exists() && data.IsArray() {
		for _, trade := range data.Array() {
			atomic.AddInt64(&tm.tradeCount, 1)
			atomic.AddInt64(&tm.client.totalTradeCount, 1)

			// 发送到Kafka
			if tm.client.kafkaProducer != nil {
				// 解析交易对
				symbol := gjson.Get(trade.Raw, "instId").String()
				if symbol != "" {
					// 解析完整的交易数据
					var tradeData map[string]interface{}
					if err := json.Unmarshal([]byte(trade.Raw), &tradeData); err == nil {
						tm.client.kafkaProducer.SendTradeData(symbol, tradeData)
					}
				}
			}

			// 调用数据处理器
			tm.client.dataHandler.HandleTradeMessage([]byte(trade.Raw))
		}
	}
}

// handleOrderbookMessage 处理盘口数据
func (om *OrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查是否是pong响应
	if string(message) == "pong" {
		return
	}

	// 检查是否是订阅确认
	event := gjson.GetBytes(message, "event").String()
	if event == "subscribe" {
		log.Printf("✅ 盘口数据订阅确认成功")
		return
	}

	// 处理实际的盘口数据
	data := gjson.GetBytes(message, "data")
	if data.Exists() && data.IsArray() {
		for _, orderbook := range data.Array() {
			atomic.AddInt64(&om.orderbookCount, 1)
			atomic.AddInt64(&om.client.totalOrderbookCount, 1)

			// 发送到Kafka
			if om.client.kafkaProducer != nil {
				// 从消息的arg字段获取交易对信息
				symbol := gjson.GetBytes(message, "arg.instId").String()

				if symbol != "" {
					// 解析完整的盘口数据
					var orderbookData map[string]interface{}
					if err := json.Unmarshal([]byte(orderbook.Raw), &orderbookData); err == nil {
						om.client.kafkaProducer.SendOrderbookData(symbol, orderbookData)
					} else {
						log.Printf("❌ [OKX] 盘口数据JSON解析失败: %v", err)
					}
				} else {
					log.Printf("❌ [OKX] 盘口数据交易对为空")
				}
			} else {
				log.Printf("❌ [OKX] Kafka生产者为nil")
			}

			// 调用数据处理器
			om.client.dataHandler.HandleOrderbookMessage([]byte(orderbook.Raw))
		}
	}
}

// handleFundingMessage 处理资金费率数据
func (fm *FundingDataManager) handleFundingMessage(message []byte) {
	// 检查是否是pong响应
	if string(message) == "pong" {
		return
	}

	// 检查是否是订阅确认
	event := gjson.GetBytes(message, "event").String()
	if event == "subscribe" {
		log.Printf("✅ 资金费率数据订阅确认成功")
		return
	}

	// 处理实际的资金费率数据
	data := gjson.GetBytes(message, "data")
	if data.Exists() && data.IsArray() {
		for _, funding := range data.Array() {
			atomic.AddInt64(&fm.fundingCount, 1)
			atomic.AddInt64(&fm.client.totalFundingCount, 1)

			// 发送到Kafka
			if fm.client.kafkaProducer != nil {
				// 解析交易对
				symbol := gjson.Get(funding.Raw, "instId").String()
				if symbol != "" {
					// 解析完整的资金费率数据
					var fundingData map[string]interface{}
					if err := json.Unmarshal([]byte(funding.Raw), &fundingData); err == nil {
						fm.client.kafkaProducer.SendFundingData(symbol, fundingData)
					}
				}
			}

			// 调用数据处理器
			fm.client.dataHandler.HandleTradeMessage([]byte(funding.Raw))
		}
	}
}

// printGlobalStatistics 打印全局统计
func (c *OKXClient) printGlobalStatistics() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			runtime := time.Since(c.startTime)
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)

			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 19) / 20 // 每批20个交易对

			log.Printf("📊 [OKX] 全局统计 - 运行时间: %v, 交易连接: %d/%d, 盘口连接: %d/%d, 资金费率连接: %d/%d, 交易消息: %d, 盘口消息: %d, 资金费率消息: %d, 总消息: %d",
				runtime.Round(time.Second),
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				totalTrade,
				totalOrderbook,
				totalFunding,
				totalTrade+totalOrderbook+totalFunding,
			)

			// 数据流速率统计（每秒消息数）
			if runtime.Seconds() > 0 {
				tradeRate := float64(totalTrade) / runtime.Seconds()
				orderbookRate := float64(totalOrderbook) / runtime.Seconds()
				fundingRate := float64(totalFunding) / runtime.Seconds()
				log.Printf("📈 [OKX] 数据流速率 - 交易: %.1f msg/s, 盘口: %.1f msg/s, 资金费率: %.1f msg/s",
					tradeRate, orderbookRate, fundingRate)
			}

			// WebSocket连接健康状态
			totalConnections := tradeConnections + orderbookConnections + fundingConnections
			expectedTotal := expectedConnections * 3
			if totalConnections == expectedTotal {
				log.Printf("✅ [OKX] 所有WebSocket连接健康")
			} else if totalConnections >= expectedTotal*2/3 {
				log.Printf("⚠️ [OKX] 部分WebSocket连接异常")
			} else {
				log.Printf("❌ [OKX] 大量WebSocket连接异常")
			}
		}
	}
}

// monitorConnections 监控连接状态
func (c *OKXClient) monitorConnections() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			tradeConnNames := make([]string, 0, len(c.tradeManager.connections))
			for name := range c.tradeManager.connections {
				tradeConnNames = append(tradeConnNames, name)
			}
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			orderbookConnNames := make([]string, 0, len(c.orderbookManager.connections))
			for name := range c.orderbookManager.connections {
				orderbookConnNames = append(orderbookConnNames, name)
			}
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			fundingConnNames := make([]string, 0, len(c.fundingManager.connections))
			for name := range c.fundingManager.connections {
				fundingConnNames = append(fundingConnNames, name)
			}
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 19) / 20 // 每批20个交易对

			log.Printf("🔍 [OKX] 连接监控 - 交易连接: %d/%d, 盘口连接: %d/%d, 资金费率连接: %d/%d, 交易对数: %d, 批次大小: 20",
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				len(c.symbols))

			// 详细连接状态
			log.Printf("🔗 [OKX] 连接详情 - 交易连接: %v", tradeConnNames)
			log.Printf("🔗 [OKX] 连接详情 - 盘口连接: %v", orderbookConnNames)
			log.Printf("🔗 [OKX] 连接详情 - 资金费率连接: %v", fundingConnNames)

			// 检查连接健康状态
			if tradeConnections < expectedConnections/2 {
				log.Printf("⚠️ [OKX] 交易数据连接数异常：当前 %d，预期 %d", tradeConnections, expectedConnections)
			}
			if orderbookConnections < expectedConnections/2 {
				log.Printf("⚠️ [OKX] 盘口数据连接数异常：当前 %d，预期 %d", orderbookConnections, expectedConnections)
			}
			if fundingConnections < expectedConnections/2 {
				log.Printf("⚠️ [OKX] 资金费率数据连接数异常：当前 %d，预期 %d", fundingConnections, expectedConnections)
			}

			// 数据流健康检查
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)
			totalConnections := tradeConnections + orderbookConnections + fundingConnections

			if totalConnections > 0 && totalTrade == 0 && totalOrderbook == 0 && totalFunding == 0 {
				log.Printf("⚠️ [OKX] 数据流异常：有连接但没有数据")
			}

			// 连接效率检查
			if totalConnections > 0 {
				avgTradePerConn := float64(totalTrade) / float64(tradeConnections)
				avgOrderbookPerConn := float64(totalOrderbook) / float64(orderbookConnections)
				avgFundingPerConn := float64(totalFunding) / float64(fundingConnections)
				log.Printf("📊 [OKX] 连接效率 - 平均交易消息/连接: %.1f, 平均盘口消息/连接: %.1f, 平均资金费率消息/连接: %.1f",
					avgTradePerConn, avgOrderbookPerConn, avgFundingPerConn)
			}

			// 连接质量统计
			c.logConnectionQualityStats()
		}
	}
}

// logConnectionQualityStats 记录连接质量统计
func (c *OKXClient) logConnectionQualityStats() {
	var totalQuality float64
	var qualityCount int
	var excellentCount, goodCount, fairCount, poorCount, badCount int

	// 统计交易数据连接质量
	c.tradeManager.connMutex.RLock()
	for name, connInfo := range c.tradeManager.connections {
		connInfo.mutex.RLock()
		quality := connInfo.connectionQuality
		avgDelay := connInfo.avgPongDelay
		timeouts := connInfo.consecutiveTimeouts
		messageCount := connInfo.messageCount
		connInfo.mutex.RUnlock()

		totalQuality += quality
		qualityCount++

		// 质量分级统计
		if quality >= 0.9 {
			excellentCount++
		} else if quality >= 0.7 {
			goodCount++
		} else if quality >= 0.4 {
			fairCount++
		} else if quality >= 0.3 {
			poorCount++
		} else {
			badCount++
		}

		// 详细连接监控
		log.Printf("🔍 [OKX] 交易连接 %s - 质量: %.1f%%, 平均延迟: %.1fs, 超时: %d次, 消息: %d条",
			name, quality*100, avgDelay.Seconds(), timeouts, messageCount)
	}
	c.tradeManager.connMutex.RUnlock()

	// 统计盘口数据连接质量
	c.orderbookManager.connMutex.RLock()
	for name, connInfo := range c.orderbookManager.connections {
		connInfo.mutex.RLock()
		quality := connInfo.connectionQuality
		avgDelay := connInfo.avgPongDelay
		timeouts := connInfo.consecutiveTimeouts
		messageCount := connInfo.messageCount
		connInfo.mutex.RUnlock()

		totalQuality += quality
		qualityCount++

		if quality >= 0.9 {
			excellentCount++
		} else if quality >= 0.7 {
			goodCount++
		} else if quality >= 0.4 {
			fairCount++
		} else if quality >= 0.3 {
			poorCount++
		} else {
			badCount++
		}

		log.Printf("🔍 [OKX] 盘口连接 %s - 质量: %.1f%%, 平均延迟: %.1fs, 超时: %d次, 消息: %d条",
			name, quality*100, avgDelay.Seconds(), timeouts, messageCount)
	}
	c.orderbookManager.connMutex.RUnlock()

	// 统计资金费率数据连接质量
	c.fundingManager.connMutex.RLock()
	for name, connInfo := range c.fundingManager.connections {
		connInfo.mutex.RLock()
		quality := connInfo.connectionQuality
		avgDelay := connInfo.avgPongDelay
		timeouts := connInfo.consecutiveTimeouts
		messageCount := connInfo.messageCount
		connInfo.mutex.RUnlock()

		totalQuality += quality
		qualityCount++

		if quality >= 0.9 {
			excellentCount++
		} else if quality >= 0.7 {
			goodCount++
		} else if quality >= 0.4 {
			fairCount++
		} else if quality >= 0.3 {
			poorCount++
		} else {
			badCount++
		}

		log.Printf("🔍 [OKX] 资金费率连接 %s - 质量: %.1f%%, 平均延迟: %.1fs, 超时: %d次, 消息: %d条",
			name, quality*100, avgDelay.Seconds(), timeouts, messageCount)
	}
	c.fundingManager.connMutex.RUnlock()

	// 输出总体质量统计
	if qualityCount > 0 {
		avgQuality := totalQuality / float64(qualityCount)
		log.Printf("📊 [OKX] 连接质量总览 - 平均质量: %.1f%%, 优秀: %d, 良好: %d, 一般: %d, 较差: %d, 极差: %d",
			avgQuality*100, excellentCount, goodCount, fairCount, poorCount, badCount)

		// 质量警告
		if badCount > 0 {
			log.Printf("⚠️ [OKX] 发现 %d 个极差质量连接，需要关注", badCount)
		}
		if poorCount > 0 {
			log.Printf("⚠️ [OKX] 发现 %d 个较差质量连接，建议监控", poorCount)
		}
	}
}

// Stop 停止客户端
func (c *OKXClient) Stop() {
	log.Printf("🛑 正在停止OKX客户端...")
	c.cancel()

	// 关闭所有交易数据连接
	c.tradeManager.connMutex.Lock()
	for name, connInfo := range c.tradeManager.connections {
		log.Printf("🔌 关闭交易数据连接: %s", name)
		if connInfo.conn != nil {
			connInfo.conn.Close()
		}
	}
	c.tradeManager.connMutex.Unlock()

	// 关闭所有盘口数据连接
	c.orderbookManager.connMutex.Lock()
	for name, connInfo := range c.orderbookManager.connections {
		log.Printf("🔌 关闭盘口数据连接: %s", name)
		if connInfo.conn != nil {
			connInfo.conn.Close()
		}
	}
	c.orderbookManager.connMutex.Unlock()

	// 关闭所有资金费率数据连接
	c.fundingManager.connMutex.Lock()
	for name, connInfo := range c.fundingManager.connections {
		log.Printf("🔌 关闭资金费率数据连接: %s", name)
		if connInfo.conn != nil {
			connInfo.conn.Close()
		}
	}
	c.fundingManager.connMutex.Unlock()

	// 等待所有goroutine结束
	c.wg.Wait()

	// 关闭Kafka生产者
	if c.kafkaProducer != nil {
		c.kafkaProducer.Close()
	}

	// 打印最终统计
	totalTrade := atomic.LoadInt64(&c.totalTradeCount)
	totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
	totalFunding := atomic.LoadInt64(&c.totalFundingCount)

	log.Printf("✅ OKX客户端已停止 - 总交易数据: %d 条, 总盘口数据: %d 条, 总资金费率数据: %d 条", totalTrade, totalOrderbook, totalFunding)
}

// getActiveSymbols 获取活跃的永续合约交易对
func getActiveSymbols(config *Config) ([]string, error) {
	url := fmt.Sprintf("%s/api/v5/public/instruments?instType=SWAP", config.RestAPIURL)

	resp, err := makeHTTPRequest(url)
	if err != nil {
		return nil, fmt.Errorf("请求交易对列表失败: %v", err)
	}
	defer resp.Body.Close()

	var result struct {
		Code string `json:"code"`
		Data []struct {
			InstID string `json:"instId"`
			State  string `json:"state"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Code != "0" {
		return nil, fmt.Errorf("API返回错误代码: %s", result.Code)
	}

	var symbols []string
	for _, instrument := range result.Data {
		if instrument.State == "live" {
			symbols = append(symbols, instrument.InstID)
		}
	}

	log.Printf("📊 获取到 %d 个活跃的永续合约交易对", len(symbols))
	return symbols, nil
}

// setupLogging 设置日志
func setupLogging(config *Config) error {
	// 创建日志目录
	logDir := filepath.Dir(config.LogFile)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开日志文件
	logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 设置日志输出到文件
	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	return nil
}

func main() {
	// 加载配置
	config, err := loadConfig("config.json")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 设置日志
	if err := setupLogging(config); err != nil {
		log.Fatalf("❌ 设置日志失败: %v", err)
	}

	// 创建数据处理器
	dataHandler := NewOKXDataHandler()

	// 创建OKX客户端
	client := NewOKXClient(config, dataHandler)

	log.Printf("🚀 启动OKX数据订阅程序")
	log.Printf("📡 WebSocket地址: %s", config.WebSocketURL)
	log.Printf("🔧 心跳间隔: %v", config.GetPingInterval())

	// 获取活跃交易对
	if err := client.GetAllSymbols(); err != nil {
		log.Fatalf("❌ 获取交易对失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据订阅
	if err := client.SubscribeData(); err != nil {
		log.Fatalf("❌ 启动订阅失败: %v", err)
	}

	log.Printf("🎉 所有订阅已启动，等待数据...")
	log.Printf("💡 程序具备以下健壮性保障：")
	log.Printf("   • 数据类型分离：交易数据和盘口数据使用独立连接")
	log.Printf("   • 自动重连机制：连接断开后自动重连")
	log.Printf("   • 心跳检测：定期发送心跳，检测连接状态")
	log.Printf("   • 连接监控：实时监控连接健康状态")
	log.Printf("   • 批次处理：每批20个交易对，减少连接压力")

	// 等待信号
	<-sigChan
	log.Printf("📡 收到停止信号")

	// 停止客户端
	client.Stop()
}
