package main

import (
	"log"
	"math/rand"
	"strings"
	"time"

	"github.com/gorilla/websocket"
)

// startEnhancedHeartbeat 启动增强心跳机制 - 交易数据
func (tm *TradeDataManager) startEnhancedHeartbeat(connName string, connInfo *ConnectionInfo) {
	// 主动ping间隔
	pingTicker := time.NewTicker(tm.client.config.GetPingInterval())
	defer pingTicker.Stop()

	// 连接健康检查间隔（更频繁）
	healthTicker := time.NewTicker(tm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	log.Printf("💓 [OKX] 交易数据连接 %s 增强心跳启动 - ping间隔: %v, 健康检查间隔: %v",
		connName, tm.client.config.GetPingInterval(), tm.client.config.GetPingInterval()/2)

	for {
		select {
		case <-pingTicker.C:
			// 主动发送ping保持连接活跃
			tm.sendActivePing(connName, connInfo)

		case <-healthTicker.C:
			// 连接健康检查
			if !tm.checkConnectionHealth(connName, connInfo) {
				log.Printf("💔 [OKX] 交易数据连接 %s 健康检查失败，关闭连接", connName)
				tm.cleanupConnection(connName, connInfo)
				return
			}

		case <-connInfo.heartbeatStop:
			log.Printf("💓 [OKX] 交易数据连接 %s 心跳停止", connName)
			return
		case <-tm.client.ctx.Done():
			log.Printf("💓 [OKX] 交易数据连接 %s 心跳因上下文取消而停止", connName)
			return
		}
	}
}

// sendActivePing 主动发送ping - 交易数据
func (tm *TradeDataManager) sendActivePing(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state == StateDisconnected || state == StateStopped {
		return
	}

	// 发送WebSocket ping帧（更可靠）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("okx-trade-ping")); err != nil {
		log.Printf("💔 [OKX] 交易数据连接 %s WebSocket ping发送失败: %v", connName, err)
		tm.cleanupConnection(connName, connInfo)
		return
	}

	// 同时发送应用层ping（双重保障）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
		log.Printf("💔 [OKX] 交易数据连接 %s 应用层ping发送失败: %v", connName, err)
		tm.cleanupConnection(connName, connInfo)
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	log.Printf("💓 [OKX] 交易数据连接 %s 发送双重ping", connName)
}

// checkConnectionHealth 检查连接健康状态 - 交易数据
func (tm *TradeDataManager) checkConnectionHealth(connName string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	connInfo.mutex.RUnlock()

	if conn == nil {
		log.Printf("🔍 [OKX] 交易数据连接 %s 健康检查：连接为空", connName)
		return false
	}

	if state == StateDisconnected || state == StateStopped {
		log.Printf("🔍 [OKX] 交易数据连接 %s 健康检查：状态为 %v", connName, state)
		return false
	}

	// 只有在Connected状态下才进行严格的超时检查
	if state == StateConnected {
		now := time.Now()
		pongDelay := now.Sub(lastPongTime)
		pingDelay := now.Sub(lastPingTime)

		// 使用更宽松的超时策略
		maxPongDelay := tm.client.config.GetPingInterval() * 10 // 10倍ping间隔
		maxPingDelay := tm.client.config.GetPingInterval() * 5  // 5倍ping间隔

		if pongDelay > maxPongDelay {
			log.Printf("💔 [OKX] 交易数据连接 %s pong超时: %.1fs (最大允许: %.1fs)",
				connName, pongDelay.Seconds(), maxPongDelay.Seconds())
			return false
		}

		if pingDelay > maxPingDelay {
			log.Printf("⚠️ [OKX] 交易数据连接 %s ping间隔过长: %.1fs", connName, pingDelay.Seconds())
		}

		// 更新连接质量评估
		tm.updateConnectionQuality(connName, connInfo, pongDelay, pingDelay)
	}

	return true
}

// updateConnectionQuality 更新连接质量评估 - 交易数据
func (tm *TradeDataManager) updateConnectionQuality(connName string, connInfo *ConnectionInfo, pongDelay, pingDelay time.Duration) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	// 更新平均pong延迟（使用指数移动平均）
	if connInfo.avgPongDelay == 0 {
		connInfo.avgPongDelay = pongDelay
	} else {
		// 使用0.2的权重进行平滑
		connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*0.8 + float64(pongDelay)*0.2)
	}

	// 检查是否超时
	isTimeout := pongDelay > tm.client.config.GetPingInterval()*4
	if isTimeout {
		connInfo.consecutiveTimeouts++
		log.Printf("⚠️ [OKX] 交易数据连接 %s 超时 (第%d次): pong延迟=%.1fs",
			connName, connInfo.consecutiveTimeouts, pongDelay.Seconds())
	} else {
		connInfo.consecutiveTimeouts = 0
	}

	// 计算连接质量评分 (0-1)
	quality := 1.0

	// 基于pong延迟的评分
	idealDelay := tm.client.config.GetPingInterval()
	if pongDelay > idealDelay {
		delayPenalty := float64(pongDelay-idealDelay) / float64(idealDelay*3)
		if delayPenalty > 1.0 {
			delayPenalty = 1.0
		}
		quality -= delayPenalty * 0.4 // 延迟占40%权重
	}

	// 基于连续超时的评分
	if connInfo.consecutiveTimeouts > 0 {
		timeoutPenalty := float64(connInfo.consecutiveTimeouts) / 5.0 // 5次超时为满分惩罚
		if timeoutPenalty > 1.0 {
			timeoutPenalty = 1.0
		}
		quality -= timeoutPenalty * 0.3 // 超时占30%权重
	}

	// 基于消息活跃度的评分
	messageAge := now.Sub(connInfo.lastMessageTime)
	if messageAge > 30*time.Second {
		agePenalty := float64(messageAge-30*time.Second) / float64(60*time.Second)
		if agePenalty > 1.0 {
			agePenalty = 1.0
		}
		quality -= agePenalty * 0.3 // 消息活跃度占30%权重
	}

	// 确保质量评分在合理范围内
	if quality < 0 {
		quality = 0
	}

	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	// 根据质量评分输出不同级别的日志
	if quality < 0.3 {
		log.Printf("💔 [OKX] 交易数据连接 %s 质量极差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.6 {
		log.Printf("⚠️ [OKX] 交易数据连接 %s 质量较差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.8 {
		log.Printf("📊 [OKX] 交易数据连接 %s 质量一般: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	} else {
		log.Printf("✅ [OKX] 交易数据连接 %s 质量良好: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	}
}

// cleanupConnection 清理连接 - 交易数据
func (tm *TradeDataManager) cleanupConnection(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	log.Printf("🧹 [OKX] 清理交易数据连接 %s", connName)

	if connInfo.conn != nil {
		// 发送关闭消息
		connInfo.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		connInfo.conn.Close()
		connInfo.conn = nil
	}

	// 停止心跳，使用非阻塞方式
	select {
	case connInfo.heartbeatStop <- struct{}{}:
	default:
	}

	connInfo.state = StateDisconnected
}

// 智能重连延迟计算
func (tm *TradeDataManager) calculateSmartReconnectDelay(connName string, reconnectCount int) time.Duration {
	// 基础延迟
	baseDelay := time.Duration(1<<uint(min(reconnectCount, 6))) * 3 * time.Second // 3s, 6s, 12s, 24s, 48s, 96s, 192s

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(5000)) * time.Millisecond // 0-5秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connName, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高，延迟更短
	} else if strings.Contains(connName, "orderbook") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connName, "funding") {
		typeMultiplier = 1.2 // 资金费率数据优先级较低
	}

	// 根据重连次数动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连的连接增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	log.Printf("🧮 [OKX] 连接 %s 重连延迟计算: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connName, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}

// 为盘口数据管理器添加智能重连延迟计算
func (om *OrderbookDataManager) calculateSmartReconnectDelay(connName string, reconnectCount int) time.Duration {
	// 基础延迟
	baseDelay := time.Duration(1<<uint(min(reconnectCount, 6))) * 3 * time.Second // 3s, 6s, 12s, 24s, 48s, 96s, 192s

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(5000)) * time.Millisecond // 0-5秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connName, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高，延迟更短
	} else if strings.Contains(connName, "orderbook") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connName, "funding") {
		typeMultiplier = 1.2 // 资金费率数据优先级较低
	}

	// 根据重连次数动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连的连接增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	log.Printf("🧮 [OKX] 连接 %s 重连延迟计算: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connName, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}

// 为资金费率数据管理器添加智能重连延迟计算
func (fm *FundingDataManager) calculateSmartReconnectDelay(connName string, reconnectCount int) time.Duration {
	// 基础延迟
	baseDelay := time.Duration(1<<uint(min(reconnectCount, 6))) * 3 * time.Second // 3s, 6s, 12s, 24s, 48s, 96s, 192s

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(5000)) * time.Millisecond // 0-5秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connName, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高，延迟更短
	} else if strings.Contains(connName, "orderbook") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connName, "funding") {
		typeMultiplier = 1.2 // 资金费率数据优先级较低
	}

	// 根据重连次数动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连的连接增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	log.Printf("🧮 [OKX] 连接 %s 重连延迟计算: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connName, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}

// 为盘口数据管理器添加相同的方法
func (om *OrderbookDataManager) startEnhancedHeartbeat(connName string, connInfo *ConnectionInfo) {
	pingTicker := time.NewTicker(om.client.config.GetPingInterval())
	defer pingTicker.Stop()

	healthTicker := time.NewTicker(om.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	log.Printf("💓 [OKX] 盘口数据连接 %s 增强心跳启动 - ping间隔: %v, 健康检查间隔: %v",
		connName, om.client.config.GetPingInterval(), om.client.config.GetPingInterval()/2)

	for {
		select {
		case <-pingTicker.C:
			om.sendActivePing(connName, connInfo)
		case <-healthTicker.C:
			if !om.checkConnectionHealth(connName, connInfo) {
				log.Printf("💔 [OKX] 盘口数据连接 %s 健康检查失败，关闭连接", connName)
				om.cleanupConnection(connName, connInfo)
				return
			}
		case <-connInfo.heartbeatStop:
			log.Printf("💓 [OKX] 盘口数据连接 %s 心跳停止", connName)
			return
		case <-om.client.ctx.Done():
			log.Printf("💓 [OKX] 盘口数据连接 %s 心跳因上下文取消而停止", connName)
			return
		}
	}
}

func (om *OrderbookDataManager) sendActivePing(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state == StateDisconnected || state == StateStopped {
		return
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("okx-orderbook-ping")); err != nil {
		log.Printf("💔 [OKX] 盘口数据连接 %s WebSocket ping发送失败: %v", connName, err)
		om.cleanupConnection(connName, connInfo)
		return
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
		log.Printf("💔 [OKX] 盘口数据连接 %s 应用层ping发送失败: %v", connName, err)
		om.cleanupConnection(connName, connInfo)
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	log.Printf("💓 [OKX] 盘口数据连接 %s 发送双重ping", connName)
}

func (om *OrderbookDataManager) checkConnectionHealth(connName string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	connInfo.mutex.RUnlock()

	if conn == nil {
		log.Printf("🔍 [OKX] 盘口数据连接 %s 健康检查：连接为空", connName)
		return false
	}

	if state == StateDisconnected || state == StateStopped {
		log.Printf("🔍 [OKX] 盘口数据连接 %s 健康检查：状态为 %v", connName, state)
		return false
	}

	if state == StateConnected {
		now := time.Now()
		pongDelay := now.Sub(lastPongTime)
		pingDelay := now.Sub(lastPingTime)

		maxPongDelay := om.client.config.GetPingInterval() * 10
		maxPingDelay := om.client.config.GetPingInterval() * 5

		if pongDelay > maxPongDelay {
			log.Printf("💔 [OKX] 盘口数据连接 %s pong超时: %.1fs (最大允许: %.1fs)",
				connName, pongDelay.Seconds(), maxPongDelay.Seconds())
			return false
		}

		if pingDelay > maxPingDelay {
			log.Printf("⚠️ [OKX] 盘口数据连接 %s ping间隔过长: %.1fs", connName, pingDelay.Seconds())
		}

		om.updateConnectionQuality(connName, connInfo, pongDelay, pingDelay)
	}

	return true
}

func (om *OrderbookDataManager) updateConnectionQuality(connName string, connInfo *ConnectionInfo, pongDelay, pingDelay time.Duration) {
	// 与交易数据管理器相同的实现
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	if connInfo.avgPongDelay == 0 {
		connInfo.avgPongDelay = pongDelay
	} else {
		connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*0.8 + float64(pongDelay)*0.2)
	}

	isTimeout := pongDelay > om.client.config.GetPingInterval()*4
	if isTimeout {
		connInfo.consecutiveTimeouts++
		log.Printf("⚠️ [OKX] 盘口数据连接 %s 超时 (第%d次): pong延迟=%.1fs",
			connName, connInfo.consecutiveTimeouts, pongDelay.Seconds())
	} else {
		connInfo.consecutiveTimeouts = 0
	}

	quality := 1.0
	idealDelay := om.client.config.GetPingInterval()
	if pongDelay > idealDelay {
		delayPenalty := float64(pongDelay-idealDelay) / float64(idealDelay*3)
		if delayPenalty > 1.0 {
			delayPenalty = 1.0
		}
		quality -= delayPenalty * 0.4
	}

	if connInfo.consecutiveTimeouts > 0 {
		timeoutPenalty := float64(connInfo.consecutiveTimeouts) / 5.0
		if timeoutPenalty > 1.0 {
			timeoutPenalty = 1.0
		}
		quality -= timeoutPenalty * 0.3
	}

	messageAge := now.Sub(connInfo.lastMessageTime)
	if messageAge > 30*time.Second {
		agePenalty := float64(messageAge-30*time.Second) / float64(60*time.Second)
		if agePenalty > 1.0 {
			agePenalty = 1.0
		}
		quality -= agePenalty * 0.3
	}

	if quality < 0 {
		quality = 0
	}

	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	if quality < 0.3 {
		log.Printf("💔 [OKX] 盘口数据连接 %s 质量极差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.6 {
		log.Printf("⚠️ [OKX] 盘口数据连接 %s 质量较差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.8 {
		log.Printf("📊 [OKX] 盘口数据连接 %s 质量一般: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	} else {
		log.Printf("✅ [OKX] 盘口数据连接 %s 质量良好: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	}
}

func (om *OrderbookDataManager) cleanupConnection(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	log.Printf("🧹 [OKX] 清理盘口数据连接 %s", connName)

	if connInfo.conn != nil {
		connInfo.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		connInfo.conn.Close()
		connInfo.conn = nil
	}

	select {
	case connInfo.heartbeatStop <- struct{}{}:
	default:
	}

	connInfo.state = StateDisconnected
}

// 为资金费率数据管理器添加相同的方法
func (fm *FundingDataManager) startEnhancedHeartbeat(connName string, connInfo *ConnectionInfo) {
	pingTicker := time.NewTicker(fm.client.config.GetPingInterval())
	defer pingTicker.Stop()

	healthTicker := time.NewTicker(fm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	log.Printf("💓 [OKX] 资金费率数据连接 %s 增强心跳启动 - ping间隔: %v, 健康检查间隔: %v",
		connName, fm.client.config.GetPingInterval(), fm.client.config.GetPingInterval()/2)

	for {
		select {
		case <-pingTicker.C:
			fm.sendActivePing(connName, connInfo)
		case <-healthTicker.C:
			if !fm.checkConnectionHealth(connName, connInfo) {
				log.Printf("💔 [OKX] 资金费率数据连接 %s 健康检查失败，关闭连接", connName)
				fm.cleanupConnection(connName, connInfo)
				return
			}
		case <-connInfo.heartbeatStop:
			log.Printf("💓 [OKX] 资金费率数据连接 %s 心跳停止", connName)
			return
		case <-fm.client.ctx.Done():
			log.Printf("💓 [OKX] 资金费率数据连接 %s 心跳因上下文取消而停止", connName)
			return
		}
	}
}

func (fm *FundingDataManager) sendActivePing(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state == StateDisconnected || state == StateStopped {
		return
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("okx-funding-ping")); err != nil {
		log.Printf("💔 [OKX] 资金费率数据连接 %s WebSocket ping发送失败: %v", connName, err)
		fm.cleanupConnection(connName, connInfo)
		return
	}

	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
		log.Printf("💔 [OKX] 资金费率数据连接 %s 应用层ping发送失败: %v", connName, err)
		fm.cleanupConnection(connName, connInfo)
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	log.Printf("💓 [OKX] 资金费率数据连接 %s 发送双重ping", connName)
}

func (fm *FundingDataManager) checkConnectionHealth(connName string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	connInfo.mutex.RUnlock()

	if conn == nil {
		log.Printf("🔍 [OKX] 资金费率数据连接 %s 健康检查：连接为空", connName)
		return false
	}

	if state == StateDisconnected || state == StateStopped {
		log.Printf("🔍 [OKX] 资金费率数据连接 %s 健康检查：状态为 %v", connName, state)
		return false
	}

	if state == StateConnected {
		now := time.Now()
		pongDelay := now.Sub(lastPongTime)
		pingDelay := now.Sub(lastPingTime)

		maxPongDelay := fm.client.config.GetPingInterval() * 10
		maxPingDelay := fm.client.config.GetPingInterval() * 5

		if pongDelay > maxPongDelay {
			log.Printf("💔 [OKX] 资金费率数据连接 %s pong超时: %.1fs (最大允许: %.1fs)",
				connName, pongDelay.Seconds(), maxPongDelay.Seconds())
			return false
		}

		if pingDelay > maxPingDelay {
			log.Printf("⚠️ [OKX] 资金费率数据连接 %s ping间隔过长: %.1fs", connName, pingDelay.Seconds())
		}

		fm.updateConnectionQuality(connName, connInfo, pongDelay, pingDelay)
	}

	return true
}

func (fm *FundingDataManager) updateConnectionQuality(connName string, connInfo *ConnectionInfo, pongDelay, pingDelay time.Duration) {
	// 与其他管理器相同的实现
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	if connInfo.avgPongDelay == 0 {
		connInfo.avgPongDelay = pongDelay
	} else {
		connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*0.8 + float64(pongDelay)*0.2)
	}

	isTimeout := pongDelay > fm.client.config.GetPingInterval()*4
	if isTimeout {
		connInfo.consecutiveTimeouts++
		log.Printf("⚠️ [OKX] 资金费率数据连接 %s 超时 (第%d次): pong延迟=%.1fs",
			connName, connInfo.consecutiveTimeouts, pongDelay.Seconds())
	} else {
		connInfo.consecutiveTimeouts = 0
	}

	quality := 1.0
	idealDelay := fm.client.config.GetPingInterval()
	if pongDelay > idealDelay {
		delayPenalty := float64(pongDelay-idealDelay) / float64(idealDelay*3)
		if delayPenalty > 1.0 {
			delayPenalty = 1.0
		}
		quality -= delayPenalty * 0.4
	}

	if connInfo.consecutiveTimeouts > 0 {
		timeoutPenalty := float64(connInfo.consecutiveTimeouts) / 5.0
		if timeoutPenalty > 1.0 {
			timeoutPenalty = 1.0
		}
		quality -= timeoutPenalty * 0.3
	}

	messageAge := now.Sub(connInfo.lastMessageTime)
	if messageAge > 30*time.Second {
		agePenalty := float64(messageAge-30*time.Second) / float64(60*time.Second)
		if agePenalty > 1.0 {
			agePenalty = 1.0
		}
		quality -= agePenalty * 0.3
	}

	if quality < 0 {
		quality = 0
	}

	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	if quality < 0.3 {
		log.Printf("💔 [OKX] 资金费率数据连接 %s 质量极差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.6 {
		log.Printf("⚠️ [OKX] 资金费率数据连接 %s 质量较差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connName, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.8 {
		log.Printf("📊 [OKX] 资金费率数据连接 %s 质量一般: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	} else {
		log.Printf("✅ [OKX] 资金费率数据连接 %s 质量良好: %.1f%% (平均延迟=%.1fs)",
			connName, quality*100, connInfo.avgPongDelay.Seconds())
	}
}

func (fm *FundingDataManager) cleanupConnection(connName string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	log.Printf("🧹 [OKX] 清理资金费率数据连接 %s", connName)

	if connInfo.conn != nil {
		connInfo.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		connInfo.conn.Close()
		connInfo.conn = nil
	}

	select {
	case connInfo.heartbeatStop <- struct{}{}:
	default:
	}

	connInfo.state = StateDisconnected
}
