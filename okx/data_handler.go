package main

import (
	"fmt"
	"time"

	"github.com/tidwall/gjson"
)

// OKXTradeData 交易数据结构
type OKXTradeData struct {
	Symbol    string `json:"symbol"`
	Price     string `json:"price"`
	Quantity  string `json:"quantity"`
	Side      string `json:"side"`
	Timestamp int64  `json:"timestamp"`
	TradeID   string `json:"trade_id"`
}

// OKXOrderbookData 盘口数据结构
type OKXOrderbookData struct {
	Symbol       string `json:"symbol"`
	BestBidPrice string `json:"best_bid_price"`
	BestBidSize  string `json:"best_bid_size"`
	BestAskPrice string `json:"best_ask_price"`
	BestAskSize  string `json:"best_ask_size"`
	Timestamp    int64  `json:"timestamp"`
	UpdateID     int64  `json:"update_id"`
}

// DataHandler 数据处理器接口
type DataHandler interface {
	HandleTradeMessage(message []byte) error
	HandleOrderbookMessage(message []byte) error
}

// OKXDataHandler OKX数据处理器
type OKXDataHandler struct {
	tradeCount     int64
	orderbookCount int64
	lastLogTime    time.Time
}

// NewOKXDataHandler 创建新的OKX数据处理器
func NewOKXDataHandler() *OKXDataHandler {
	return &OKXDataHandler{
		lastLogTime: time.Now(),
	}
}

// HandleTradeMessage 处理交易数据
func (h *OKXDataHandler) HandleTradeMessage(message []byte) error {
	// 解析数据
	dataArray := gjson.GetBytes(message, "data").Array()
	if len(dataArray) == 0 {
		// 如果没有数据，可能是订阅确认或其他非数据消息，直接返回nil
		return nil
	}

	// 获取交易对信息
	symbol := gjson.GetBytes(message, "arg.instId").String()
	if symbol == "" {
		return fmt.Errorf("无法获取交易对信息")
	}

	h.tradeCount++

	for _, data := range dataArray {
		_ = OKXTradeData{
			Symbol:    symbol,
			Price:     data.Get("px").String(),
			Quantity:  data.Get("sz").String(),
			Side:      data.Get("side").String(),
			Timestamp: data.Get("ts").Int(),
			TradeID:   data.Get("tradeId").String(),
		}

		// 记录统计日志
		h.logStatistics()
	}

	return nil
}

// HandleOrderbookMessage 处理盘口数据
func (h *OKXDataHandler) HandleOrderbookMessage(message []byte) error {
	// 解析数据
	dataArray := gjson.GetBytes(message, "data").Array()
	if len(dataArray) == 0 {
		// 如果没有数据，可能是订阅确认或其他非数据消息，直接返回nil
		return nil
	}

	// 获取交易对信息
	symbol := gjson.GetBytes(message, "arg.instId").String()
	if symbol == "" {
		return fmt.Errorf("无法获取交易对信息")
	}

	h.orderbookCount++

	for _, data := range dataArray {
		bids := data.Get("bids").Array()
		asks := data.Get("asks").Array()

		if len(bids) > 0 && len(asks) > 0 {
			_ = OKXOrderbookData{
				Symbol:       symbol,
				BestBidPrice: bids[0].Get("0").String(),
				BestBidSize:  bids[0].Get("1").String(),
				BestAskPrice: asks[0].Get("0").String(),
				BestAskSize:  asks[0].Get("1").String(),
				Timestamp:    data.Get("ts").Int(),
				UpdateID:     data.Get("seqId").Int(),
			}
		}

		// 记录统计日志
		h.logStatistics()
	}

	return nil
}

// logStatistics 记录统计信息 - 移除详细统计，统一在全局统计中显示
func (h *OKXDataHandler) logStatistics() {
	// 不再输出详细的数据统计，统一在main.go的全局统计中显示
}
