#!/bin/bash

# OKX永续合约数据订阅程序启动脚本

PROGRAM_NAME="okx-subscriber"
PID_FILE="$PROGRAM_NAME.pid"
LOG_DIR="logs"

# 创建日志目录
mkdir -p $LOG_DIR

# 函数：显示使用方法
show_usage() {
    echo "用法: $0 {start|stop|restart|status|logs|build}"
    echo "  start   - 启动程序"
    echo "  stop    - 停止程序"
    echo "  restart - 重启程序"
    echo "  status  - 查看程序状态"
    echo "  logs    - 查看日志"
    echo "  build   - 编译程序"
}

# 函数：编译程序
build_program() {
    echo "🔨 编译 $PROGRAM_NAME..."
    go mod tidy
    go build -o $PROGRAM_NAME .
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功"
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 函数：启动程序
start_program() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️ 程序已经在运行中 (PID: $PID)"
            return 1
        else
            echo "🧹 清理无效的PID文件"
            rm -f $PID_FILE
        fi
    fi

    echo "🚀 启动 $PROGRAM_NAME..."
    
    # 检查程序文件是否存在
    if [ ! -f $PROGRAM_NAME ]; then
        echo "📦 程序文件不存在，开始编译..."
        build_program
    fi
    
    # 后台启动程序
    nohup ./$PROGRAM_NAME > $LOG_DIR/output.log 2>&1 &
    PID=$!
    echo $PID > $PID_FILE
    
    # 等待一下确认程序启动
    sleep 2
    if ps -p $PID > /dev/null 2>&1; then
        echo "✅ 程序启动成功 (PID: $PID)"
        echo "📝 日志文件: $LOG_DIR/okx.log"
        echo "📝 输出日志: $LOG_DIR/output.log"
    else
        echo "❌ 程序启动失败"
        rm -f $PID_FILE
        return 1
    fi
}

# 函数：停止程序
stop_program() {
    if [ ! -f $PID_FILE ]; then
        echo "⚠️ PID文件不存在，尝试按进程名查找..."
        
        # 方法2：按进程名查找
        PIDS=$(pgrep -f "$PROGRAM_NAME")
        if [ -n "$PIDS" ]; then
            echo "🔍 找到进程: $PIDS"
            for pid in $PIDS; do
                echo "🛑 停止进程 $pid..."
                kill $pid
                sleep 2
                if ps -p $pid > /dev/null 2>&1; then
                    echo "💀 强制停止进程 $pid..."
                    kill -9 $pid
                fi
            done
            echo "✅ 程序已停止"
        else
            # 方法3：使用killall
            echo "🔍 尝试使用killall停止程序..."
            killall $PROGRAM_NAME 2>/dev/null
            if [ $? -eq 0 ]; then
                echo "✅ 程序已停止"
            else
                echo "⚠️ 没有找到运行中的程序"
            fi
        fi
        return 0
    fi

    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "🛑 停止程序 (PID: $PID)..."
        kill $PID
        
        # 等待程序优雅退出
        for i in {1..10}; do
            if ! ps -p $PID > /dev/null 2>&1; then
                break
            fi
            sleep 1
        done
        
        # 如果还在运行，强制停止
        if ps -p $PID > /dev/null 2>&1; then
            echo "💀 强制停止程序..."
            kill -9 $PID
        fi
        
        rm -f $PID_FILE
        echo "✅ 程序已停止"
    else
        echo "⚠️ 程序未运行"
        rm -f $PID_FILE
    fi
}

# 函数：查看程序状态
show_status() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "✅ 程序正在运行 (PID: $PID)"
            echo "📊 进程信息:"
            ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
        else
            echo "❌ 程序未运行 (PID文件存在但进程不存在)"
            rm -f $PID_FILE
        fi
    else
        echo "❌ 程序未运行"
    fi
}

# 函数：查看日志
show_logs() {
    if [ -f "$LOG_DIR/okx.log" ]; then
        echo "📝 显示最近50行日志:"
        tail -50 "$LOG_DIR/okx.log"
    else
        echo "⚠️ 日志文件不存在"
    fi
}

# 函数：重启程序
restart_program() {
    echo "🔄 重启程序..."
    stop_program
    sleep 2
    start_program
}

# 主逻辑
case "$1" in
    start)
        start_program
        ;;
    stop)
        stop_program
        ;;
    restart)
        restart_program
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    build)
        build_program
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit 0 