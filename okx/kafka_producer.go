package main

import (
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

// KafkaProducer Kafka生产者
type KafkaProducer struct {
	producer *kafka.Producer
	config   *KafkaConfig

	// 统计计数器
	tradesSent     int64
	orderbooksSent int64
	fundingSent    int64
	totalSent      int64
	errorCount     int64

	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// KafkaMessage Kafka消息结构
type KafkaMessage struct {
	Exchange  string      `json:"exchange"`
	Symbol    string      `json:"symbol"`
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// NewKafkaProducer 创建新的Kafka生产者
func NewKafkaProducer(config *KafkaConfig) (*KafkaProducer, error) {
	if !config.Enabled {
		return nil, nil
	}

	// 构建Kafka配置
	configMap := &kafka.ConfigMap{
		"bootstrap.servers":                     fmt.Sprintf("%s", config.Brokers[0]),
		"security.protocol":                     config.SecurityProtocol,
		"sasl.mechanism":                        config.SASLMechanism,
		"sasl.username":                         config.SASLUsername,
		"sasl.password":                         config.SASLPassword,
		"acks":                                  config.ProducerConfig.Acks,
		"retries":                               config.ProducerConfig.Retries,
		"batch.size":                            config.ProducerConfig.BatchSize,
		"linger.ms":                             config.ProducerConfig.LingerMs,
		"queue.buffering.max.kbytes":            config.ProducerConfig.BufferMemory / 1024, // 转换为KB
		"queue.buffering.max.messages":          1000000,                                   // 增加队列消息数量限制
		"compression.type":                      "snappy",
		"max.in.flight.requests.per.connection": 5,
		"request.timeout.ms":                    30000,
		"delivery.timeout.ms":                   120000,
	}

	// 创建生产者
	producer, err := kafka.NewProducer(configMap)
	if err != nil {
		return nil, fmt.Errorf("创建Kafka生产者失败: %v", err)
	}

	kp := &KafkaProducer{
		producer: producer,
		config:   config,
		stopChan: make(chan struct{}),
	}

	// 启动事件处理协程
	kp.wg.Add(1)
	go kp.handleEvents()

	// 启动统计协程
	kp.wg.Add(1)
	go kp.statisticsLoop()

	fmt.Printf("🚀 [OKX] Kafka生产者已启动 - brokers: %v, topics: trades=%s, orderbooks=%s, funding=%s\n",
		config.Brokers, config.Topics.Trades, config.Topics.Orderbooks, config.Topics.Funding)

	return kp, nil
}

// SendTradeData 发送交易数据
func (kp *KafkaProducer) SendTradeData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "okx",
		Symbol:    symbol,
		Type:      "trade",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Trades, message, &kp.tradesSent)
}

// SendOrderbookData 发送盘口数据
func (kp *KafkaProducer) SendOrderbookData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "okx",
		Symbol:    symbol,
		Type:      "orderbook",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Orderbooks, message, &kp.orderbooksSent)
}

// SendFundingData 发送资金费率数据
func (kp *KafkaProducer) SendFundingData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "okx",
		Symbol:    symbol,
		Type:      "funding",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Funding, message, &kp.fundingSent)
}

// sendMessage 发送消息到Kafka
func (kp *KafkaProducer) sendMessage(topic string, message KafkaMessage, counter *int64) {
	jsonData, err := json.Marshal(message)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		fmt.Printf("❌ [OKX] 序列化Kafka消息失败: %v\n", err)
		return
	}

	kafkaMessage := &kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          jsonData,
		Key:            []byte(message.Symbol),
	}

	// 异步发送
	err = kp.producer.Produce(kafkaMessage, nil)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		fmt.Printf("❌ [OKX] 发送Kafka消息失败: %v (topic: %s)\n", err, topic)
		return
	}

	atomic.AddInt64(counter, 1)
	atomic.AddInt64(&kp.totalSent, 1)
}

// handleEvents 处理Kafka事件
func (kp *KafkaProducer) handleEvents() {
	defer kp.wg.Done()

	for {
		select {
		case <-kp.stopChan:
			return
		case e := <-kp.producer.Events():
			switch ev := e.(type) {
			case *kafka.Message:
				if ev.TopicPartition.Error != nil {
					atomic.AddInt64(&kp.errorCount, 1)
					fmt.Printf("❌ [OKX] Kafka消息发送失败: %v (topic: %s)\n",
						ev.TopicPartition.Error, *ev.TopicPartition.Topic)
				}
			case kafka.Error:
				atomic.AddInt64(&kp.errorCount, 1)
				fmt.Printf("❌ [OKX] Kafka错误: %v\n", ev)
			}
		}
	}
}

// statisticsLoop 统计循环
func (kp *KafkaProducer) statisticsLoop() {
	defer kp.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	var lastTrades, lastOrderbooks, lastFunding, lastTotal int64
	startTime := time.Now()

	for {
		select {
		case <-kp.stopChan:
			return
		case <-ticker.C:
			trades := atomic.LoadInt64(&kp.tradesSent)
			orderbooks := atomic.LoadInt64(&kp.orderbooksSent)
			funding := atomic.LoadInt64(&kp.fundingSent)
			total := atomic.LoadInt64(&kp.totalSent)
			errors := atomic.LoadInt64(&kp.errorCount)

			// 计算速率
			tradeRate := trades - lastTrades
			orderbookRate := orderbooks - lastOrderbooks
			fundingRate := funding - lastFunding
			totalRate := total - lastTotal

			fmt.Printf("📊 [OKX] Kafka发送统计 - 交易: %d条 (%d/10s), 盘口: %d条 (%d/10s), 资金费率: %d条 (%d/10s), 总计: %d条 (%d/10s), 错误: %d, 运行时间: %v\n",
				trades, tradeRate, orderbooks, orderbookRate, funding, fundingRate, total, totalRate, errors, time.Since(startTime).Round(time.Second))

			lastTrades = trades
			lastOrderbooks = orderbooks
			lastFunding = funding
			lastTotal = total
		}
	}
}

// GetStats 获取统计信息
func (kp *KafkaProducer) GetStats() (trades, orderbooks, funding, total, errors int64) {
	if kp == nil {
		return 0, 0, 0, 0, 0
	}
	return atomic.LoadInt64(&kp.tradesSent),
		atomic.LoadInt64(&kp.orderbooksSent),
		atomic.LoadInt64(&kp.fundingSent),
		atomic.LoadInt64(&kp.totalSent),
		atomic.LoadInt64(&kp.errorCount)
}

// Close 关闭Kafka生产者
func (kp *KafkaProducer) Close() {
	if kp == nil || kp.producer == nil {
		return
	}

	fmt.Println("🔄 [OKX] 正在关闭Kafka生产者...")

	// 停止统计和事件处理协程
	close(kp.stopChan)

	// 等待所有消息发送完成
	kp.producer.Flush(5000) // 5秒超时

	// 关闭生产者
	kp.producer.Close()

	// 等待协程结束
	kp.wg.Wait()

	// 输出最终统计
	trades, orderbooks, funding, total, errors := kp.GetStats()
	fmt.Printf("📊 [OKX] Kafka生产者已关闭 - 最终统计: 交易=%d, 盘口=%d, 资金费率=%d, 总计=%d, 错误=%d\n",
		trades, orderbooks, funding, total, errors)
}
