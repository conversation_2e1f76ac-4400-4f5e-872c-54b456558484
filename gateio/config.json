{"exchange": "gateio", "websocket_url": "wss://fx-ws.gateio.ws/v4/ws/usdt", "rest_api_url": "https://api.gateio.ws", "reconnect_delay": "3s", "ping_interval": "25s", "log_file": "logs/gateio.log", "log_level": "info", "kafka": {"enabled": true, "brokers": ["43.133.193.167:9092"], "security_protocol": "SASL_PLAINTEXT", "sasl_mechanism": "PLAIN", "sasl_username": "kafka", "sasl_password": "Abc112311", "topics": {"trades": "gateio-trades", "orderbooks": "gateio-orderbooks", "funding": "gateio-funding"}, "producer_config": {"acks": "1", "retries": 3, "batch_size": 65536, "linger_ms": 5, "buffer_memory": 134217728}}}