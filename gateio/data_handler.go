package main

import (
	"github.com/sirupsen/logrus"
)

// GateioTradeData 交易数据结构
type GateioTradeData struct {
	Symbol    string `json:"symbol"`
	Price     string `json:"price"`
	Quantity  string `json:"quantity"`
	Side      string `json:"side"`
	Timestamp int64  `json:"timestamp"`
	TradeID   int64  `json:"trade_id"`
}

// GateioOrderbookData 盘口数据结构
type GateioOrderbookData struct {
	Symbol       string `json:"symbol"`
	BestBidPrice string `json:"best_bid_price"`
	BestBidSize  string `json:"best_bid_size"`
	BestAskPrice string `json:"best_ask_price"`
	BestAskSize  string `json:"best_ask_size"`
	Timestamp    int64  `json:"timestamp"`
	UpdateID     int64  `json:"update_id"`
}

// GateIODataHandler Gate.io数据处理器接口
type GateIODataHandler interface {
	HandleTradeMessage(message []byte)
	HandleOrderbookMessage(message []byte)
	HandleFundingMessage(message []byte)
}

// DefaultGateIODataHandler 默认数据处理器
type DefaultGateIODataHandler struct {
	logger *logrus.Logger
}

// HandleTradeMessage 处理交易消息
func (h *DefaultGateIODataHandler) HandleTradeMessage(message []byte) {
	// 这里可以添加具体的交易数据处理逻辑
	// 例如：发送到Kafka、存储到数据库等
	h.logger.Debug("📈 [Gate.io] 处理交易数据")
}

// HandleOrderbookMessage 处理盘口消息
func (h *DefaultGateIODataHandler) HandleOrderbookMessage(message []byte) {
	// 这里可以添加具体的盘口数据处理逻辑
	// 例如：发送到Kafka、存储到数据库等
	h.logger.Debug("📊 [Gate.io] 处理盘口数据")
}

// HandleFundingMessage 处理资金费率消息
func (h *DefaultGateIODataHandler) HandleFundingMessage(message []byte) {
	// 这里可以添加具体的资金费率数据处理逻辑
	// 例如：发送到Kafka、存储到数据库等
	h.logger.Debug("💰 [Gate.io] 处理资金费率数据")
}

// NewDefaultDataHandler 创建默认数据处理器
func NewDefaultDataHandler(logger *logrus.Logger) GateIODataHandler {
	return &DefaultGateIODataHandler{
		logger: logger,
	}
}
