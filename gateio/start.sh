#!/bin/bash

# Gate.io永续合约数据订阅程序启动脚本

PROGRAM_NAME="gateio-subscriber"
PID_FILE="$PROGRAM_NAME.pid"
LOG_DIR="logs"

# 创建日志目录
mkdir -p $LOG_DIR

# 函数：显示使用方法
show_usage() {
    echo "用法: $0 {start|stop|restart|status|logs|build}"
    echo "  start   - 启动程序"
    echo "  stop    - 停止程序"
    echo "  restart - 重启程序"
    echo "  status  - 查看程序状态"
    echo "  logs    - 查看日志"
    echo "  build   - 编译程序"
}

# 函数：编译程序
build_program() {
    echo "🔨 编译 $PROGRAM_NAME..."
    go mod tidy
    go build -o $PROGRAM_NAME .
    if [ $? -eq 0 ]; then
        echo "✅ 编译成功"
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 函数：启动程序
start_program() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️ 程序已经在运行中 (PID: $PID)"
            return 1
        else
            echo "🧹 清理无效的PID文件"
            rm -f $PID_FILE
        fi
    fi

    echo "🚀 启动 $PROGRAM_NAME..."
    nohup ./$PROGRAM_NAME > /dev/null 2>&1 &
    PID=$!
    echo $PID > $PID_FILE
    
    # 等待一下检查程序是否成功启动
    sleep 2
    if ps -p $PID > /dev/null 2>&1; then
        echo "✅ 程序启动成功 (PID: $PID)"
        echo "📋 查看日志: tail -f $LOG_DIR/gateio.log"
    else
        echo "❌ 程序启动失败"
        rm -f $PID_FILE
        return 1
    fi
}

# 函数：停止程序
stop_program() {
    echo "🛑 停止 $PROGRAM_NAME..."
    
    # 方法1：通过PID文件停止
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            kill $PID
            sleep 2
            if ps -p $PID > /dev/null 2>&1; then
                kill -9 $PID
                sleep 1
            fi
            if ! ps -p $PID > /dev/null 2>&1; then
                echo "✅ 程序已停止"
                rm -f $PID_FILE
                return 0
            fi
        fi
        rm -f $PID_FILE
    fi
    
    # 方法2：按进程名查找并停止
    PIDS=$(pgrep -f "$PROGRAM_NAME")
    if [ -n "$PIDS" ]; then
        echo "🔍 找到进程: $PIDS"
        kill $PIDS
        sleep 2
        PIDS=$(pgrep -f "$PROGRAM_NAME")
        if [ -n "$PIDS" ]; then
            kill -9 $PIDS
            sleep 1
        fi
    fi
    
    # 方法3：使用killall
    killall $PROGRAM_NAME 2>/dev/null
    
    # 检查是否还有进程在运行
    if pgrep -f "$PROGRAM_NAME" > /dev/null; then
        echo "⚠️ 可能还有进程在运行，请手动检查"
        return 1
    else
        echo "✅ 所有相关进程已停止"
        return 0
    fi
}

# 函数：查看程序状态
show_status() {
    if [ -f $PID_FILE ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            echo "✅ 程序正在运行 (PID: $PID)"
            echo "📊 进程信息:"
            ps -p $PID -o pid,ppid,cmd,etime
        else
            echo "❌ PID文件存在但进程未运行"
            rm -f $PID_FILE
        fi
    else
        echo "❌ 程序未运行"
    fi
    
    # 检查是否有其他相关进程
    OTHER_PIDS=$(pgrep -f "$PROGRAM_NAME")
    if [ -n "$OTHER_PIDS" ]; then
        echo "⚠️ 发现其他相关进程: $OTHER_PIDS"
    fi
}

# 函数：查看日志
show_logs() {
    if [ -f "$LOG_DIR/gateio.log" ]; then
        echo "📋 最近的日志内容:"
        tail -50 "$LOG_DIR/gateio.log"
        echo ""
        echo "💡 实时查看日志: tail -f $LOG_DIR/gateio.log"
    else
        echo "❌ 日志文件不存在: $LOG_DIR/gateio.log"
    fi
}

# 主逻辑
case "$1" in
    start)
        build_program && start_program
        ;;
    stop)
        stop_program
        ;;
    restart)
        stop_program
        sleep 2
        build_program && start_program
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    build)
        build_program
        ;;
    *)
        show_usage
        exit 1
        ;;
esac

exit $? 