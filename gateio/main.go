package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	state           ConnectionState
	lastPingTime    time.Time
	lastPongTime    time.Time
	reconnectCount  int
	mutex           sync.RWMutex
	conn            *websocket.Conn
	heartbeatStop   chan struct{}
	lastMessageTime time.Time
	messageCount    int64
}

// makeHTTPRequest 发送HTTP请求
func makeHTTPRequest(url string) (*http.Response, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	return client.Get(url)
}

// GateIOClient Gate.io客户端
type GateIOClient struct {
	config      *Config
	dataHandler GateIODataHandler
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 数据管理器
	tradeManager     *TradeDataManager
	orderbookManager *OrderbookDataManager
	fundingManager   *FundingDataManager

	// Kafka生产者
	kafkaProducer *KafkaProducer

	// 全局统计
	totalTradeCount     int64
	totalOrderbookCount int64
	totalFundingCount   int64
	startTime           time.Time
}

// TradeDataManager 交易数据管理器
type TradeDataManager struct {
	client      *GateIOClient
	connections map[string]*websocket.Conn
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type OrderbookDataManager struct {
	client         *GateIOClient
	connections    map[string]*websocket.Conn
	connMutex      sync.RWMutex
	orderbookCount int64
}

// FundingDataManager 资金费率数据管理器
type FundingDataManager struct {
	client       *GateIOClient
	connections  map[string]*websocket.Conn
	connMutex    sync.RWMutex
	fundingCount int64
}

// NewGateIOClient 创建Gate.io客户端
func NewGateIOClient(config *Config, dataHandler GateIODataHandler) *GateIOClient {
	ctx, cancel := context.WithCancel(context.Background())

	client := &GateIOClient{
		config:      config,
		dataHandler: dataHandler,
		ctx:         ctx,
		cancel:      cancel,
		startTime:   time.Now(),
	}

	client.tradeManager = &TradeDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.orderbookManager = &OrderbookDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.fundingManager = &FundingDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	// 初始化Kafka生产者
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	kafkaProducer, err := NewKafkaProducer(&config.Kafka, logger)
	if err != nil {
		log.Printf("❌ [Gate.io] 初始化Kafka生产者失败: %v", err)
	} else if kafkaProducer != nil {
		client.kafkaProducer = kafkaProducer
		log.Printf("✅ [Gate.io] Kafka生产者初始化成功")
	} else {
		log.Printf("ℹ️ [Gate.io] Kafka未启用，跳过Kafka生产者初始化")
	}

	return client
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetAllSymbols 获取所有活跃交易对
func (c *GateIOClient) GetAllSymbols() error {
	symbols, err := fetchActiveSymbols(c.config)
	if err != nil {
		return err
	}
	c.symbols = symbols
	return nil
}

// SubscribeData 订阅数据
func (c *GateIOClient) SubscribeData() error {
	log.Printf("🚀 开始分批订阅Gate.io数据流...")

	if len(c.symbols) == 0 {
		return fmt.Errorf("没有可订阅的交易对")
	}

	batchSize := 30 // Gate.io建议每批30个交易对
	totalBatches := (len(c.symbols) + batchSize - 1) / batchSize

	log.Printf("📊 [Gate.io] 分批订阅策略 - 总交易对: %d, 批次大小: %d, 总批次: %d",
		len(c.symbols), batchSize, totalBatches)

	// 显示前几个交易对作为示例
	sampleSymbols := c.symbols
	if len(sampleSymbols) > 5 {
		sampleSymbols = sampleSymbols[:5]
	}
	log.Printf("📋 [Gate.io] 交易对示例: %v", sampleSymbols)

	batchCount := 0
	successfulBatches := 0

	for i := 0; i < len(c.symbols); i += batchSize {
		end := i + batchSize
		if end > len(c.symbols) {
			end = len(c.symbols)
		}

		batch := c.symbols[i:end]
		batchCount++

		log.Printf("📡 [Gate.io] 处理批次 %d/%d - 交易对数量: %d", batchCount, totalBatches, len(batch))

		// 为每批创建三个连接：交易数据、盘口数据、资金费率数据
		tradeConnName := fmt.Sprintf("trade_batch_%d", batchCount)
		orderbookConnName := fmt.Sprintf("orderbook_batch_%d", batchCount)
		fundingConnName := fmt.Sprintf("funding_batch_%d", batchCount)

		// 并发启动三个连接，提高效率
		var batchWg sync.WaitGroup
		batchErrors := make(chan error, 3)

		// 订阅交易数据
		batchWg.Add(1)
		go func(connName string, symbols []string) {
			defer batchWg.Done()
			log.Printf("🔗 [Gate.io] 启动交易数据连接: %s", connName)
			c.wg.Add(1)
			go c.tradeManager.subscribeTradeData(connName, symbols)
		}(tradeConnName, batch)

		// 订阅盘口数据
		batchWg.Add(1)
		go func(connName string, symbols []string) {
			defer batchWg.Done()
			time.Sleep(100 * time.Millisecond) // 小延迟避免同时连接
			log.Printf("📊 [Gate.io] 启动盘口数据连接: %s", connName)
			c.wg.Add(1)
			go c.orderbookManager.subscribeOrderbookData(connName, symbols)
		}(orderbookConnName, batch)

		// 订阅资金费率数据
		batchWg.Add(1)
		go func(connName string, symbols []string) {
			defer batchWg.Done()
			time.Sleep(200 * time.Millisecond) // 小延迟避免同时连接
			log.Printf("💰 [Gate.io] 启动资金费率连接: %s", connName)
			c.wg.Add(1)
			go c.fundingManager.subscribeFundingData(connName, symbols)
		}(fundingConnName, batch)

		// 等待当前批次的连接启动完成
		batchWg.Wait()
		close(batchErrors)

		// 检查是否有错误
		hasError := false
		for err := range batchErrors {
			if err != nil {
				log.Printf("❌ [Gate.io] 批次 %d 启动失败: %v", batchCount, err)
				hasError = true
			}
		}

		if !hasError {
			successfulBatches++
		}

		// 批次间延迟，避免同时创建太多连接
		if batchCount < totalBatches {
			log.Printf("⏰ [Gate.io] 批次 %d 完成，等待 800ms 后继续下一批次...", batchCount)
			time.Sleep(800 * time.Millisecond)
		}
	}

	log.Printf("✅ [Gate.io] 所有分批订阅已启动 - 成功批次: %d/%d, 总连接数: %d",
		successfulBatches, totalBatches, successfulBatches*3)

	// 启动全局统计和监控
	c.wg.Add(2)
	go c.printGlobalStatistics()
	go c.monitorConnections()

	return nil
}

// subscribeTradeData 订阅交易数据
func (tm *TradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.client.wg.Done()

	// 根据Gate.io官方文档，payload直接使用交易对名称
	// 格式应该是 BTC_USD 而不是 futures.trades.BTC_USD
	tm.subscribeWithRetry(connName, symbols, tm.handleTradeMessage)
}

// subscribeOrderbookData 订阅盘口数据
func (om *OrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.client.wg.Done()

	// 根据Gate.io官方文档，payload直接使用交易对名称
	// 格式应该是 BTC_USD 而不是 futures.order_book.BTC_USD
	om.subscribeWithRetry(connName, symbols, om.handleOrderbookMessage)
}

// subscribeFundingData 订阅资金费率数据
func (fm *FundingDataManager) subscribeFundingData(connName string, symbols []string) {
	defer fm.client.wg.Done()

	// 根据Gate.io官方文档，资金费率数据从futures.tickers频道获取
	// 直接传递交易对列表，不需要构建特殊的频道名称
	fm.subscribeWithRetry(connName, symbols, fm.handleFundingMessage)
}

// subscribeWithRetry 资金费率数据版本
func (fm *FundingDataManager) subscribeWithRetry(connName string, channels []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := fm.client.config.GetReconnectDelay()

	for {
		select {
		case <-fm.client.ctx.Done():
			log.Printf("🛑 资金费率连接 %s 收到停止信号", connName)
			return
		default:
			if fm.connectAndListen(connName, channels, handler, &reconnectAttempts) {
				return
			}

			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 资金费率连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 资金费率连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-fm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
			}
		}
	}
}

// connectAndListen 资金费率数据连接和监听
func (fm *FundingDataManager) connectAndListen(connName string, channels []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接资金费率数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(channels), *reconnectAttempts+1)

	conn, _, err := websocket.DefaultDialer.Dial(fm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 资金费率数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	*reconnectAttempts = 0

	fm.connMutex.Lock()
	fm.connections[connName] = conn
	fm.connMutex.Unlock()

	defer func() {
		fm.connMutex.Lock()
		delete(fm.connections, connName)
		fm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 资金费率数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 资金费率数据连接 %s 建立成功", connName)

	// 根据Gate.io官方文档，使用ticker频道获取资金费率
	// ticker数据包含funding_rate字段
	subscribeMsg := map[string]interface{}{
		"time":    time.Now().Unix(),
		"channel": "futures.tickers",
		"event":   "subscribe",
		"payload": channels, // 传递交易对列表
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送资金费率数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 资金费率数据订阅消息已发送 %s", connName)

	go fm.startPing(conn, connName)

	for {
		select {
		case <-fm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 资金费率数据读取消息失败 %s: %v", connName, err)
				return false
			}
			handler(message)
		}
	}
}

// startPing 资金费率数据心跳
func (fm *FundingDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(fm.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(fm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Gate.io] 资金费率连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Gate.io] 资金费率连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Gate.io] 资金费率连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-fm.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("gateio-funding-ping")); err != nil {
				log.Printf("💔 [Gate.io] 资金费率WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 发送应用层ping（使用正确的Gate.io格式）
			pingMsg := map[string]interface{}{
				"time":    time.Now().Unix(),
				"channel": "futures.ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Gate.io] 资金费率应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Gate.io] 资金费率连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := fm.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Gate.io] 资金费率连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Gate.io] 资金费率连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// handleFundingMessage 处理资金费率数据（从ticker数据中提取）
func (fm *FundingDataManager) handleFundingMessage(message []byte) {
	// 根据Gate.io新格式，检查event和channel字段
	event := gjson.GetBytes(message, "event").String()
	channel := gjson.GetBytes(message, "channel").String()

	// 检查是否是心跳响应
	if channel == "futures.pong" {
		log.Printf("💓 [Gate.io] 收到ticker数据pong响应")
		return
	}

	// 检查是否是订阅确认
	if event == "subscribe" && channel == "futures.tickers" {
		result := gjson.GetBytes(message, "result")
		if result.Exists() {
			status := gjson.Get(result.Raw, "status").String()
			if status == "success" {
				log.Printf("✅ [Gate.io] ticker数据订阅确认成功")
			} else {
				log.Printf("❌ [Gate.io] ticker数据订阅失败: %s", string(message))
			}
		}
		return
	}

	// 检查是否是ticker数据更新（包含资金费率）
	if event == "update" && channel == "futures.tickers" {
		result := gjson.GetBytes(message, "result")
		if result.Exists() && result.IsArray() && len(result.Array()) > 0 {
			atomic.AddInt64(&fm.fundingCount, 1)
			atomic.AddInt64(&fm.client.totalFundingCount, 1)

			// 发送到Kafka
			if fm.client.kafkaProducer != nil {
				// 提取交易对信息
				if len(result.Array()) > 0 {
					firstTicker := result.Array()[0]
					contract := gjson.Get(firstTicker.Raw, "contract").String()
					if contract != "" {
						fm.client.kafkaProducer.SendFundingData(contract, result.Value())
					}
				}
			}

			// 解析ticker数据中的资金费率
			tickersProcessed := 0
			for _, ticker := range result.Array() {
				contract := gjson.Get(ticker.Raw, "contract").String()

				// 数据验证
				if contract == "" {
					continue
				}

				// 只在特定条件下打印详细信息，避免日志过多
				// 不再输出单独的交易统计，统一在全局统计中显示
				tickersProcessed++
			}

			// 调用数据处理器
			if fm.client.dataHandler != nil {
				fm.client.dataHandler.HandleTradeMessage(message)
			}

			// 不再输出单独的Ticker统计，统一在全局统计中显示
		}
	} else if event != "" || channel != "" {
		// 记录未处理的消息类型，用于调试
		log.Printf("🔍 [Gate.io] 未处理的Ticker消息 - Event: %s, Channel: %s", event, channel)
	}
}

// subscribeWithRetry 通用订阅方法（带重连机制）
func (tm *TradeDataManager) subscribeWithRetry(connName string, channels []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := tm.client.config.GetReconnectDelay()

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 交易数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if tm.connectAndListen(connName, channels, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 交易数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 交易数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-tm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 盘口数据版本
func (om *OrderbookDataManager) subscribeWithRetry(connName string, channels []string, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := om.client.config.GetReconnectDelay()

	for {
		select {
		case <-om.client.ctx.Done():
			log.Printf("🛑 盘口数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if om.connectAndListen(connName, channels, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 盘口数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 盘口数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-om.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// connectAndListen 交易数据连接和监听
func (tm *TradeDataManager) connectAndListen(connName string, channels []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接交易数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(channels), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(tm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 交易数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	tm.connMutex.Lock()
	tm.connections[connName] = conn
	tm.connMutex.Unlock()

	defer func() {
		tm.connMutex.Lock()
		delete(tm.connections, connName)
		tm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 交易数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 交易数据连接 %s 建立成功", connName)

	// 根据Gate.io官方文档，使用正确的订阅格式
	// 交易数据使用 futures.trades 频道
	subscribeMsg := map[string]interface{}{
		"time":    time.Now().Unix(),
		"channel": "futures.trades",
		"event":   "subscribe",
		"payload": channels, // 直接传递交易对列表
	}

	// 添加调试日志
	log.Printf("📤 发送交易数据订阅请求 %s: %+v", connName, subscribeMsg)

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送交易数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 交易数据订阅消息已发送 %s，交易对数量: %d", connName, len(channels))

	// 启动心跳
	go tm.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-tm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 交易数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// connectAndListen 盘口数据连接和监听
func (om *OrderbookDataManager) connectAndListen(connName string, channels []string, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接盘口数据 %s，订阅 %d 个频道 (尝试 %d)", connName, len(channels), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(om.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 盘口数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	om.connMutex.Lock()
	om.connections[connName] = conn
	om.connMutex.Unlock()

	defer func() {
		om.connMutex.Lock()
		delete(om.connections, connName)
		om.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 盘口数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 盘口数据连接 %s 建立成功", connName)

	// 根据Gate.io官方文档，使用正确的订阅格式
	// 盘口数据使用 futures.book_ticker 频道（最佳买卖价）
	subscribeMsg := map[string]interface{}{
		"time":    time.Now().Unix(),
		"channel": "futures.book_ticker",
		"event":   "subscribe",
		"payload": channels, // 直接传递交易对列表
	}

	// 添加调试日志
	log.Printf("📤 发送盘口数据订阅请求 %s: %+v", connName, subscribeMsg)

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送盘口数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	log.Printf("📡 盘口数据订阅消息已发送 %s，交易对数量: %d", connName, len(channels))

	// 启动心跳
	go om.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-om.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 盘口数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// startPing 交易数据心跳
func (tm *TradeDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(tm.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(tm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Gate.io] 交易数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Gate.io] 交易数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Gate.io] 交易数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-tm.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("gateio-trade-ping")); err != nil {
				log.Printf("💔 [Gate.io] 交易数据WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 发送应用层ping（使用正确的Gate.io格式）
			pingMsg := map[string]interface{}{
				"time":    time.Now().Unix(),
				"channel": "futures.ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Gate.io] 交易数据应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Gate.io] 交易数据连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := tm.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Gate.io] 交易数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Gate.io] 交易数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// startPing 盘口数据心跳
func (om *OrderbookDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(om.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(om.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Gate.io] 盘口数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Gate.io] 盘口数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Gate.io] 盘口数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-om.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("gateio-orderbook-ping")); err != nil {
				log.Printf("💔 [Gate.io] 盘口数据WebSocket ping失败 %s: %v", connName, err)
				return
			}

			// 发送应用层ping（使用正确的Gate.io格式）
			pingMsg := map[string]interface{}{
				"time":    time.Now().Unix(),
				"channel": "futures.ping",
			}
			if err := conn.WriteJSON(pingMsg); err != nil {
				log.Printf("💔 [Gate.io] 盘口数据应用层ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Gate.io] 盘口数据连接 %s 发送增强双重ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := om.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Gate.io] 盘口数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Gate.io] 盘口数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// handleTradeMessage 处理交易数据
func (tm *TradeDataManager) handleTradeMessage(message []byte) {
	// 根据Gate.io新格式，检查event和channel字段
	event := gjson.GetBytes(message, "event").String()
	channel := gjson.GetBytes(message, "channel").String()

	// 检查是否是心跳响应
	if channel == "futures.pong" {
		log.Printf("💓 [Gate.io] 收到交易数据pong响应")
		return
	}

	// 检查是否是订阅确认
	if event == "subscribe" && channel == "futures.trades" {
		result := gjson.GetBytes(message, "result")
		if result.Exists() {
			status := gjson.Get(result.Raw, "status").String()
			if status == "success" {
				log.Printf("✅ [Gate.io] 交易数据订阅确认成功")
			} else {
				log.Printf("❌ [Gate.io] 交易数据订阅失败: %s", string(message))
			}
		}
		return
	}

	// 检查是否是交易数据更新
	if event == "update" && channel == "futures.trades" {
		// 检查是否有实际的交易数据
		result := gjson.GetBytes(message, "result")
		if result.Exists() && result.IsArray() && len(result.Array()) > 0 {
			atomic.AddInt64(&tm.tradeCount, 1)
			atomic.AddInt64(&tm.client.totalTradeCount, 1)

			// 发送到Kafka
			if tm.client.kafkaProducer != nil {
				// 提取交易对信息
				if len(result.Array()) > 0 {
					firstTrade := result.Array()[0]
					contract := gjson.Get(firstTrade.Raw, "contract").String()
					if contract != "" {
						tm.client.kafkaProducer.SendTradeData(contract, result.Value())
					}
				}
			}

			// 只在特定条件下打印详细信息，避免日志过多
			// 不再输出单独的交易统计，统一在全局统计中显示
			tradesProcessed := 0
			for _, trade := range result.Array() {
				contract := gjson.Get(trade.Raw, "contract").String()
				price := gjson.Get(trade.Raw, "price").String()
				id := gjson.Get(trade.Raw, "id").Int()

				// 数据验证
				if contract == "" || price == "" || id == 0 {
					continue
				}

				tradesProcessed++
			}

			// 调用数据处理器
			if tm.client.dataHandler != nil {
				tm.client.dataHandler.HandleTradeMessage(message)
			}

			// 不再输出单独的交易统计，统一在全局统计中显示
		}
	} else if event != "" || channel != "" {
		// 记录未处理的消息类型，用于调试
		log.Printf("🔍 [Gate.io] 未处理的交易消息 - Event: %s, Channel: %s", event, channel)
	}
}

// handleOrderbookMessage 处理盘口数据
func (om *OrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 根据Gate.io新格式，检查event和channel字段
	event := gjson.GetBytes(message, "event").String()
	channel := gjson.GetBytes(message, "channel").String()

	// 检查是否是心跳响应
	if channel == "futures.pong" {
		log.Printf("💓 [Gate.io] 收到盘口数据pong响应")
		return
	}

	// 检查是否是订阅确认
	if event == "subscribe" && channel == "futures.book_ticker" {
		result := gjson.GetBytes(message, "result")
		if result.Exists() {
			status := gjson.Get(result.Raw, "status").String()
			if status == "success" {
				log.Printf("✅ [Gate.io] 盘口数据订阅确认成功")
			} else {
				log.Printf("❌ [Gate.io] 盘口数据订阅失败: %s", string(message))
			}
		}
		return
	}

	// 检查是否是盘口数据更新
	if event == "update" && channel == "futures.book_ticker" {
		// 检查是否有实际的盘口数据
		result := gjson.GetBytes(message, "result")
		if result.Exists() {
			atomic.AddInt64(&om.orderbookCount, 1)
			atomic.AddInt64(&om.client.totalOrderbookCount, 1)

			// 解析盘口数据（根据Gate.io官方文档格式）
			updateId := gjson.Get(result.Raw, "u").Int()    // 深度的ID
			contract := gjson.Get(result.Raw, "s").String() // 合约名称

			// 数据验证
			if contract == "" || updateId == 0 {
				return
			}

			// 发送到Kafka
			if om.client.kafkaProducer != nil {
				om.client.kafkaProducer.SendOrderbookData(contract, result.Value())
			}

			// 不再输出单独的盘口统计，统一在全局统计中显示
		}
	} else if event != "" || channel != "" {
		// 记录未处理的消息类型，用于调试
		log.Printf("🔍 [Gate.io] 未处理的盘口消息 - Event: %s, Channel: %s", event, channel)
	}
}

// printGlobalStatistics 打印全局统计
func (c *GateIOClient) printGlobalStatistics() {
	defer c.wg.Done()

	ticker := time.NewTicker(15 * time.Second) // 增加到15秒，减少日志频率
	defer ticker.Stop()

	var lastTradeCount, lastOrderbookCount, lastFundingCount int64
	var lastTime time.Time = time.Now()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			now := time.Now()
			runtime := now.Sub(c.startTime)

			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)

			// 计算增量和速率
			deltaTime := now.Sub(lastTime).Seconds()
			tradeDelta := totalTrade - lastTradeCount
			orderbookDelta := totalOrderbook - lastOrderbookCount
			fundingDelta := totalFunding - lastFundingCount

			tradeRate := float64(tradeDelta) / deltaTime
			orderbookRate := float64(orderbookDelta) / deltaTime
			fundingRate := float64(fundingDelta) / deltaTime

			// 更新上次记录
			lastTradeCount = totalTrade
			lastOrderbookCount = totalOrderbook
			lastFundingCount = totalFunding
			lastTime = now

			// 获取连接数
			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 29) / 30 // 每批30个交易对
			totalConnections := tradeConnections + orderbookConnections + fundingConnections
			expectedTotal := expectedConnections * 3

			// 连接健康状态
			healthStatus := "🔴 异常"
			if totalConnections == expectedTotal {
				healthStatus = "🟢 健康"
			} else if totalConnections >= expectedTotal*2/3 {
				healthStatus = "🟡 部分异常"
			}

			log.Printf("📊 [Gate.io] 全局统计 - 运行时间: %v, 状态: %s",
				runtime.Round(time.Second), healthStatus)

			log.Printf("🔗 [Gate.io] 连接状态 - 交易: %d/%d, 盘口: %d/%d, Ticker: %d/%d (总计: %d/%d)",
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				totalConnections, expectedTotal)

			log.Printf("📈 [Gate.io] 消息统计 - 交易: %d, 盘口: %d, Ticker: %d (总计: %d)",
				totalTrade, totalOrderbook, totalFunding, totalTrade+totalOrderbook+totalFunding)

			log.Printf("⚡ [Gate.io] 实时速率 - 交易: %.1f/s, 盘口: %.1f/s, Ticker: %.1f/s (总计: %.1f/s)",
				tradeRate, orderbookRate, fundingRate, tradeRate+orderbookRate+fundingRate)

			// 平均速率统计
			if runtime.Seconds() > 0 {
				avgTradeRate := float64(totalTrade) / runtime.Seconds()
				avgOrderbookRate := float64(totalOrderbook) / runtime.Seconds()
				avgFundingRate := float64(totalFunding) / runtime.Seconds()
				log.Printf("📊 [Gate.io] 平均速率 - 交易: %.1f/s, 盘口: %.1f/s, Ticker: %.1f/s",
					avgTradeRate, avgOrderbookRate, avgFundingRate)
			}

			// 连接效率分析
			if totalConnections > 0 {
				avgTradePerConn := float64(totalTrade) / float64(tradeConnections)
				avgOrderbookPerConn := float64(totalOrderbook) / float64(orderbookConnections)
				avgFundingPerConn := float64(totalFunding) / float64(fundingConnections)
				log.Printf("🎯 [Gate.io] 连接效率 - 交易: %.1f msg/conn, 盘口: %.1f msg/conn, Ticker: %.1f msg/conn",
					avgTradePerConn, avgOrderbookPerConn, avgFundingPerConn)
			}

			log.Printf("─────────────────────────────────────────────────────────")
		}
	}
}

// monitorConnections 监控连接状态
func (c *GateIOClient) monitorConnections() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			tradeConnNames := make([]string, 0, len(c.tradeManager.connections))
			for name := range c.tradeManager.connections {
				tradeConnNames = append(tradeConnNames, name)
			}
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			orderbookConnNames := make([]string, 0, len(c.orderbookManager.connections))
			for name := range c.orderbookManager.connections {
				orderbookConnNames = append(orderbookConnNames, name)
			}
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			fundingConnNames := make([]string, 0, len(c.fundingManager.connections))
			for name := range c.fundingManager.connections {
				fundingConnNames = append(fundingConnNames, name)
			}
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 29) / 30 // 每批30个交易对

			log.Printf("🔍 [Gate.io] 连接监控 - 交易连接: %d/%d, 盘口连接: %d/%d, Ticker连接: %d/%d, 交易对数: %d, 批次大小: 30",
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				len(c.symbols))

			// 详细连接状态
			log.Printf("🔗 [Gate.io] 连接详情 - 交易连接: %v", tradeConnNames)
			log.Printf("🔗 [Gate.io] 连接详情 - 盘口连接: %v", orderbookConnNames)
			log.Printf("🔗 [Gate.io] 连接详情 - Ticker连接: %v", fundingConnNames)

			// 检查连接健康状态
			if tradeConnections < expectedConnections/2 {
				log.Printf("⚠️ [Gate.io] 交易数据连接数异常：当前 %d，预期 %d", tradeConnections, expectedConnections)
			}
			if orderbookConnections < expectedConnections/2 {
				log.Printf("⚠️ [Gate.io] 盘口数据连接数异常：当前 %d，预期 %d", orderbookConnections, expectedConnections)
			}

			// 数据流健康检查
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalConnections := tradeConnections + orderbookConnections

			if totalConnections > 0 && totalTrade == 0 && totalOrderbook == 0 {
				log.Printf("⚠️ [Gate.io] 数据流异常：有连接但没有数据")
			}

			// 连接效率检查
			if totalConnections > 0 {
				avgTradePerConn := float64(totalTrade) / float64(tradeConnections)
				avgOrderbookPerConn := float64(totalOrderbook) / float64(orderbookConnections)
				log.Printf("📊 [Gate.io] 连接效率 - 平均交易消息/连接: %.1f, 平均盘口消息/连接: %.1f",
					avgTradePerConn, avgOrderbookPerConn)
			}
		}
	}
}

// Stop 停止客户端
func (c *GateIOClient) Stop() {
	log.Printf("🛑 正在停止Gate.io客户端...")
	c.cancel()

	// 关闭所有交易数据连接
	c.tradeManager.connMutex.Lock()
	for name, conn := range c.tradeManager.connections {
		log.Printf("🔌 关闭交易数据连接: %s", name)
		conn.Close()
	}
	c.tradeManager.connMutex.Unlock()

	// 关闭所有盘口数据连接
	c.orderbookManager.connMutex.Lock()
	for name, conn := range c.orderbookManager.connections {
		log.Printf("🔌 关闭盘口数据连接: %s", name)
		conn.Close()
	}
	c.orderbookManager.connMutex.Unlock()

	// 关闭Kafka生产者
	if c.kafkaProducer != nil {
		c.kafkaProducer.Close()
	}

	// 等待所有goroutine结束
	c.wg.Wait()

	// 打印最终统计
	totalTrade := atomic.LoadInt64(&c.totalTradeCount)
	totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)

	log.Printf("✅ Gate.io客户端已停止 - 总交易数据: %d 条, 总盘口数据: %d 条", totalTrade, totalOrderbook)
}

// fetchActiveSymbols 获取活跃交易对
func fetchActiveSymbols(config *Config) ([]string, error) {
	url := fmt.Sprintf("%s/api/v4/futures/usdt/contracts", config.RestAPIURL)

	resp, err := makeHTTPRequest(url)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	var contracts []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&contracts); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	var symbols []string
	for _, contract := range contracts {
		if contract["in_delisting"] == false {
			if name, ok := contract["name"].(string); ok {
				symbols = append(symbols, name)
			}
		}
	}

	log.Printf("📋 获取到 %d 个活跃交易对", len(symbols))
	return symbols, nil
}

// setupLogging 设置日志
func setupLogging(config *Config) error {
	logDir := filepath.Dir(config.LogFile)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	return nil
}

func main() {
	// 加载配置
	config, err := loadConfig("config.json")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 设置日志
	if err := setupLogging(config); err != nil {
		log.Fatalf("❌ 设置日志失败: %v", err)
	}

	// 创建logrus logger
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建数据处理器
	dataHandler := NewDefaultDataHandler(logger)

	// 创建Gate.io客户端
	client := NewGateIOClient(config, dataHandler)

	log.Printf("🚀 启动Gate.io数据订阅程序")
	log.Printf("📡 WebSocket地址: %s", config.WebSocketURL)
	log.Printf("🔧 心跳间隔: %v", config.GetPingInterval())

	// 获取活跃交易对
	if err := client.GetAllSymbols(); err != nil {
		log.Fatalf("❌ 获取交易对失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据订阅
	if err := client.SubscribeData(); err != nil {
		log.Fatalf("❌ 启动订阅失败: %v", err)
	}

	log.Printf("🎉 所有订阅已启动，等待数据...")
	log.Printf("💡 程序具备以下健壮性保障：")
	log.Printf("   • 数据类型分离：交易数据和盘口数据使用独立连接")
	log.Printf("   • 自动重连机制：连接断开后自动重连")
	log.Printf("   • 心跳检测：定期发送心跳，检测连接状态")
	log.Printf("   • 连接监控：实时监控连接健康状态")
	log.Printf("   • 批次处理：每批30个交易对，减少连接压力")

	// 等待信号
	<-sigChan
	log.Printf("📡 收到停止信号")

	// 停止客户端
	client.Stop()
}
