package main

import (
	"fmt"
	"math"
	"math/rand"
	"net"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// EnhancedPingPongManager 增强的ping/pong管理器
type EnhancedPingPongManager struct {
	client *GateIOClient
	logger *logrus.Logger
	config *GateIOConfig

	// 全局统计
	totalPings        int64
	totalPongs        int64
	totalTimeouts     int64
	totalReconnects   int64
	minResponseTime   time.Duration
	maxResponseTime   time.Duration
	totalResponseTime time.Duration
	responseCount     int64

	// 连接质量统计
	qualityMutex sync.RWMutex
	qualityStats map[string]*ConnectionQualityStats

	// 随机数生成器
	randMutex sync.Mutex
	randGen   *rand.Rand
}

// ConnectionQualityStats 连接质量统计
type ConnectionQualityStats struct {
	connectionKey       string
	totalPings          int64
	totalPongs          int64
	totalTimeouts       int64
	avgResponseTime     time.Duration
	lastResponseTime    time.Duration
	quality             float64
	lastQualityUpdate   time.Time
	consecutiveTimeouts int
	messageActivity     float64 // 消息活跃度
	lastMessageTime     time.Time
}

// NewEnhancedPingPongManager 创建增强ping/pong管理器
func NewEnhancedPingPongManager(client *GateIOClient, logger *logrus.Logger, config *GateIOConfig) *EnhancedPingPongManager {
	return &EnhancedPingPongManager{
		client:          client,
		logger:          logger,
		config:          config,
		qualityStats:    make(map[string]*ConnectionQualityStats),
		randGen:         rand.New(rand.NewSource(time.Now().UnixNano())),
		minResponseTime: time.Hour, // 初始化为一个大值
	}
}

// CreateEnhancedWebSocketDialer 创建增强的WebSocket拨号器
func (pm *EnhancedPingPongManager) CreateEnhancedWebSocketDialer() *websocket.Dialer {
	return &websocket.Dialer{
		HandshakeTimeout: pm.config.GetHandshakeTimeout(),
		ReadBufferSize:   pm.config.GetReadBufferSize(),
		WriteBufferSize:  pm.config.GetWriteBufferSize(),
		NetDial: func(network, addr string) (net.Conn, error) {
			dialer := &net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}
			return dialer.Dial(network, addr)
		},
	}
}

// StartEnhancedHeartbeat 启动增强心跳机制
func (pm *EnhancedPingPongManager) StartEnhancedHeartbeat(connKey string, connInfo *ConnectionInfo) {
	pm.logger.WithField("connection", connKey).Debug("🚀 [Gate.io] 启动增强心跳机制")

	// 初始化连接质量统计
	pm.initConnectionQualityStats(connKey)

	// 启动多个定时器
	pingTicker := time.NewTicker(pm.config.GetPingInterval())
	healthTicker := time.NewTicker(pm.config.GetHealthCheckInterval())
	qualityTicker := time.NewTicker(pm.config.GetQualityCheckInterval())

	defer func() {
		pingTicker.Stop()
		healthTicker.Stop()
		qualityTicker.Stop()
		pm.logger.WithField("connection", connKey).Debug("🛑 [Gate.io] 增强心跳机制已停止")
	}()

	// 设置ping/pong处理器
	pm.setupPingPongHandlers(connKey, connInfo)

	for {
		select {
		case <-pm.client.ctx.Done():
			return
		case <-connInfo.heartbeatStop:
			return
		case <-pingTicker.C:
			pm.sendEnhancedPing(connKey, connInfo)
		case <-healthTicker.C:
			pm.performHealthCheck(connKey, connInfo)
		case <-qualityTicker.C:
			pm.updateConnectionQuality(connKey, connInfo)
		}
	}
}

// setupPingPongHandlers 设置ping/pong处理器
func (pm *EnhancedPingPongManager) setupPingPongHandlers(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	connInfo.mutex.RUnlock()

	if conn == nil {
		return
	}

	// 设置pong处理器（处理WebSocket pong帧）
	conn.SetPongHandler(func(appData string) error {
		now := time.Now()

		connInfo.mutex.Lock()
		responseTime := now.Sub(connInfo.lastPingTime)
		connInfo.lastPongTime = now
		connInfo.mutex.Unlock()

		// 更新统计
		atomic.AddInt64(&pm.totalPongs, 1)
		pm.updateResponseTimeStats(responseTime)
		pm.updateConnectionPongStats(connKey, responseTime)

		pm.logger.WithFields(logrus.Fields{
			"connection":    connKey,
			"response_time": responseTime,
		}).Debug("💓 [Gate.io] 收到WebSocket pong响应")

		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(pm.config.GetWriteTimeout()))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			pm.logger.WithError(err).Errorf("💔 [Gate.io] 连接 %s 响应服务器ping失败", connKey)
			return err
		}
		pm.logger.WithField("connection", connKey).Debug("💓 [Gate.io] 响应服务器ping")
		return nil
	})
}

// sendEnhancedPing 发送增强ping
func (pm *EnhancedPingPongManager) sendEnhancedPing(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	connInfo.mutex.RUnlock()

	if conn == nil {
		return
	}

	now := time.Now()

	// 更新ping时间
	connInfo.mutex.Lock()
	connInfo.lastPingTime = now
	connInfo.mutex.Unlock()

	// 发送WebSocket ping帧（主动ping）
	conn.SetWriteDeadline(now.Add(pm.config.GetWriteTimeout()))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("gateio-enhanced-ping")); err != nil {
		pm.logger.WithError(err).Errorf("💔 [Gate.io] WebSocket ping失败: %s", connKey)
		return
	}

	// 发送应用层ping（使用正确的Gate.io格式）
	pingMsg := map[string]interface{}{
		"time":    now.Unix(),
		"channel": "futures.ping",
	}
	if err := conn.WriteJSON(pingMsg); err != nil {
		pm.logger.WithError(err).Errorf("💔 [Gate.io] 应用层ping失败: %s", connKey)
		return
	}

	// 更新统计
	atomic.AddInt64(&pm.totalPings, 1)
	pm.updateConnectionPingStats(connKey)

	pm.logger.WithField("connection", connKey).Debug("💓 [Gate.io] 发送增强双重ping")
}

// performHealthCheck 执行健康检查
func (pm *EnhancedPingPongManager) performHealthCheck(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	lastPongTime := connInfo.lastPongTime
	connInfo.mutex.RUnlock()

	pongDelay := time.Since(lastPongTime)
	maxPongDelay := pm.config.GetPongTimeout()

	pm.qualityMutex.Lock()
	stats := pm.qualityStats[connKey]
	if stats != nil {
		if pongDelay > maxPongDelay {
			stats.consecutiveTimeouts++
			atomic.AddInt64(&pm.totalTimeouts, 1)

			pm.logger.WithFields(logrus.Fields{
				"connection":           connKey,
				"pong_delay":           pongDelay,
				"max_delay":            maxPongDelay,
				"consecutive_timeouts": stats.consecutiveTimeouts,
			}).Warn("⚠️ [Gate.io] 连接pong超时")

			// 检查是否需要关闭连接
			if stats.consecutiveTimeouts >= pm.config.GetMaxConsecutiveTimeouts() {
				pm.logger.WithField("connection", connKey).Error("💔 [Gate.io] 连接连续超时，需要重连")
				pm.qualityMutex.Unlock()

				// 触发重连
				connInfo.mutex.Lock()
				if connInfo.heartbeatStop != nil {
					close(connInfo.heartbeatStop)
					connInfo.heartbeatStop = nil
				}
				connInfo.mutex.Unlock()
				return
			}
		} else {
			// 重置连续超时计数
			if stats.consecutiveTimeouts > 0 {
				stats.consecutiveTimeouts = 0
				pm.logger.WithField("connection", connKey).Debug("✅ [Gate.io] 连接恢复正常")
			}
		}
	}
	pm.qualityMutex.Unlock()
}

// updateConnectionQuality 更新连接质量
func (pm *EnhancedPingPongManager) updateConnectionQuality(connKey string, connInfo *ConnectionInfo) {
	pm.qualityMutex.Lock()
	defer pm.qualityMutex.Unlock()

	stats := pm.qualityStats[connKey]
	if stats == nil {
		return
	}

	now := time.Now()

	// 计算消息活跃度
	connInfo.mutex.RLock()
	lastMessageTime := connInfo.lastMessageTime
	messageCount := connInfo.messageCount
	connInfo.mutex.RUnlock()

	messageActivity := 0.0
	if !lastMessageTime.IsZero() {
		timeSinceLastMessage := now.Sub(lastMessageTime)
		if timeSinceLastMessage < time.Minute {
			messageActivity = 1.0
		} else if timeSinceLastMessage < 5*time.Minute {
			messageActivity = 0.5
		}
	}

	// 计算延迟评分 (40%)
	delayScore := 1.0
	if stats.avgResponseTime > 0 {
		// 延迟越低分数越高
		normalizedDelay := float64(stats.avgResponseTime) / float64(pm.config.GetPingInterval())
		delayScore = math.Max(0, 1.0-normalizedDelay)
	}

	// 计算超时评分 (30%)
	timeoutScore := 1.0
	if stats.totalPings > 0 {
		timeoutRate := float64(stats.totalTimeouts) / float64(stats.totalPings)
		timeoutScore = math.Max(0, 1.0-timeoutRate*2) // 超时率越低分数越高
	}

	// 消息活跃度评分 (30%)
	activityScore := messageActivity

	// 综合质量评分
	quality := delayScore*0.4 + timeoutScore*0.3 + activityScore*0.3

	// 使用指数移动平均平滑质量变化
	alpha := 0.3
	if stats.quality > 0 {
		stats.quality = alpha*quality + (1-alpha)*stats.quality
	} else {
		stats.quality = quality
	}

	stats.messageActivity = messageActivity
	stats.lastQualityUpdate = now

	// 记录质量变化
	qualityLevel := pm.getQualityLevel(stats.quality)
	pm.logger.WithFields(logrus.Fields{
		"connection":     connKey,
		"quality":        fmt.Sprintf("%.1f%%", stats.quality*100),
		"quality_level":  qualityLevel,
		"delay_score":    fmt.Sprintf("%.1f%%", delayScore*100),
		"timeout_score":  fmt.Sprintf("%.1f%%", timeoutScore*100),
		"activity_score": fmt.Sprintf("%.1f%%", activityScore*100),
		"avg_response":   stats.avgResponseTime,
		"message_count":  messageCount,
	}).Debug("🎯 [Gate.io] 连接质量更新")
}

// getQualityLevel 获取质量等级
func (pm *EnhancedPingPongManager) getQualityLevel(quality float64) string {
	if quality >= 0.9 {
		return "优秀"
	} else if quality >= 0.7 {
		return "良好"
	} else if quality >= 0.4 {
		return "一般"
	} else if quality >= 0.3 {
		return "较差"
	} else {
		return "极差"
	}
}

// CalculateSmartReconnectDelay 计算智能重连延迟
func (pm *EnhancedPingPongManager) CalculateSmartReconnectDelay(connKey string, attempt int) time.Duration {
	if !pm.config.EnableSmartReconnect {
		return pm.config.GetReconnectDelay()
	}

	// 基础延迟
	baseDelay := pm.config.GetBaseReconnectDelay()

	// 指数退避
	backoffRate := pm.config.GetExponentialBackoffRate()
	exponentialDelay := time.Duration(float64(baseDelay) * math.Pow(backoffRate, float64(attempt)))

	// 限制最大延迟
	maxDelay := pm.config.GetMaxReconnectDelay()
	if exponentialDelay > maxDelay {
		exponentialDelay = maxDelay
	}

	// 添加随机抖动避免雷群效应
	pm.randMutex.Lock()
	jitter := time.Duration(pm.randGen.Int63n(int64(pm.config.GetReconnectJitterMax())))
	pm.randMutex.Unlock()

	// 连接类型优先级调整
	priorityMultiplier := 1.0
	if strings.Contains(connKey, "trade") {
		priorityMultiplier = 0.8 // 交易数据优先级最高
	} else if strings.Contains(connKey, "orderbook") {
		priorityMultiplier = 0.9 // 盘口数据次之
	} else if strings.Contains(connKey, "funding") {
		priorityMultiplier = 1.1 // 资金费率优先级最低
	}

	finalDelay := time.Duration(float64(exponentialDelay)*priorityMultiplier) + jitter

	pm.logger.WithFields(logrus.Fields{
		"connection":          connKey,
		"attempt":             attempt,
		"base_delay":          baseDelay,
		"exponential_delay":   exponentialDelay,
		"jitter":              jitter,
		"priority_multiplier": priorityMultiplier,
		"final_delay":         finalDelay,
	}).Debug("🔄 [Gate.io] 计算智能重连延迟")

	return finalDelay
}

// 统计相关方法
func (pm *EnhancedPingPongManager) initConnectionQualityStats(connKey string) {
	pm.qualityMutex.Lock()
	defer pm.qualityMutex.Unlock()

	pm.qualityStats[connKey] = &ConnectionQualityStats{
		connectionKey:     connKey,
		lastQualityUpdate: time.Now(),
	}
}

func (pm *EnhancedPingPongManager) updateResponseTimeStats(responseTime time.Duration) {
	atomic.AddInt64(&pm.responseCount, 1)
	atomic.AddInt64((*int64)(&pm.totalResponseTime), int64(responseTime))

	// 更新最小响应时间
	for {
		current := atomic.LoadInt64((*int64)(&pm.minResponseTime))
		if int64(responseTime) >= current && current != int64(time.Hour) {
			break
		}
		if atomic.CompareAndSwapInt64((*int64)(&pm.minResponseTime), current, int64(responseTime)) {
			break
		}
	}

	// 更新最大响应时间
	for {
		current := atomic.LoadInt64((*int64)(&pm.maxResponseTime))
		if int64(responseTime) <= current {
			break
		}
		if atomic.CompareAndSwapInt64((*int64)(&pm.maxResponseTime), current, int64(responseTime)) {
			break
		}
	}
}

func (pm *EnhancedPingPongManager) updateConnectionPingStats(connKey string) {
	pm.qualityMutex.Lock()
	defer pm.qualityMutex.Unlock()

	if stats := pm.qualityStats[connKey]; stats != nil {
		stats.totalPings++
	}
}

func (pm *EnhancedPingPongManager) updateConnectionPongStats(connKey string, responseTime time.Duration) {
	pm.qualityMutex.Lock()
	defer pm.qualityMutex.Unlock()

	if stats := pm.qualityStats[connKey]; stats != nil {
		stats.totalPongs++
		stats.lastResponseTime = responseTime

		// 计算指数移动平均响应时间
		alpha := 0.3
		if stats.avgResponseTime == 0 {
			stats.avgResponseTime = responseTime
		} else {
			newAvg := time.Duration(alpha*float64(responseTime) + (1-alpha)*float64(stats.avgResponseTime))
			stats.avgResponseTime = newAvg
		}
	}
}

// GetGlobalPingPongStats 获取全局ping/pong统计
func (pm *EnhancedPingPongManager) GetGlobalPingPongStats() map[string]interface{} {
	totalPings := atomic.LoadInt64(&pm.totalPings)
	totalPongs := atomic.LoadInt64(&pm.totalPongs)
	totalTimeouts := atomic.LoadInt64(&pm.totalTimeouts)
	totalReconnects := atomic.LoadInt64(&pm.totalReconnects)
	responseCount := atomic.LoadInt64(&pm.responseCount)
	totalResponseTime := time.Duration(atomic.LoadInt64((*int64)(&pm.totalResponseTime)))
	minResponseTime := time.Duration(atomic.LoadInt64((*int64)(&pm.minResponseTime)))
	maxResponseTime := time.Duration(atomic.LoadInt64((*int64)(&pm.maxResponseTime)))

	avgResponseTime := time.Duration(0)
	if responseCount > 0 {
		avgResponseTime = totalResponseTime / time.Duration(responseCount)
	}

	// 重置最小响应时间如果还是初始值
	if minResponseTime == time.Hour {
		minResponseTime = 0
	}

	return map[string]interface{}{
		"total_pings":       totalPings,
		"total_pongs":       totalPongs,
		"total_timeouts":    totalTimeouts,
		"total_reconnects":  totalReconnects,
		"avg_response_time": avgResponseTime,
		"min_response_time": minResponseTime,
		"max_response_time": maxResponseTime,
	}
}

// GetConnectionQualityStats 获取连接质量统计
func (pm *EnhancedPingPongManager) GetConnectionQualityStats() map[string]interface{} {
	pm.qualityMutex.RLock()
	defer pm.qualityMutex.RUnlock()

	totalConnections := len(pm.qualityStats)
	excellentCount := 0
	goodCount := 0
	averageCount := 0
	poorCount := 0
	veryPoorCount := 0
	totalQuality := 0.0

	for _, stats := range pm.qualityStats {
		totalQuality += stats.quality

		if stats.quality >= 0.9 {
			excellentCount++
		} else if stats.quality >= 0.7 {
			goodCount++
		} else if stats.quality >= 0.4 {
			averageCount++
		} else if stats.quality >= 0.3 {
			poorCount++
		} else {
			veryPoorCount++
		}
	}

	avgQuality := 0.0
	if totalConnections > 0 {
		avgQuality = totalQuality / float64(totalConnections)
	}

	return map[string]interface{}{
		"total_connections": totalConnections,
		"excellent_count":   excellentCount,
		"good_count":        goodCount,
		"average_count":     averageCount,
		"poor_count":        poorCount,
		"very_poor_count":   veryPoorCount,
		"average_quality":   avgQuality,
	}
}
