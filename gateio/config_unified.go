package main

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
)

// GateIOConfig Gate.io配置
type GateIOConfig struct {
	// WebSocket配置
	WebSocketURL string `json:"websocket_url"`
	RestAPIURL   string `json:"rest_api_url"`

	// 连接配置
	BatchSize        int           `json:"batch_size"`
	PingInterval     time.Duration `json:"ping_interval"`
	ReconnectDelay   time.Duration `json:"reconnect_delay"`
	MaxReconnects    int           `json:"max_reconnects"`
	ReadTimeout      time.Duration `json:"read_timeout"`
	WriteTimeout     time.Duration `json:"write_timeout"`
	HandshakeTimeout time.Duration `json:"handshake_timeout"`

	// 缓冲区配置
	ReadBufferSize  int `json:"read_buffer_size"`
	WriteBufferSize int `json:"write_buffer_size"`

	// 日志配置
	LogLevel    string `json:"log_level"`
	LogFile     string `json:"log_file"`
	LogMaxSize  int    `json:"log_max_size"`
	LogMaxAge   int    `json:"log_max_age"`
	LogCompress bool   `json:"log_compress"`

	// 增强ping/pong配置
	EnableEnhancedPingPong bool          `json:"enable_enhanced_ping_pong"`
	PongTimeout            time.Duration `json:"pong_timeout"`
	HealthCheckInterval    time.Duration `json:"health_check_interval"`
	QualityCheckInterval   time.Duration `json:"quality_check_interval"`
	MaxConsecutiveTimeouts int           `json:"max_consecutive_timeouts"`

	// 重连策略配置
	EnableSmartReconnect   bool          `json:"enable_smart_reconnect"`
	BaseReconnectDelay     time.Duration `json:"base_reconnect_delay"`
	MaxReconnectDelay      time.Duration `json:"max_reconnect_delay"`
	ReconnectJitterMax     time.Duration `json:"reconnect_jitter_max"`
	ExponentialBackoffRate float64       `json:"exponential_backoff_rate"`
}

// GetPingInterval 获取ping间隔
func (c *GateIOConfig) GetPingInterval() time.Duration {
	if c.PingInterval > 0 {
		return c.PingInterval
	}
	return 30 * time.Second
}

// GetReconnectDelay 获取重连延迟
func (c *GateIOConfig) GetReconnectDelay() time.Duration {
	if c.ReconnectDelay > 0 {
		return c.ReconnectDelay
	}
	return 3 * time.Second
}

// GetPongTimeout 获取pong超时时间
func (c *GateIOConfig) GetPongTimeout() time.Duration {
	if c.PongTimeout > 0 {
		return c.PongTimeout
	}
	return c.GetPingInterval() * 8
}

// GetHealthCheckInterval 获取健康检查间隔
func (c *GateIOConfig) GetHealthCheckInterval() time.Duration {
	if c.HealthCheckInterval > 0 {
		return c.HealthCheckInterval
	}
	return c.GetPingInterval() / 2
}

// GetQualityCheckInterval 获取质量检查间隔
func (c *GateIOConfig) GetQualityCheckInterval() time.Duration {
	if c.QualityCheckInterval > 0 {
		return c.QualityCheckInterval
	}
	return 10 * time.Second
}

// GetReadTimeout 获取读取超时
func (c *GateIOConfig) GetReadTimeout() time.Duration {
	if c.ReadTimeout > 0 {
		return c.ReadTimeout
	}
	return 5 * time.Minute
}

// GetWriteTimeout 获取写入超时
func (c *GateIOConfig) GetWriteTimeout() time.Duration {
	if c.WriteTimeout > 0 {
		return c.WriteTimeout
	}
	return 30 * time.Second
}

// GetHandshakeTimeout 获取握手超时
func (c *GateIOConfig) GetHandshakeTimeout() time.Duration {
	if c.HandshakeTimeout > 0 {
		return c.HandshakeTimeout
	}
	return 45 * time.Second
}

// GetReadBufferSize 获取读缓冲区大小
func (c *GateIOConfig) GetReadBufferSize() int {
	if c.ReadBufferSize > 0 {
		return c.ReadBufferSize
	}
	return 8192
}

// GetWriteBufferSize 获取写缓冲区大小
func (c *GateIOConfig) GetWriteBufferSize() int {
	if c.WriteBufferSize > 0 {
		return c.WriteBufferSize
	}
	return 8192
}

// GetMaxConsecutiveTimeouts 获取最大连续超时次数
func (c *GateIOConfig) GetMaxConsecutiveTimeouts() int {
	if c.MaxConsecutiveTimeouts > 0 {
		return c.MaxConsecutiveTimeouts
	}
	return 3
}

// GetBaseReconnectDelay 获取基础重连延迟
func (c *GateIOConfig) GetBaseReconnectDelay() time.Duration {
	if c.BaseReconnectDelay > 0 {
		return c.BaseReconnectDelay
	}
	return 3 * time.Second
}

// GetMaxReconnectDelay 获取最大重连延迟
func (c *GateIOConfig) GetMaxReconnectDelay() time.Duration {
	if c.MaxReconnectDelay > 0 {
		return c.MaxReconnectDelay
	}
	return 60 * time.Second
}

// GetReconnectJitterMax 获取重连抖动最大值
func (c *GateIOConfig) GetReconnectJitterMax() time.Duration {
	if c.ReconnectJitterMax > 0 {
		return c.ReconnectJitterMax
	}
	return 3 * time.Second
}

// GetExponentialBackoffRate 获取指数退避率
func (c *GateIOConfig) GetExponentialBackoffRate() float64 {
	if c.ExponentialBackoffRate > 0 {
		return c.ExponentialBackoffRate
	}
	return 2.0
}

// loadGateIOConfig 加载配置
func loadGateIOConfig(configPath string) (*GateIOConfig, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := &GateIOConfig{
			WebSocketURL:           "wss://fx-ws.gateio.ws/v4/ws/usdt",
			RestAPIURL:             "https://api.gateio.ws",
			BatchSize:              30,
			PingInterval:           30 * time.Second,
			ReconnectDelay:         3 * time.Second,
			MaxReconnects:          15,
			ReadTimeout:            5 * time.Minute,
			WriteTimeout:           30 * time.Second,
			HandshakeTimeout:       45 * time.Second,
			ReadBufferSize:         8192,
			WriteBufferSize:        8192,
			LogLevel:               "info",
			LogFile:                "logs/gateio.log",
			LogMaxSize:             100,
			LogMaxAge:              7,
			LogCompress:            true,
			EnableEnhancedPingPong: true,
			PongTimeout:            240 * time.Second,
			HealthCheckInterval:    15 * time.Second,
			QualityCheckInterval:   10 * time.Second,
			MaxConsecutiveTimeouts: 3,
			EnableSmartReconnect:   true,
			BaseReconnectDelay:     3 * time.Second,
			MaxReconnectDelay:      60 * time.Second,
			ReconnectJitterMax:     3 * time.Second,
			ExponentialBackoffRate: 2.0,
		}

		// 创建配置文件目录
		if err := os.MkdirAll(filepath.Dir(configPath), 0755); err != nil {
			return nil, fmt.Errorf("创建配置目录失败: %v", err)
		}

		// 保存默认配置
		configData, err := json.MarshalIndent(defaultConfig, "", "  ")
		if err != nil {
			return nil, fmt.Errorf("序列化默认配置失败: %v", err)
		}

		if err := os.WriteFile(configPath, configData, 0644); err != nil {
			return nil, fmt.Errorf("保存默认配置失败: %v", err)
		}

		fmt.Printf("✅ 已创建默认配置文件: %s\n", configPath)
		return defaultConfig, nil
	}

	// 读取配置文件
	configData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config GateIOConfig
	if err := json.Unmarshal(configData, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	return &config, nil
}

// setupGateIOLogging 设置日志
func setupGateIOLogging(config *GateIOConfig, logger *logrus.Logger) error {
	// 设置日志级别
	level, err := logrus.ParseLevel(config.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006/01/02 15:04:05",
		ForceColors:     true,
	})

	// 创建日志目录
	if config.LogFile != "" {
		logDir := filepath.Dir(config.LogFile)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %v", err)
		}

		// 打开日志文件
		logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("打开日志文件失败: %v", err)
		}

		logger.SetOutput(logFile)
	}

	return nil
}
