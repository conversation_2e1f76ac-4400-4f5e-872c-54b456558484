package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type Config struct {
	Exchange       string `json:"exchange"`
	WebSocketURL   string `json:"websocket_url"`
	RestAPIURL     string `json:"rest_api_url"`
	ReconnectDelay string `json:"reconnect_delay"`
	PingInterval   string `json:"ping_interval"`
	LogFile        string `json:"log_file"`
	LogLevel       string `json:"log_level"`

	// Kafka配置
	Kafka KafkaConfig `json:"kafka"`

	// 运行时解析的时间字段
	reconnectDelay time.Duration
	pingInterval   time.Duration
}

// KafkaConfig Kafka配置结构
type KafkaConfig struct {
	Enabled          bool                `json:"enabled"`
	Brokers          []string            `json:"brokers"`
	SecurityProtocol string              `json:"security_protocol"`
	SASLMechanism    string              `json:"sasl_mechanism"`
	SASLUsername     string              `json:"sasl_username"`
	SASLPassword     string              `json:"sasl_password"`
	Topics           KafkaTopics         `json:"topics"`
	ProducerConfig   KafkaProducerConfig `json:"producer_config"`
}

// KafkaTopics Kafka主题配置
type KafkaTopics struct {
	Trades     string `json:"trades"`
	Orderbooks string `json:"orderbooks"`
	Funding    string `json:"funding"`
}

// KafkaProducerConfig Kafka生产者配置
type KafkaProducerConfig struct {
	Acks         string `json:"acks"`
	Retries      int    `json:"retries"`
	BatchSize    int    `json:"batch_size"`
	LingerMs     int    `json:"linger_ms"`
	BufferMemory int    `json:"buffer_memory"`
}

func loadConfig(filename string) (*Config, error) {
	// 默认配置
	config := &Config{
		Exchange:       "gateio",
		WebSocketURL:   "wss://fx-ws.gateio.ws/v4/ws/usdt",
		RestAPIURL:     "https://api.gateio.ws",
		ReconnectDelay: "5s",
		PingInterval:   "20s",
		LogFile:        "logs/gateio.log",
		LogLevel:       "info",
	}

	// 尝试加载配置文件
	if _, err := os.Stat(filename); err == nil {
		file, err := os.Open(filename)
		if err != nil {
			return nil, fmt.Errorf("无法打开配置文件: %v", err)
		}
		defer file.Close()

		decoder := json.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return nil, fmt.Errorf("解析配置文件失败: %v", err)
		}
	}

	// 解析时间字段
	if err := config.parseDurations(); err != nil {
		return nil, err
	}

	return config, nil
}

func (c *Config) parseDurations() error {
	var err error

	c.reconnectDelay, err = time.ParseDuration(c.ReconnectDelay)
	if err != nil {
		return fmt.Errorf("解析重连延迟时间失败: %v", err)
	}

	c.pingInterval, err = time.ParseDuration(c.PingInterval)
	if err != nil {
		return fmt.Errorf("解析心跳间隔时间失败: %v", err)
	}

	return nil
}

func (c *Config) GetReconnectDelay() time.Duration {
	return c.reconnectDelay
}

func (c *Config) GetPingInterval() time.Duration {
	return c.pingInterval
}
