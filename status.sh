#!/bin/bash

# 监控所有交易所程序状态

echo "📊 交易所程序状态监控"
echo "========================"
echo ""

# 检查程序状态
check_status() {
    local exchange=$1
    local executable=$2
    
    if [ -f "logs/${exchange}.pid" ]; then
        local pid=$(cat logs/${exchange}.pid)
        
        if ps -p $pid > /dev/null 2>&1; then
            # 获取进程信息
            local cpu_mem=$(ps -p $pid -o %cpu,%mem --no-headers)
            local start_time=$(ps -p $pid -o lstart --no-headers)
            
            echo "✅ $exchange"
            echo "   PID: $pid"
            echo "   CPU/MEM: $cpu_mem"
            echo "   启动时间: $start_time"
            
            # 检查日志文件大小
            if [ -f "logs/${exchange}.log" ]; then
                local log_size=$(du -h logs/${exchange}.log | cut -f1)
                echo "   日志大小: $log_size"
                
                # 显示最近的日志
                echo "   最近日志:"
                tail -n 2 logs/${exchange}.log | sed 's/^/     /'
            fi
        else
            echo "❌ $exchange (PID: $pid) - 进程不存在"
        fi
    else
        echo "❓ $exchange - 未启动"
    fi
    echo ""
}

# 显示系统信息
echo "🖥️ 系统信息:"
echo "   时间: $(date)"
echo "   负载: $(uptime | awk -F'load average:' '{print $2}')"
echo "   内存: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo ""

# 检查各个交易所状态
echo "📡 交易所程序状态:"
echo ""

check_status "binance" "binance-data"
check_status "okx" "okx-data"
check_status "gateio" "gateio-data"
check_status "bybit" "bybit-data"
check_status "bitget" "bitget-data"
check_status "hyperliquid" "hyperliquid-data"

# 统计信息
echo "📈 统计信息:"
running_count=0
total_count=6

for exchange in binance okx gateio bybit bitget hyperliquid; do
    if [ -f "logs/${exchange}.pid" ]; then
        pid=$(cat logs/${exchange}.pid)
        if ps -p $pid > /dev/null 2>&1; then
            running_count=$((running_count + 1))
        fi
    fi
done

echo "   运行中: $running_count/$total_count"
echo "   运行率: $(echo "scale=1; $running_count * 100 / $total_count" | bc)%"
echo ""

# 显示日志目录信息
if [ -d "logs" ]; then
    echo "📁 日志目录信息:"
    echo "   总大小: $(du -sh logs | cut -f1)"
    echo "   文件数: $(ls -1 logs | wc -l)"
    echo ""
fi

# 提供操作建议
echo "🔧 操作命令:"
echo "   启动所有: ./start_all.sh"
echo "   停止所有: ./stop_all.sh"
echo "   查看日志: tail -f logs/[exchange].log"
echo "   实时监控: watch -n 5 ./status.sh" 