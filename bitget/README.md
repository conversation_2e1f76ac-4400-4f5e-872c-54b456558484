# Bitget永续合约数据订阅程序

基于Bitget V2 WebSocket API的永续合约交易数据和盘口数据实时订阅程序。

## 功能特性

- ✅ **全币种订阅**: 自动获取所有活跃的USDT永续合约交易对
- 📈 **交易数据**: 实时订阅交易数据（trade频道）
- 📊 **盘口数据**: 实时订阅一档盘口数据（books1频道）
- 🔄 **自动重连**: 支持三种重连策略（有限、无限、指数退避）
- 💔 **心跳检测**: 30秒心跳间隔，确保连接活跃
- 📡 **批量处理**: 分批订阅，每批50个交易对，避免连接压力
- 📊 **实时统计**: 每10秒显示数据统计信息
- 🔍 **连接监控**: 每分钟进行连接健康检查
- 🎯 **过滤器**: 支持指定交易对过滤
- 🛑 **优雅停止**: 支持Ctrl+C优雅停止

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置文件

程序首次运行会自动创建 `bitget_config.json` 配置文件：

```json
{
  "api_base_url": "https://api.bitget.com",
  "ws_base_url": "wss://ws.bitget.com/v2/ws/public",
  "batch_size": 50,
  "max_reconnect_attempts": 10,
  "reconnect_delay": "5s",
  "max_reconnect_delay": "300s",
  "reconnect_strategy": "infinite",
  "connection_timeout": "30s",
  "read_timeout": "300s",
  "write_timeout": "10s",
  "ping_interval": "30s",
  "pong_timeout": "10s",
  "enable_trade_data": true,
  "enable_book_ticker": true,
  "enable_depth_data": false,
  "log_level": "info",
  "symbol_filter": []
}
```

### 3. 运行程序

```bash
go run .
```

或编译后运行：

```bash
go build -o bitget-subscriber
./bitget-subscriber
```

## 配置说明

### API配置
- `api_base_url`: Bitget REST API地址
- `ws_base_url`: Bitget WebSocket地址

### 连接配置
- `batch_size`: 每批处理的交易对数量（建议50）
- `max_reconnect_attempts`: 最大重连次数
- `reconnect_delay`: 重连延迟时间
- `max_reconnect_delay`: 最大重连延迟（指数退避用）
- `reconnect_strategy`: 重连策略
  - `limited`: 有限重连
  - `infinite`: 无限重连（推荐）
  - `exponential`: 指数退避重连

### WebSocket配置
- `connection_timeout`: 连接超时时间
- `read_timeout`: 读取超时时间
- `write_timeout`: 写入超时时间
- `ping_interval`: 心跳间隔
- `pong_timeout`: 心跳超时

### 订阅配置
- `enable_trade_data`: 启用交易数据订阅
- `enable_book_ticker`: 启用盘口数据订阅
- `enable_depth_data`: 启用深度数据订阅（暂未实现）

### 其他配置
- `log_level`: 日志级别（debug, info, warn, error）
- `symbol_filter`: 交易对过滤器（空数组表示订阅所有）

## 数据格式

### 交易数据（Trade）
```json
{
  "instId": "BTCUSDT",
  "tradeId": "123456789",
  "price": "50000.00",
  "size": "0.001",
  "side": "buy",
  "ts": "1640995200000"
}
```

### 一档盘口数据（Books1）
```json
{
  "instId": "BTCUSDT",
  "bids": [["49999.99", "1.5"]],
  "asks": [["50000.01", "2.0"]],
  "ts": "1640995200000"
}
```

## 重连机制

程序具备完善的重连机制：

1. **自动重连**: 连接断开后自动重连
2. **重连策略**: 支持三种重连策略
3. **故障隔离**: 单个连接失败不影响其他连接
4. **心跳检测**: 定期发送心跳包检测连接状态

## 性能特性

- **并发处理**: 每个批次独立的goroutine处理
- **内存优化**: 使用gjson进行高效JSON解析
- **连接复用**: 每个连接处理多个交易对
- **批量订阅**: 减少连接数量，提高效率

## 监控信息

程序提供详细的监控信息：

- 运行时间
- 活跃连接数
- 交易消息数量
- 盘口消息数量
- 总消息数量

## 注意事项

1. **API限制**: 遵守Bitget API频率限制
2. **网络稳定**: 确保网络连接稳定
3. **资源监控**: 监控CPU和内存使用情况
4. **日志管理**: 定期清理日志文件

## 故障排除

### 连接失败
- 检查网络连接
- 验证WebSocket地址
- 检查防火墙设置

### 数据丢失
- 检查重连配置
- 监控连接状态
- 查看错误日志

### 性能问题
- 调整批处理大小
- 优化日志级别
- 监控系统资源

## 技术架构

- **语言**: Go 1.21+
- **WebSocket**: gorilla/websocket
- **JSON解析**: tidwall/gjson
- **日志**: sirupsen/logrus
- **并发**: goroutine + channel

## 许可证

MIT License 