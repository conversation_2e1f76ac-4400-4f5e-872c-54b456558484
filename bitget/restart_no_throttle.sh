#!/bin/bash

echo "🚀 重启 Bitget 程序（无并发限制版本）"
echo "=================================="

# 停止现有程序
echo "🛑 停止现有程序..."
pkill -f "bitget-subscriber-enhanced" 2>/dev/null || true
sleep 3

# 备份当前日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%H%M%S")
    cp logs/bitget.log logs/bitget_no_throttle_${timestamp}.log
    echo "📁 日志已备份为: logs/bitget_no_throttle_${timestamp}.log"
fi

# 清空日志
> logs/bitget.log

echo "🔧 当前配置（无并发限制版本）:"
echo "   ✅ 移除并发连接限制"
echo "   ✅ 移除重连节流"
echo "   ✅ 简化延迟计算"
echo "   ✅ 保持优先级调度"
echo ""

# 启动程序
echo "🚀 启动程序..."
./bitget-subscriber-enhanced &
PID=$!

echo "✅ 程序已启动 (PID: $PID)"
echo "📊 监控关键指标..."
echo ""

# 监控日志，重点关注连接状态
tail -f logs/bitget.log | while read line; do
    # 高亮显示重要信息
    if echo "$line" | grep -q "等待.*活跃重连数"; then
        echo -e "\033[33m$line\033[0m"  # 黄色 - 应该不再出现
    elif echo "$line" | grep -q "连接.*成功\|建立成功"; then
        echo -e "\033[32m$line\033[0m"  # 绿色
    elif echo "$line" | grep -q "错误\|异常\|失败"; then
        echo -e "\033[31m$line\033[0m"  # 红色
    elif echo "$line" | grep -q "全局统计\|连接质量\|Kafka"; then
        echo -e "\033[36m$line\033[0m"  # 青色
    elif echo "$line" | grep -q "稳定性\|重连\|延迟"; then
        echo -e "\033[35m$line\033[0m"  # 紫色
    else
        echo "$line"
    fi
done
