package main

import (
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// ErrorType 错误类型
type ErrorType int

const (
	ErrorTypeNetwork ErrorType = iota
	ErrorTypeWebSocket
	ErrorTypeTimeout
	ErrorTypeAuth
	ErrorTypeRateLimit
	ErrorTypeUnknown
)

// ErrorPattern 错误模式
type ErrorPattern struct {
	Pattern     string
	ErrorType   ErrorType
	Severity    int // 1-5, 5最严重
	RetryDelay  time.Duration
	MaxRetries  int
	Description string
}

// EnhancedErrorHandler 增强错误处理器
type EnhancedErrorHandler struct {
	logger *logrus.Logger
	config *BitgetConfig

	// 错误模式库
	errorPatterns []ErrorPattern

	// 错误统计
	errorStats struct {
		totalErrors    int64
		errorsByType   map[ErrorType]int64
		recentErrors   []ErrorRecord
		lastErrorTime  time.Time
		errorTrend     []float64
		mutex          sync.RWMutex
	}
}

// ErrorRecord 错误记录
type ErrorRecord struct {
	Timestamp   time.Time
	ConnKey     string
	ErrorType   ErrorType
	ErrorMsg    string
	RetryCount  int
	Resolution  string
}

// NewEnhancedErrorHandler 创建增强错误处理器
func NewEnhancedErrorHandler(logger *logrus.Logger, config *BitgetConfig) *EnhancedErrorHandler {
	handler := &EnhancedErrorHandler{
		logger: logger,
		config: config,
	}

	// 初始化错误统计
	handler.errorStats.errorsByType = make(map[ErrorType]int64)

	// 初始化错误模式库
	handler.initErrorPatterns()

	logger.Info("✅ 增强错误处理器初始化完成")
	return handler
}

// initErrorPatterns 初始化错误模式库
func (h *EnhancedErrorHandler) initErrorPatterns() {
	h.errorPatterns = []ErrorPattern{
		{
			Pattern:     "websocket: close 1006",
			ErrorType:   ErrorTypeWebSocket,
			Severity:    4,
			RetryDelay:  10 * time.Second,
			MaxRetries:  5,
			Description: "WebSocket异常关闭 - 通常由网络中断或服务器重启引起",
		},
		{
			Pattern:     "unexpected EOF",
			ErrorType:   ErrorTypeNetwork,
			Severity:    4,
			RetryDelay:  8 * time.Second,
			MaxRetries:  5,
			Description: "网络连接意外中断",
		},
		{
			Pattern:     "connection refused",
			ErrorType:   ErrorTypeNetwork,
			Severity:    5,
			RetryDelay:  30 * time.Second,
			MaxRetries:  3,
			Description: "连接被拒绝 - 可能服务器不可用",
		},
		{
			Pattern:     "timeout",
			ErrorType:   ErrorTypeTimeout,
			Severity:    3,
			RetryDelay:  5 * time.Second,
			MaxRetries:  8,
			Description: "连接或读取超时",
		},
		{
			Pattern:     "no such host",
			ErrorType:   ErrorTypeNetwork,
			Severity:    5,
			RetryDelay:  60 * time.Second,
			MaxRetries:  2,
			Description: "DNS解析失败",
		},
		{
			Pattern:     "rate limit",
			ErrorType:   ErrorTypeRateLimit,
			Severity:    3,
			RetryDelay:  60 * time.Second,
			MaxRetries:  3,
			Description: "API速率限制",
		},
		{
			Pattern:     "unauthorized",
			ErrorType:   ErrorTypeAuth,
			Severity:    5,
			RetryDelay:  300 * time.Second,
			MaxRetries:  1,
			Description: "认证失败",
		},
	}
}

// AnalyzeError 分析错误并返回处理策略
func (h *EnhancedErrorHandler) AnalyzeError(connKey string, err error, retryCount int) *ErrorStrategy {
	if err == nil {
		return nil
	}

	errorMsg := err.Error()
	errorType := h.classifyError(errorMsg)
	pattern := h.findMatchingPattern(errorMsg)

	// 记录错误
	h.recordError(connKey, errorType, errorMsg, retryCount)

	// 生成处理策略
	strategy := &ErrorStrategy{
		ErrorType:    errorType,
		Severity:     pattern.Severity,
		ShouldRetry:  retryCount < pattern.MaxRetries,
		RetryDelay:   h.calculateRetryDelay(pattern, retryCount, connKey),
		MaxRetries:   pattern.MaxRetries,
		Description:  pattern.Description,
		Suggestions:  h.generateSuggestions(errorType, errorMsg),
	}

	// 记录详细的错误分析日志
	h.logErrorAnalysis(connKey, errorMsg, strategy)

	return strategy
}

// ErrorStrategy 错误处理策略
type ErrorStrategy struct {
	ErrorType    ErrorType
	Severity     int
	ShouldRetry  bool
	RetryDelay   time.Duration
	MaxRetries   int
	Description  string
	Suggestions  []string
}

// classifyError 分类错误
func (h *EnhancedErrorHandler) classifyError(errorMsg string) ErrorType {
	errorMsg = strings.ToLower(errorMsg)

	if strings.Contains(errorMsg, "websocket") {
		return ErrorTypeWebSocket
	}
	if strings.Contains(errorMsg, "timeout") || strings.Contains(errorMsg, "deadline") {
		return ErrorTypeTimeout
	}
	if strings.Contains(errorMsg, "connection") || strings.Contains(errorMsg, "network") || 
	   strings.Contains(errorMsg, "eof") || strings.Contains(errorMsg, "refused") {
		return ErrorTypeNetwork
	}
	if strings.Contains(errorMsg, "unauthorized") || strings.Contains(errorMsg, "forbidden") {
		return ErrorTypeAuth
	}
	if strings.Contains(errorMsg, "rate") || strings.Contains(errorMsg, "limit") {
		return ErrorTypeRateLimit
	}

	return ErrorTypeUnknown
}

// findMatchingPattern 查找匹配的错误模式
func (h *EnhancedErrorHandler) findMatchingPattern(errorMsg string) ErrorPattern {
	errorMsg = strings.ToLower(errorMsg)

	for _, pattern := range h.errorPatterns {
		if strings.Contains(errorMsg, strings.ToLower(pattern.Pattern)) {
			return pattern
		}
	}

	// 返回默认模式
	return ErrorPattern{
		Pattern:     "unknown",
		ErrorType:   ErrorTypeUnknown,
		Severity:    3,
		RetryDelay:  10 * time.Second,
		MaxRetries:  5,
		Description: "未知错误类型",
	}
}

// calculateRetryDelay 计算重试延迟
func (h *EnhancedErrorHandler) calculateRetryDelay(pattern ErrorPattern, retryCount int, connKey string) time.Duration {
	baseDelay := pattern.RetryDelay

	// 指数退避（温和）
	multiplier := 1.0 + float64(retryCount)*0.3
	if multiplier > 3.0 {
		multiplier = 3.0
	}

	// 根据错误严重程度调整
	severityMultiplier := 1.0 + float64(pattern.Severity-1)*0.2

	// 连接类型优先级调整
	priorityMultiplier := 1.0
	if strings.Contains(connKey, "trade") {
		priorityMultiplier = 0.7 // 交易数据优先级高，延迟短
	} else if strings.Contains(connKey, "books1") {
		priorityMultiplier = 0.9
	}

	finalDelay := time.Duration(float64(baseDelay) * multiplier * severityMultiplier * priorityMultiplier)

	// 限制延迟范围
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	return finalDelay
}

// generateSuggestions 生成修复建议
func (h *EnhancedErrorHandler) generateSuggestions(errorType ErrorType, errorMsg string) []string {
	suggestions := []string{}

	switch errorType {
	case ErrorTypeWebSocket:
		suggestions = append(suggestions, 
			"检查网络连接稳定性",
			"考虑增加WebSocket超时时间",
			"检查防火墙设置",
		)
	case ErrorTypeNetwork:
		suggestions = append(suggestions,
			"检查网络连接",
			"验证DNS设置",
			"检查代理配置",
		)
	case ErrorTypeTimeout:
		suggestions = append(suggestions,
			"增加连接超时时间",
			"检查网络延迟",
			"优化网络配置",
		)
	case ErrorTypeRateLimit:
		suggestions = append(suggestions,
			"减少请求频率",
			"增加重试间隔",
			"检查API配额",
		)
	case ErrorTypeAuth:
		suggestions = append(suggestions,
			"检查API密钥",
			"验证权限设置",
			"检查账户状态",
		)
	}

	return suggestions
}

// recordError 记录错误
func (h *EnhancedErrorHandler) recordError(connKey string, errorType ErrorType, errorMsg string, retryCount int) {
	h.errorStats.mutex.Lock()
	defer h.errorStats.mutex.Unlock()

	h.errorStats.totalErrors++
	h.errorStats.errorsByType[errorType]++
	h.errorStats.lastErrorTime = time.Now()

	// 记录详细错误
	errorRecord := ErrorRecord{
		Timestamp:  time.Now(),
		ConnKey:    connKey,
		ErrorType:  errorType,
		ErrorMsg:   errorMsg,
		RetryCount: retryCount,
	}

	h.errorStats.recentErrors = append(h.errorStats.recentErrors, errorRecord)

	// 只保留最近100个错误记录
	if len(h.errorStats.recentErrors) > 100 {
		h.errorStats.recentErrors = h.errorStats.recentErrors[1:]
	}
}

// logErrorAnalysis 记录错误分析日志
func (h *EnhancedErrorHandler) logErrorAnalysis(connKey string, errorMsg string, strategy *ErrorStrategy) {
	logLevel := logrus.InfoLevel
	if strategy.Severity >= 4 {
		logLevel = logrus.ErrorLevel
	} else if strategy.Severity >= 3 {
		logLevel = logrus.WarnLevel
	}

	h.logger.WithFields(logrus.Fields{
		"conn_key":     connKey,
		"error_type":   h.errorTypeToString(strategy.ErrorType),
		"severity":     strategy.Severity,
		"should_retry": strategy.ShouldRetry,
		"retry_delay":  strategy.RetryDelay,
		"max_retries":  strategy.MaxRetries,
	}).Log(logLevel, fmt.Sprintf("🔍 [错误分析] %s: %s", strategy.Description, errorMsg))

	// 输出修复建议
	if len(strategy.Suggestions) > 0 {
		h.logger.Infof("💡 [修复建议] 连接 %s: %v", connKey, strategy.Suggestions)
	}
}

// errorTypeToString 错误类型转字符串
func (h *EnhancedErrorHandler) errorTypeToString(errorType ErrorType) string {
	switch errorType {
	case ErrorTypeNetwork:
		return "网络错误"
	case ErrorTypeWebSocket:
		return "WebSocket错误"
	case ErrorTypeTimeout:
		return "超时错误"
	case ErrorTypeAuth:
		return "认证错误"
	case ErrorTypeRateLimit:
		return "限流错误"
	default:
		return "未知错误"
	}
}

// GetErrorStats 获取错误统计
func (h *EnhancedErrorHandler) GetErrorStats() map[string]interface{} {
	h.errorStats.mutex.RLock()
	defer h.errorStats.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_errors"] = h.errorStats.totalErrors
	stats["last_error_time"] = h.errorStats.lastErrorTime
	stats["errors_by_type"] = make(map[string]int64)

	for errorType, count := range h.errorStats.errorsByType {
		stats["errors_by_type"].(map[string]int64)[h.errorTypeToString(errorType)] = count
	}

	return stats
}
