package main

import (
	"context"
	"math"
	"math/rand"
	"net"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// EnhancedStabilityManager 增强稳定性管理器
type EnhancedStabilityManager struct {
	client *BitgetClient
	logger *logrus.Logger
	config *BitgetConfig

	// 连接池管理
	connectionPool struct {
		activeConnections  map[string]*StabilityInfo
		connectionQueue    []string
		maxConcurrentConns int
		reconnectThrottle  time.Duration
		lastReconnectTime  time.Time
		mutex              sync.RWMutex
	}

	// 网络质量监控
	networkQuality struct {
		avgLatency        time.Duration
		packetLossRate    float64
		connectionSuccess float64
		lastQualityCheck  time.Time
		qualityTrend      []float64
		mutex             sync.RWMutex
	}

	// 自适应参数
	adaptiveParams struct {
		currentBatchSize    int
		currentPingInterval time.Duration
		currentTimeout      time.Duration
		adaptationEnabled   bool
		lastAdaptation      time.Time
		mutex               sync.RWMutex
	}
}

// StabilityInfo 连接稳定性信息
type StabilityInfo struct {
	ConnKey            string
	LastStableTime     time.Time
	StabilityScore     float64
	ReconnectFrequency float64
	NetworkLatency     time.Duration
	ErrorPattern       []string
	RecoveryStrategy   string
	AdaptiveDelay      time.Duration
}

// NewEnhancedStabilityManager 创建增强稳定性管理器
func NewEnhancedStabilityManager(client *BitgetClient, logger *logrus.Logger, config *BitgetConfig) *EnhancedStabilityManager {
	manager := &EnhancedStabilityManager{
		client: client,
		logger: logger,
		config: config,
	}

	// 初始化连接池
	manager.connectionPool.activeConnections = make(map[string]*StabilityInfo)
	manager.connectionPool.maxConcurrentConns = 2               // 降低并发连接数
	manager.connectionPool.reconnectThrottle = 10 * time.Second // 增加重连节流

	// 初始化自适应参数
	manager.adaptiveParams.currentBatchSize = config.BatchSize
	manager.adaptiveParams.currentPingInterval = config.PingInterval
	manager.adaptiveParams.currentTimeout = config.ConnectionTimeout
	manager.adaptiveParams.adaptationEnabled = true

	logger.Info("✅ 增强稳定性管理器初始化完成")
	return manager
}

// CreateStabilizedWebSocketDialer 创建稳定化的WebSocket拨号器
func (m *EnhancedStabilityManager) CreateStabilizedWebSocketDialer(connKey string) *websocket.Dialer {
	// 根据连接历史调整参数
	stabilityInfo := m.getStabilityInfo(connKey)

	// 基础超时时间
	handshakeTimeout := 60 * time.Second
	readBufferSize := 16384
	writeBufferSize := 16384

	// 根据稳定性调整参数
	if stabilityInfo.StabilityScore < 0.5 {
		handshakeTimeout = 90 * time.Second // 不稳定连接使用更长超时
		readBufferSize = 32768              // 增大缓冲区
		writeBufferSize = 32768
	}

	return &websocket.Dialer{
		HandshakeTimeout:  handshakeTimeout,
		EnableCompression: false, // 禁用压缩减少CPU负载
		ReadBufferSize:    readBufferSize,
		WriteBufferSize:   writeBufferSize,
		NetDialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			dialer := &net.Dialer{
				Timeout:   45 * time.Second, // 增加网络连接超时
				KeepAlive: 60 * time.Second, // 增加TCP KeepAlive
				// 启用TCP_NODELAY减少延迟
				Control: func(network, address string, c syscall.RawConn) error {
					return c.Control(func(fd uintptr) {
						syscall.SetsockoptInt(int(fd), syscall.IPPROTO_TCP, syscall.TCP_NODELAY, 1)
					})
				},
			}
			return dialer.DialContext(ctx, network, addr)
		},
	}
}

// CalculateAdaptiveReconnectDelay 计算自适应重连延迟
func (m *EnhancedStabilityManager) CalculateAdaptiveReconnectDelay(connKey string, failureCount int) time.Duration {
	stabilityInfo := m.getStabilityInfo(connKey)

	// 基础延迟（更保守的指数退避）
	baseDelay := time.Duration(math.Min(
		float64(5*time.Second)*math.Pow(1.5, float64(failureCount)),
		float64(5*time.Minute),
	))

	// 网络质量调整
	m.networkQuality.mutex.RLock()
	networkMultiplier := 1.0
	if m.networkQuality.connectionSuccess < 0.7 {
		networkMultiplier = 2.0 // 网络质量差时延长延迟
	} else if m.networkQuality.connectionSuccess > 0.9 {
		networkMultiplier = 0.7 // 网络质量好时缩短延迟
	}
	m.networkQuality.mutex.RUnlock()

	// 稳定性评分调整
	stabilityMultiplier := 1.0
	if stabilityInfo.StabilityScore < 0.3 {
		stabilityMultiplier = 3.0 // 不稳定连接大幅延长
	} else if stabilityInfo.StabilityScore < 0.6 {
		stabilityMultiplier = 1.8
	} else if stabilityInfo.StabilityScore > 0.8 {
		stabilityMultiplier = 0.6 // 稳定连接缩短延迟
	}

	// 连接类型优先级调整
	priorityMultiplier := m.getPriorityMultiplier(connKey)

	// 时间分散（避免同时重连）
	disperseDelay := m.calculateDisperseDelay(connKey)

	// 随机抖动（30%-70%）
	jitterPercent := 0.3 + rand.Float64()*0.4
	jitter := time.Duration(float64(baseDelay) * jitterPercent)

	// 计算最终延迟
	finalDelay := time.Duration(
		float64(baseDelay)*networkMultiplier*stabilityMultiplier*priorityMultiplier,
	) + disperseDelay + jitter

	// 确保延迟范围合理
	minDelay := 5 * time.Second
	maxDelay := 10 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	// 更新稳定性信息
	m.updateStabilityInfo(connKey, false, finalDelay)

	m.logger.Infof("🧮 [稳定性] 连接 %s 自适应延迟: 基础=%v, 网络倍数=%.1f, 稳定性倍数=%.1f, 优先级倍数=%.1f, 分散=%v, 抖动=%v, 最终=%v",
		connKey, baseDelay, networkMultiplier, stabilityMultiplier, priorityMultiplier, disperseDelay, jitter, finalDelay)

	return finalDelay
}

// ShouldAllowReconnect 判断是否允许重连
func (m *EnhancedStabilityManager) ShouldAllowReconnect(connKey string) bool {
	m.connectionPool.mutex.Lock()
	defer m.connectionPool.mutex.Unlock()

	// 检查并发连接限制
	activeCount := len(m.connectionPool.activeConnections)
	if activeCount >= m.connectionPool.maxConcurrentConns {
		m.logger.Warnf("🚦 [稳定性] 连接 %s 等待：当前活跃重连数 %d/%d",
			connKey, activeCount, m.connectionPool.maxConcurrentConns)
		return false
	}

	// 检查重连节流
	now := time.Now()
	if now.Sub(m.connectionPool.lastReconnectTime) < m.connectionPool.reconnectThrottle {
		remaining := m.connectionPool.reconnectThrottle - now.Sub(m.connectionPool.lastReconnectTime)
		m.logger.Warnf("🚦 [稳定性] 连接 %s 节流等待：还需等待 %v", connKey, remaining)
		return false
	}

	// 注册活跃连接
	m.connectionPool.activeConnections[connKey] = m.getStabilityInfo(connKey)
	m.connectionPool.lastReconnectTime = now

	m.logger.Infof("✅ [稳定性] 连接 %s 获得重连许可", connKey)
	return true
}

// OnReconnectComplete 重连完成回调
func (m *EnhancedStabilityManager) OnReconnectComplete(connKey string, success bool) {
	m.connectionPool.mutex.Lock()
	delete(m.connectionPool.activeConnections, connKey)
	m.connectionPool.mutex.Unlock()

	// 更新网络质量统计
	m.updateNetworkQuality(success)

	// 更新稳定性信息
	m.updateStabilityInfo(connKey, success, 0)

	if success {
		m.logger.Infof("✅ [稳定性] 连接 %s 重连成功", connKey)
	} else {
		m.logger.Warnf("❌ [稳定性] 连接 %s 重连失败", connKey)
	}
}

// getStabilityInfo 获取连接稳定性信息
func (m *EnhancedStabilityManager) getStabilityInfo(connKey string) *StabilityInfo {
	// 这里应该从持久化存储或内存缓存中获取
	// 简化实现，返回默认值
	return &StabilityInfo{
		ConnKey:        connKey,
		StabilityScore: 0.5,
		LastStableTime: time.Now().Add(-time.Hour),
	}
}

// updateStabilityInfo 更新稳定性信息
func (m *EnhancedStabilityManager) updateStabilityInfo(connKey string, success bool, delay time.Duration) {
	// 实现稳定性信息更新逻辑
	// 这里可以记录到数据库或内存缓存
}

// updateNetworkQuality 更新网络质量
func (m *EnhancedStabilityManager) updateNetworkQuality(success bool) {
	m.networkQuality.mutex.Lock()
	defer m.networkQuality.mutex.Unlock()

	// 使用指数移动平均更新成功率
	alpha := 0.1
	if success {
		m.networkQuality.connectionSuccess = m.networkQuality.connectionSuccess*(1-alpha) + 1.0*alpha
	} else {
		m.networkQuality.connectionSuccess = m.networkQuality.connectionSuccess*(1-alpha) + 0.0*alpha
	}

	m.networkQuality.lastQualityCheck = time.Now()
}

// getPriorityMultiplier 获取优先级倍数
func (m *EnhancedStabilityManager) getPriorityMultiplier(connKey string) float64 {
	if contains(connKey, "trade") {
		return 0.4 // 交易数据最高优先级，最短延迟
	} else if contains(connKey, "books1") {
		return 0.7 // 盘口数据次优先级
	} else if contains(connKey, "ticker") {
		return 1.0 // 资金费率数据正常优先级
	}
	return 1.0
}

// calculateDisperseDelay 计算分散延迟
func (m *EnhancedStabilityManager) calculateDisperseDelay(connKey string) time.Duration {
	// 简单的分散策略：基于连接名称的哈希值
	hash := 0
	for _, c := range connKey {
		hash = hash*31 + int(c)
	}

	// 0-15秒的分散延迟
	disperseSeconds := hash % 16
	return time.Duration(disperseSeconds) * time.Second
}

// contains 辅助函数
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}
