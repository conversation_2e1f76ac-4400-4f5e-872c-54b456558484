#!/bin/bash

# 启动监控的便捷脚本

echo "🚀 启动 Bitget 监控系统"
echo "======================"

# 检查监控是否已经在运行
if pgrep -f "monitor.sh start" > /dev/null; then
    echo "⚠️  监控已经在运行"
    echo "💡 查看监控状态: ./monitor.sh check"
    echo "💡 查看监控日志: tail -f logs/monitor.log"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动监控（后台运行）
echo "🔄 启动监控进程..."
nohup ./monitor.sh start > /dev/null 2>&1 &
MONITOR_PID=$!

# 等待一下确保监控启动
sleep 2

# 检查监控是否成功启动
if ps -p $MONITOR_PID > /dev/null 2>&1; then
    echo "✅ 监控已启动 (PID: $MONITOR_PID)"
    echo ""
    echo "📊 监控信息:"
    echo "   • 监控日志: logs/monitor.log"
    echo "   • 程序日志: logs/bitget.log"
    echo "   • 检查间隔: 60秒"
    echo ""
    echo "💡 常用命令:"
    echo "   查看监控日志: tail -f logs/monitor.log"
    echo "   查看程序日志: tail -f logs/bitget.log"
    echo "   检查状态:     ./monitor.sh check"
    echo "   停止监控:     ./monitor.sh stop"
    echo ""
    echo "🎯 监控系统已启动，将自动处理程序故障"
else
    echo "❌ 监控启动失败"
    exit 1
fi
