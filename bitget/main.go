package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/tidwall/gjson"
)

// EnhancedReconnectManager 增强重连管理器
type EnhancedReconnectManager struct {
	client *BitgetClient
	logger *logrus.Logger
	config *BitgetConfig

	// 全局重连统计
	globalStats struct {
		totalReconnects      int64
		successfulReconnects int64
		failedReconnects     int64
		averageReconnectTime time.Duration
		lastMassReconnect    time.Time
		disperseEnabled      bool
		mutex                sync.RWMutex
	}

	// 连接分散控制
	disperseManager struct {
		activeConnections  map[string]time.Time // 正在连接的连接及其开始时间
		connectionQueues   map[string][]string  // 按组分类的连接队列
		lastDispatchTime   time.Time            // 上次分发时间
		maxConcurrentConns int                  // 最大并发连接数
		disperseInterval   time.Duration        // 分散间隔
		burstAllowance     int                  // 突发允许量
		mutex              sync.RWMutex
	}

	// 健康评分系统
	healthManager struct {
		connectionHealthMap map[string]*ConnectionHealth
		mutex               sync.RWMutex
	}
}

// ConnectionHealth 连接健康状态
type ConnectionHealth struct {
	ConnKey               string
	HealthScore           float64
	StabilityScore        float64
	LastHealthCheck       time.Time
	ConnectSuccessRate    float64
	AverageConnectionTime time.Duration
	RecentFailures        []time.Time
	QualityTrend          []float64
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// ConnectionInfo 连接信息
type ConnectionInfo struct {
	conn           *websocket.Conn
	state          ConnectionState
	lastPingTime   time.Time
	lastPongTime   time.Time
	reconnectCount int
	symbols        []BitgetSymbol
	channel        string
	messageHandler func(*websocket.Conn, []byte)
	heartbeatStop  chan struct{}

	// 连接质量监控
	connectionQuality   float64       // 连接质量评分 (0-1)
	avgPongDelay        time.Duration // 平均pong延迟
	consecutiveTimeouts int           // 连续超时次数
	lastQualityCheck    time.Time     // 上次质量检查时间
	messageCount        int64         // 消息计数
	lastMessageTime     time.Time     // 最后消息时间

	// 增强重连策略字段
	lastReconnectTime    time.Time     // 上次重连时间
	consecutiveFailures  int           // 连续失败次数
	totalConnectAttempts int           // 总连接尝试次数
	healthScore          float64       // 健康评分 (0-1)
	stabilityScore       float64       // 稳定性评分 (0-1)
	reconnectBackoffBase time.Duration // 重连退避基础时间
	lastSuccessTime      time.Time     // 上次成功时间
	connectionLifetime   time.Duration // 连接生存时间

	// 连接分散策略字段
	connectionGroup     string        // 连接组标识
	priorityLevel       int           // 优先级 (1-5, 1最高)
	disperseDelay       time.Duration // 分散延迟
	maxBurstConnections int           // 最大突发连接数

	mutex sync.RWMutex
}

// BitgetSymbol Bitget交易对信息
type BitgetSymbol struct {
	Symbol    string `json:"symbol"`
	BaseCoin  string `json:"baseCoin"`
	QuoteCoin string `json:"quoteCoin"`
	Status    string `json:"status"`
}

// BitgetClient Bitget WebSocket客户端
type BitgetClient struct {
	config      *BitgetConfig
	logger      *logrus.Logger
	dataHandler BitgetDataHandler

	// 连接管理
	connections     map[string]*ConnectionInfo
	connectionMutex sync.RWMutex

	// 增强ping/pong管理器
	pingPongManager *EnhancedPingPongManager

	// 增强重连管理器
	reconnectManager *EnhancedReconnectManager

	// Kafka生产者
	kafkaProducer *KafkaProducer

	// 统计信息
	tradeCount   int64
	tickerCount  int64
	depthCount   int64
	fundingCount int64

	// 控制信号
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewBitgetClient 创建新的Bitget客户端
func NewBitgetClient(config *BitgetConfig, logger *logrus.Logger, dataHandler BitgetDataHandler) *BitgetClient {
	ctx, cancel := context.WithCancel(context.Background())

	client := &BitgetClient{
		config:       config,
		logger:       logger,
		dataHandler:  dataHandler,
		connections:  make(map[string]*ConnectionInfo),
		tradeCount:   0,
		tickerCount:  0,
		depthCount:   0,
		fundingCount: 0,
		ctx:          ctx,
		cancel:       cancel,
	}

	// 创建增强ping/pong管理器
	client.pingPongManager = NewEnhancedPingPongManager(client, logger, config)

	// 创建增强重连管理器
	client.reconnectManager = NewEnhancedReconnectManager(client, logger, config)

	// 如果启用Kafka，创建Kafka生产者
	if config.Kafka.Enabled {
		kafkaProducer, err := NewKafkaProducer(&config.Kafka, logger)
		if err != nil {
			logger.WithError(err).Warn("⚠️ Kafka生产者创建失败，将跳过Kafka发送")
		} else {
			client.kafkaProducer = kafkaProducer
			logger.Info("✅ Kafka生产者创建成功")
		}
	}

	// 启动连接监控
	go client.monitorConnections()

	// 启动统计打印
	go client.printStatistics()

	return client
}

// NewEnhancedReconnectManager 创建增强重连管理器
func NewEnhancedReconnectManager(client *BitgetClient, logger *logrus.Logger, config *BitgetConfig) *EnhancedReconnectManager {
	manager := &EnhancedReconnectManager{
		client: client,
		logger: logger,
		config: config,
	}

	// 初始化分散管理器
	manager.disperseManager.activeConnections = make(map[string]time.Time)
	manager.disperseManager.connectionQueues = make(map[string][]string)
	manager.disperseManager.maxConcurrentConns = 3             // 最多同时连接3个
	manager.disperseManager.disperseInterval = 2 * time.Second // 每2秒分发一批
	manager.disperseManager.burstAllowance = 2                 // 允许突发2个连接

	// 初始化健康管理器
	manager.healthManager.connectionHealthMap = make(map[string]*ConnectionHealth)

	// 启用分散策略
	manager.globalStats.disperseEnabled = true

	logger.Info("✅ 增强重连管理器初始化完成")
	return manager
}

// ShouldReconnectWithStrategy 使用增强策略判断是否应该重连
func (m *EnhancedReconnectManager) ShouldReconnectWithStrategy(connKey string, connInfo *ConnectionInfo) bool {
	// 无限重连策略：永远尝试重连，但会有智能延迟
	if m.config.ReconnectStrategy == BitgetReconnectStrategyInfinite {
		return true
	}

	connInfo.mutex.RLock()
	consecutiveFailures := connInfo.consecutiveFailures
	totalAttempts := connInfo.totalConnectAttempts
	healthScore := connInfo.healthScore
	connInfo.mutex.RUnlock()

	// 基于健康评分的智能重连策略
	maxAttempts := m.config.MaxReconnectAttempts

	// 如果连接健康评分高，增加重试次数
	if healthScore > 0.8 {
		maxAttempts = maxAttempts * 2
	} else if healthScore > 0.6 {
		maxAttempts = int(float64(maxAttempts) * 1.5)
	}

	// 对于重要连接（trade数据），给予更多重试机会
	if strings.Contains(connKey, "trade") {
		maxAttempts = maxAttempts * 3
	} else if strings.Contains(connKey, "books1") {
		maxAttempts = maxAttempts * 2
	}

	m.logger.Debugf("🔍 连接 %s 重连策略检查: 连续失败=%d, 总尝试=%d, 健康评分=%.2f, 最大尝试=%d",
		connKey, consecutiveFailures, totalAttempts, healthScore, maxAttempts)

	return totalAttempts < maxAttempts
}

// CalculateEnhancedReconnectDelay 计算增强重连延迟
func (m *EnhancedReconnectManager) CalculateEnhancedReconnectDelay(connKey string, connInfo *ConnectionInfo) time.Duration {
	connInfo.mutex.RLock()
	consecutiveFailures := connInfo.consecutiveFailures
	healthScore := connInfo.healthScore
	connectionGroup := connInfo.connectionGroup
	priorityLevel := connInfo.priorityLevel
	connInfo.mutex.RUnlock()

	// 基础延迟计算（指数退避）
	baseDelay := m.config.ReconnectDelay
	if m.config.ReconnectStrategy == BitgetReconnectStrategyExponential {
		maxBackoff := min(consecutiveFailures, 8) // 最多8级退避
		baseDelay = baseDelay * time.Duration(1<<uint(maxBackoff))
	}

	// 限制最大延迟
	if baseDelay > m.config.MaxReconnectDelay {
		baseDelay = m.config.MaxReconnectDelay
	}

	// 基于健康评分调整延迟
	healthMultiplier := 1.0
	if healthScore < 0.3 {
		healthMultiplier = 3.0 // 不健康的连接延迟更长
	} else if healthScore < 0.6 {
		healthMultiplier = 2.0
	} else if healthScore > 0.8 {
		healthMultiplier = 0.5 // 健康的连接延迟更短
	}

	// 基于优先级调整延迟
	priorityMultiplier := 1.0
	if priorityLevel == 1 { // 最高优先级
		priorityMultiplier = 0.3
	} else if priorityLevel == 2 {
		priorityMultiplier = 0.6
	} else if priorityLevel >= 4 {
		priorityMultiplier = 2.0
	}

	// 连接分散策略：检查是否需要延迟避免重连风暴
	disperseDelay := m.calculateDisperseDelay(connKey, connectionGroup)

	// 添加随机抖动（20%-50%的随机变化）
	jitterPercent := 0.2 + rand.Float64()*0.3
	jitter := time.Duration(float64(baseDelay) * jitterPercent)

	// 计算最终延迟
	finalDelay := time.Duration(float64(baseDelay)*healthMultiplier*priorityMultiplier) + disperseDelay + jitter

	// 确保最小延迟
	minDelay := 3 * time.Second
	if finalDelay < minDelay {
		finalDelay = minDelay
	}

	// 确保最大延迟
	maxDelay := 10 * time.Minute
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	m.logger.Infof("🧮 连接 %s 重连延迟计算: 基础=%v, 健康倍数=%.1f, 优先级倍数=%.1f, 分散延迟=%v, 抖动=%v, 最终=%v",
		connKey, baseDelay, healthMultiplier, priorityMultiplier, disperseDelay, jitter, finalDelay)

	return finalDelay
}

// calculateDisperseDelay 计算分散延迟
func (m *EnhancedReconnectManager) calculateDisperseDelay(connKey, connectionGroup string) time.Duration {
	if !m.globalStats.disperseEnabled {
		return 0
	}

	m.disperseManager.mutex.RLock()
	activeCount := len(m.disperseManager.activeConnections)
	lastDispatch := m.disperseManager.lastDispatchTime
	maxConcurrent := m.disperseManager.maxConcurrentConns
	disperseInterval := m.disperseManager.disperseInterval
	m.disperseManager.mutex.RUnlock()

	now := time.Now()

	// 如果活跃连接数超过限制，计算等待时间
	if activeCount >= maxConcurrent {
		// 找到最早应该完成的连接
		m.disperseManager.mutex.RLock()
		var earliestFinish time.Time
		for _, startTime := range m.disperseManager.activeConnections {
			expectedFinish := startTime.Add(30 * time.Second) // 假设连接需要30秒完成
			if earliestFinish.IsZero() || expectedFinish.Before(earliestFinish) {
				earliestFinish = expectedFinish
			}
		}
		m.disperseManager.mutex.RUnlock()

		if !earliestFinish.IsZero() && earliestFinish.After(now) {
			waitTime := earliestFinish.Sub(now)
			m.logger.Debugf("🚦 连接 %s 需要等待活跃连接完成: %v", connKey, waitTime)
			return waitTime
		}
	}

	// 检查分散间隔
	if now.Sub(lastDispatch) < disperseInterval {
		remainingInterval := disperseInterval - now.Sub(lastDispatch)
		m.logger.Debugf("🚦 连接 %s 分散间隔等待: %v", connKey, remainingInterval)
		return remainingInterval
	}

	return 0
}

// RegisterConnection 注册连接到分散管理器
func (m *EnhancedReconnectManager) RegisterConnection(connKey string) {
	m.disperseManager.mutex.Lock()
	defer m.disperseManager.mutex.Unlock()

	m.disperseManager.activeConnections[connKey] = time.Now()
	m.disperseManager.lastDispatchTime = time.Now()

	m.logger.Debugf("📝 注册活跃连接: %s, 当前活跃数: %d", connKey, len(m.disperseManager.activeConnections))
}

// UnregisterConnection 从分散管理器移除连接
func (m *EnhancedReconnectManager) UnregisterConnection(connKey string) {
	m.disperseManager.mutex.Lock()
	defer m.disperseManager.mutex.Unlock()

	delete(m.disperseManager.activeConnections, connKey)

	m.logger.Debugf("🗑️ 移除活跃连接: %s, 当前活跃数: %d", connKey, len(m.disperseManager.activeConnections))
}

// UpdateConnectionHealth 更新连接健康状态
func (m *EnhancedReconnectManager) UpdateConnectionHealth(connKey string, success bool, connectionTime time.Duration) {
	m.healthManager.mutex.Lock()
	defer m.healthManager.mutex.Unlock()

	health, exists := m.healthManager.connectionHealthMap[connKey]
	if !exists {
		health = &ConnectionHealth{
			ConnKey:            connKey,
			HealthScore:        0.5,
			StabilityScore:     0.5,
			LastHealthCheck:    time.Now(),
			ConnectSuccessRate: 0.5,
			RecentFailures:     make([]time.Time, 0),
			QualityTrend:       make([]float64, 0),
		}
		m.healthManager.connectionHealthMap[connKey] = health
	}

	// 更新成功率
	alpha := 0.2 // 指数移动平均的学习率
	if success {
		health.ConnectSuccessRate = health.ConnectSuccessRate*(1-alpha) + 1.0*alpha
		health.AverageConnectionTime = time.Duration(float64(health.AverageConnectionTime)*(1-alpha) + float64(connectionTime)*alpha)
	} else {
		health.ConnectSuccessRate = health.ConnectSuccessRate*(1-alpha) + 0.0*alpha
		health.RecentFailures = append(health.RecentFailures, time.Now())

		// 只保留最近1小时的失败记录
		cutoff := time.Now().Add(-1 * time.Hour)
		newFailures := make([]time.Time, 0)
		for _, failTime := range health.RecentFailures {
			if failTime.After(cutoff) {
				newFailures = append(newFailures, failTime)
			}
		}
		health.RecentFailures = newFailures
	}

	// 计算健康评分
	baseScore := health.ConnectSuccessRate

	// 基于最近失败频率调整
	recentFailureCount := len(health.RecentFailures)
	failurePenalty := float64(recentFailureCount) * 0.1
	if failurePenalty > 0.5 {
		failurePenalty = 0.5
	}

	health.HealthScore = baseScore - failurePenalty
	if health.HealthScore < 0 {
		health.HealthScore = 0
	}

	// 计算稳定性评分（基于连接时间的一致性）
	if connectionTime > 0 {
		// 连接时间越稳定，评分越高
		timeVariation := math.Abs(float64(connectionTime-health.AverageConnectionTime)) / float64(health.AverageConnectionTime+1*time.Second)
		health.StabilityScore = 1.0 - math.Min(timeVariation, 1.0)
	}

	// 记录质量趋势
	health.QualityTrend = append(health.QualityTrend, health.HealthScore)
	if len(health.QualityTrend) > 10 {
		health.QualityTrend = health.QualityTrend[1:] // 只保留最近10个记录
	}

	health.LastHealthCheck = time.Now()

	m.logger.Debugf("📊 连接 %s 健康更新: 成功=%v, 健康评分=%.2f, 稳定性=%.2f, 成功率=%.2f, 最近失败=%d",
		connKey, success, health.HealthScore, health.StabilityScore, health.ConnectSuccessRate, recentFailureCount)
}

// GetConnectionHealth 获取连接健康状态
func (m *EnhancedReconnectManager) GetConnectionHealth(connKey string) *ConnectionHealth {
	m.healthManager.mutex.RLock()
	defer m.healthManager.mutex.RUnlock()

	health, exists := m.healthManager.connectionHealthMap[connKey]
	if !exists {
		return &ConnectionHealth{
			ConnKey:            connKey,
			HealthScore:        0.5,
			StabilityScore:     0.5,
			ConnectSuccessRate: 0.5,
		}
	}

	// 返回副本避免并发问题
	return &ConnectionHealth{
		ConnKey:               health.ConnKey,
		HealthScore:           health.HealthScore,
		StabilityScore:        health.StabilityScore,
		LastHealthCheck:       health.LastHealthCheck,
		ConnectSuccessRate:    health.ConnectSuccessRate,
		AverageConnectionTime: health.AverageConnectionTime,
		RecentFailures:        append([]time.Time{}, health.RecentFailures...),
		QualityTrend:          append([]float64{}, health.QualityTrend...),
	}
}

// UpdateGlobalStats 更新全局重连统计
func (m *EnhancedReconnectManager) UpdateGlobalStats(success bool, reconnectTime time.Duration) {
	m.globalStats.mutex.Lock()
	defer m.globalStats.mutex.Unlock()

	m.globalStats.totalReconnects++
	if success {
		m.globalStats.successfulReconnects++
	} else {
		m.globalStats.failedReconnects++
	}

	// 更新平均重连时间
	if reconnectTime > 0 {
		alpha := 0.1
		if m.globalStats.averageReconnectTime == 0 {
			m.globalStats.averageReconnectTime = reconnectTime
		} else {
			m.globalStats.averageReconnectTime = time.Duration(
				float64(m.globalStats.averageReconnectTime)*(1-alpha) + float64(reconnectTime)*alpha)
		}
	}
}

// GetAllSymbols 获取所有永续合约交易对
func (c *BitgetClient) GetAllSymbols() ([]BitgetSymbol, error) {
	url := fmt.Sprintf("%s/api/v2/mix/market/contracts?productType=USDT-FUTURES", c.config.APIBaseURL)

	c.logger.Info("🔍 获取Bitget永续合约交易对列表...")

	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("获取交易对失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 添加调试信息
	c.logger.Debugf("🔍 API响应长度: %d bytes", len(body))
	c.logger.Debugf("🔍 API响应前500字符: %s", string(body[:min(500, len(body))]))

	// 解析响应
	code := gjson.GetBytes(body, "code").String()
	msg := gjson.GetBytes(body, "msg").String()
	c.logger.Debugf("🔍 API返回码: %s, 消息: %s", code, msg)

	if code != "00000" {
		return nil, fmt.Errorf("API返回错误: %s", msg)
	}

	var symbols []BitgetSymbol
	dataArray := gjson.GetBytes(body, "data").Array()
	c.logger.Debugf("🔍 数据数组长度: %d", len(dataArray))

	for i, item := range dataArray {
		symbol := BitgetSymbol{
			Symbol:    item.Get("symbol").String(),
			BaseCoin:  item.Get("baseCoin").String(),
			QuoteCoin: item.Get("quoteCoin").String(),
			Status:    item.Get("symbolStatus").String(), // 修正字段名
		}

		// 调试前几个交易对
		if i < 3 {
			c.logger.Debugf("🔍 交易对 %d: symbol=%s, status=%s", i, symbol.Symbol, symbol.Status)
		}

		// 只添加活跃的交易对
		if symbol.Status == "normal" {
			symbols = append(symbols, symbol)
		}
	}

	c.logger.Infof("✅ 成功获取 %d 个活跃的永续合约交易对", len(symbols))
	return symbols, nil
}

// SubscribeData 订阅数据
func (c *BitgetClient) SubscribeData() error {
	symbols, err := c.GetAllSymbols()
	if err != nil {
		return fmt.Errorf("获取交易对失败: %v", err)
	}

	if len(symbols) == 0 {
		return fmt.Errorf("没有找到活跃的交易对")
	}

	// 过滤交易对
	if len(c.config.SymbolFilter) > 0 {
		symbols = c.filterSymbols(symbols)
		c.logger.Infof("🔍 应用过滤器后剩余 %d 个交易对", len(symbols))
	}

	// 分批订阅 - 使用差异化批次大小策略
	baseBatchSize := c.config.BatchSize
	totalBatches := (len(symbols) + baseBatchSize - 1) / baseBatchSize

	// 限制最大连接数，避免超过Bitget的连接限制
	maxConnections := 75                 // Bitget建议的最大连接数（考虑3种连接类型）
	if totalBatches*3 > maxConnections { // *3是因为有trade、books1和ticker三种连接
		newBatchSize := (len(symbols) + maxConnections/3 - 1) / (maxConnections / 3)
		if newBatchSize > baseBatchSize {
			baseBatchSize = newBatchSize
			totalBatches = (len(symbols) + baseBatchSize - 1) / baseBatchSize
			c.logger.Warnf("⚠️ 调整批处理大小从 %d 到 %d，以限制连接数量", c.config.BatchSize, baseBatchSize)
		}
	}

	// 为高负载批次使用更小的批次大小
	problematicBatches := map[int]int{
		5: 50, // trade_batch_5 使用50个交易对
		6: 50, // trade_batch_6 使用50个交易对
	}
	c.logger.Infof("🎯 使用差异化批次策略，problematic批次将使用更小的批次大小")

	c.logger.Infof("📡 开始订阅数据，总共 %d 个交易对，分 %d 批处理，基础批次大小 %d 个",
		len(symbols), totalBatches, baseBatchSize)

	for i := 0; i < len(symbols); {
		batchNum := i/baseBatchSize + 1

		// 根据批次号确定当前批次大小
		currentBatchSize := baseBatchSize
		if specialSize, exists := problematicBatches[batchNum]; exists {
			currentBatchSize = specialSize
			c.logger.Infof("🎯 批次 %d 使用特殊批次大小: %d", batchNum, currentBatchSize)
		}

		end := i + currentBatchSize
		if end > len(symbols) {
			end = len(symbols)
		}

		batch := symbols[i:end]

		c.logger.Infof("🔄 处理第 %d/%d 批，包含 %d 个交易对", batchNum, totalBatches, len(batch))

		// 为每批创建独立的连接，串行启动避免并发连接限制
		connectionDelay := 3 * time.Second
		c.logger.Infof("⏱️ 使用串行连接模式，连接间隔: %v", connectionDelay)

		if c.config.EnableTradeData {
			c.wg.Add(1)
			go c.subscribeTradeData(batch, batchNum)
			time.Sleep(connectionDelay) // 等待连接建立
		}

		if c.config.EnableBookTicker {
			c.wg.Add(1)
			go c.subscribeBooks1Data(batch, batchNum)
			time.Sleep(connectionDelay) // 等待连接建立
		}

		// 添加资金费率订阅（使用ticker频道）
		c.wg.Add(1)
		go c.subscribeTickerData(batch, batchNum)
		time.Sleep(connectionDelay) // 等待连接建立

		// 批次间额外延迟
		if end < len(symbols) {
			c.logger.Infof("✅ 第 %d 批连接已启动，等待 %v 后处理下一批", batchNum, connectionDelay)
			time.Sleep(connectionDelay)
		}

		i = end // 移动到下一批的起始位置
	}

	// 启动统计和监控
	c.wg.Add(2)
	go c.printStatistics()
	go c.monitorConnections()

	return nil
}

// subscribeTradeData 订阅交易数据
func (c *BitgetClient) subscribeTradeData(symbols []BitgetSymbol, batchNum int) {
	defer c.wg.Done()

	connKey := fmt.Sprintf("trade_batch_%d", batchNum)
	c.subscribeWithRetry(connKey, symbols, "trade", c.handleTradeMessage)
}

// subscribeBooks1Data 订阅一档盘口数据
func (c *BitgetClient) subscribeBooks1Data(symbols []BitgetSymbol, batchNum int) {
	defer c.wg.Done()

	connKey := fmt.Sprintf("books1_batch_%d", batchNum)
	c.subscribeWithRetry(connKey, symbols, "books1", c.handleBooks1Message)
}

// subscribeTickerData 订阅ticker数据（包含资金费率）
func (c *BitgetClient) subscribeTickerData(symbols []BitgetSymbol, batchNum int) {
	defer c.wg.Done()

	connKey := fmt.Sprintf("ticker_batch_%d", batchNum)
	c.subscribeWithRetry(connKey, symbols, "ticker", c.handleTickerMessage)
}

// subscribeWithRetry 订阅数据并处理重连（使用增强重连策略）
func (c *BitgetClient) subscribeWithRetry(connKey string, symbols []BitgetSymbol, channel string, messageHandler func(*websocket.Conn, []byte)) {
	// 创建连接信息
	connInfo := &ConnectionInfo{
		state:                StateDisconnected,
		symbols:              symbols,
		channel:              channel,
		messageHandler:       messageHandler,
		heartbeatStop:        make(chan struct{}, 1), // 使用缓冲通道避免阻塞
		healthScore:          0.5,                    // 初始健康评分
		stabilityScore:       0.5,                    // 初始稳定性评分
		reconnectBackoffBase: 3 * time.Second,        // 初始退避时间
		lastSuccessTime:      time.Now(),             // 初始化为当前时间
	}

	// 设置连接优先级和分组
	if strings.Contains(connKey, "trade") {
		connInfo.priorityLevel = 1 // 交易数据最高优先级
		connInfo.connectionGroup = "trade"
	} else if strings.Contains(connKey, "books1") {
		connInfo.priorityLevel = 2 // 盘口数据次优先级
		connInfo.connectionGroup = "books1"
	} else if strings.Contains(connKey, "ticker") {
		connInfo.priorityLevel = 3 // ticker数据较低优先级
		connInfo.connectionGroup = "ticker"
	}

	c.connectionMutex.Lock()
	c.connections[connKey] = connInfo
	c.connectionMutex.Unlock()

	// 启动增强重连循环
	c.wg.Add(1)
	go func() {
		defer c.wg.Done()

		for {
			select {
			case <-c.ctx.Done():
				c.logger.Debugf("🛑 连接 %s 收到停止信号", connKey)
				c.reconnectManager.UnregisterConnection(connKey)
				return
			default:
			}

			// 使用增强重连策略检查是否应该重连
			if !c.reconnectManager.ShouldReconnectWithStrategy(connKey, connInfo) {
				c.logger.Errorf("🚫 连接 %s 达到最大重连次数，停止重连", connKey)
				c.setConnectionState(connKey, StateStopped)
				c.reconnectManager.UnregisterConnection(connKey)
				return
			}

			// 注册到分散管理器
			c.reconnectManager.RegisterConnection(connKey)

			// 更新连接尝试统计
			connInfo.mutex.Lock()
			connInfo.totalConnectAttempts++
			connInfo.lastReconnectTime = time.Now()
			connInfo.mutex.Unlock()

			// 设置连接状态
			c.setConnectionState(connKey, StateConnecting)
			c.logger.Infof("🔄 连接 %s 开始第 %d 次连接尝试...", connKey, connInfo.totalConnectAttempts)

			// 记录连接开始时间
			connectStartTime := time.Now()

			err := c.connectWithRetry(connKey, connInfo)

			// 计算连接时间
			connectionTime := time.Since(connectStartTime)

			if err != nil {
				c.logger.WithError(err).Errorf("❌ 连接 %s 第 %d 次尝试失败", connKey, connInfo.totalConnectAttempts)

				// 更新失败统计
				connInfo.mutex.Lock()
				connInfo.consecutiveFailures++
				connInfo.mutex.Unlock()

				// 更新健康状态
				c.reconnectManager.UpdateConnectionHealth(connKey, false, connectionTime)
				c.reconnectManager.UpdateGlobalStats(false, connectionTime)

				// 设置重连状态
				c.setConnectionState(connKey, StateReconnecting)

				// 使用增强延迟计算
				delay := c.reconnectManager.CalculateEnhancedReconnectDelay(connKey, connInfo)

				c.logger.Infof("⏳ 连接 %s 将在 %v 后重连 (第%d次尝试)", connKey, delay, connInfo.totalConnectAttempts+1)

				// 从分散管理器移除
				c.reconnectManager.UnregisterConnection(connKey)

				select {
				case <-time.After(delay):
					continue
				case <-c.ctx.Done():
					return
				}
			} else {
				// 连接成功
				c.logger.Infof("✅ 连接 %s 第 %d 次尝试成功，开始接收数据", connKey, connInfo.totalConnectAttempts)

				// 重置失败计数
				connInfo.mutex.Lock()
				connInfo.consecutiveFailures = 0
				connInfo.reconnectCount = 0
				connInfo.lastSuccessTime = time.Now()
				connInfo.connectionLifetime = time.Since(connectStartTime)
				connInfo.mutex.Unlock()

				// 更新健康状态
				c.reconnectManager.UpdateConnectionHealth(connKey, true, connectionTime)
				c.reconnectManager.UpdateGlobalStats(true, connectionTime)

				// 从分散管理器移除（连接已完成）
				c.reconnectManager.UnregisterConnection(connKey)

				// 连接成功后，等待连接断开再重连
				// handleMessages会在连接断开时返回错误
				continue
			}
		}
	}()
}

// connect 建立连接
func (c *BitgetClient) connect(connKey string, connInfo *ConnectionInfo) error {
	// 为problematic连接应用特殊配置
	readBufferSize := 8192
	writeBufferSize := 8192
	handshakeTimeout := 45 * time.Second

	// 为trade_batch_7使用更大的缓冲区和更长的超时
	if connKey == "trade_batch_7" {
		readBufferSize = 16384              // 加倍读缓冲区
		writeBufferSize = 16384             // 加倍写缓冲区
		handshakeTimeout = 60 * time.Second // 更长的握手超时
		c.logger.Infof("🔧 连接 %s 使用增强稳定性配置", connKey)
	}

	// 创建WebSocket连接，使用更稳定的配置
	dialer := websocket.Dialer{
		HandshakeTimeout:  handshakeTimeout,
		EnableCompression: false, // 禁用压缩减少CPU负载
		ReadBufferSize:    readBufferSize,
		WriteBufferSize:   writeBufferSize,
		// 添加更多稳定性配置
		NetDialContext: (&net.Dialer{
			Timeout:   30 * time.Second, // 网络连接超时
			KeepAlive: 30 * time.Second, // TCP KeepAlive
		}).DialContext,
	}

	conn, _, err := dialer.Dial(c.config.WSBaseURL, nil)
	if err != nil {
		// 直接返回原始WebSocket错误，不包装
		return err
	}

	// 设置连接参数，使用更宽松的超时时间
	conn.SetReadDeadline(time.Now().Add(5 * time.Minute))   // 增加读超时到5分钟
	conn.SetWriteDeadline(time.Now().Add(30 * time.Second)) // 写超时30秒

	// 设置pong处理器 - 被动响应服务器ping
	conn.SetPongHandler(func(appData string) error {
		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()
		c.logger.Debugf("💓 连接 %s 收到WebSocket pong响应", connKey)
		return nil
	})

	// 设置ping处理器 - 被动响应服务器ping
	conn.SetPingHandler(func(appData string) error {
		// 立即响应服务器的ping
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			c.logger.WithError(err).Errorf("💔 连接 %s 响应服务器ping失败", connKey)
			return err
		}
		c.logger.Debugf("💓 连接 %s 响应服务器ping", connKey)
		return nil
	})

	// 设置close处理器
	conn.SetCloseHandler(func(code int, text string) error {
		c.logger.Warnf("🔌 连接 %s 被关闭: code=%d, text=%s", connKey, code, text)
		return nil
	})

	// 保存连接
	connInfo.mutex.Lock()
	connInfo.conn = conn
	connInfo.lastPingTime = time.Now()
	connInfo.lastPongTime = time.Now()
	connInfo.mutex.Unlock()

	c.logger.Infof("✅ 连接 %s 建立成功，准备订阅 %s 数据", connKey, connInfo.channel)

	// 构建订阅消息
	subscribeMsg := c.buildSubscribeMessage(connInfo.symbols, connInfo.channel)

	// 发送订阅消息
	if err := conn.WriteMessage(websocket.TextMessage, subscribeMsg); err != nil {
		conn.Close()
		// 直接返回原始错误，不包装
		return err
	}

	c.logger.Infof("📡 连接 %s 订阅消息已发送，包含 %d 个交易对", connKey, len(connInfo.symbols))

	// 等待订阅确认消息，然后设置连接状态为已连接
	// 先启动心跳
	go c.startHeartbeat(connKey, connInfo)

	// 处理消息，在收到第一条数据消息时设置为Connected状态
	return c.handleMessages(connKey, connInfo)
}

// handleMessages 处理消息
func (c *BitgetClient) handleMessages(connKey string, connInfo *ConnectionInfo) error {
	conn := connInfo.conn

	for {
		select {
		case <-c.ctx.Done():
			return nil
		default:
		}

		// 设置读取超时，使用更宽松的时间
		conn.SetReadDeadline(time.Now().Add(5 * time.Minute))

		messageType, message, err := conn.ReadMessage()
		if err != nil {
			// 检查错误类型，提供更详细的错误信息
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure, websocket.CloseNormalClosure) {
				c.logger.WithError(err).Errorf("💔 连接 %s 异常关闭", connKey)
			} else if websocket.IsCloseError(err, websocket.CloseNormalClosure, websocket.CloseGoingAway) {
				c.logger.WithError(err).Infof("🔌 连接 %s 正常关闭", connKey)
			} else {
				// 网络错误或其他错误
				c.logger.WithError(err).Warnf("🌐 连接 %s 网络错误", connKey)
			}

			// 清理连接
			c.cleanupConnection(connKey, connInfo)
			return err
		}

		// 处理不同类型的消息
		switch messageType {
		case websocket.TextMessage:
			messageStr := string(message)

			// 处理pong响应
			if messageStr == "pong" {
				connInfo.mutex.Lock()
				connInfo.lastPongTime = time.Now()
				connInfo.mutex.Unlock()
				c.logger.Debugf("💓 连接 %s 收到pong响应", connKey)
				continue
			}

			// 检查是否是第一次收到数据消息，如果是则设置为Connected状态
			connInfo.mutex.RLock()
			currentState := connInfo.state
			connInfo.mutex.RUnlock()

			if currentState == StateConnecting {
				// 检查是否是有效的数据消息（不是错误消息）
				if !strings.Contains(messageStr, "error") && (strings.Contains(messageStr, "data") || strings.Contains(messageStr, "event")) {
					c.setConnectionState(connKey, StateConnected)
					c.logger.Infof("🎯 连接 %s 收到首条数据，状态设置为Connected", connKey)
				}
			}

			// 更新消息统计
			connInfo.mutex.Lock()
			connInfo.messageCount++
			connInfo.lastMessageTime = time.Now()
			connInfo.mutex.Unlock()

			// 处理业务消息
			connInfo.messageHandler(conn, message)
		case websocket.PingMessage:
			// 被动响应服务器ping - 立即响应保持连接活跃
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PongMessage, message); err != nil {
				c.logger.WithError(err).Errorf("💔 连接 %s 响应服务器ping失败", connKey)
				c.cleanupConnection(connKey, connInfo)
				return err
			}
			// 更新最后pong时间
			connInfo.mutex.Lock()
			connInfo.lastPongTime = time.Now()
			connInfo.mutex.Unlock()
			c.logger.Debugf("💓 连接 %s 响应服务器ping", connKey)
		case websocket.PongMessage:
			// 收到我们主动ping的pong响应
			connInfo.mutex.Lock()
			connInfo.lastPongTime = time.Now()
			connInfo.mutex.Unlock()
			c.logger.Debugf("💓 连接 %s 收到主动ping的pong响应", connKey)
		case websocket.CloseMessage:
			c.logger.Infof("🔌 连接 %s 收到关闭消息", connKey)
			c.cleanupConnection(connKey, connInfo)
			return fmt.Errorf("连接被服务器关闭")
		}
	}
}

// startHeartbeat 启动增强心跳机制
func (c *BitgetClient) startHeartbeat(connKey string, connInfo *ConnectionInfo) {
	// 使用增强的ping/pong管理器
	c.pingPongManager.StartEnhancedHeartbeat(connKey, connInfo)
}

// sendActivePing 主动发送ping
func (c *BitgetClient) sendActivePing(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state == StateDisconnected || state == StateStopped {
		return
	}

	// 发送WebSocket ping帧（更可靠）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("bitget-ping")); err != nil {
		c.logger.WithError(err).Errorf("💔 连接 %s WebSocket ping发送失败", connKey)
		c.cleanupConnection(connKey, connInfo)
		return
	}

	// 同时发送应用层ping（双重保障）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	pingMsg := "ping"
	if err := conn.WriteMessage(websocket.TextMessage, []byte(pingMsg)); err != nil {
		c.logger.WithError(err).Errorf("💔 连接 %s 应用层ping发送失败", connKey)
		c.cleanupConnection(connKey, connInfo)
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	c.logger.Debugf("💓 连接 %s 发送双重ping", connKey)
}

// checkConnectionHealth 检查连接健康状态
func (c *BitgetClient) checkConnectionHealth(connKey string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	connInfo.mutex.RUnlock()

	if conn == nil {
		c.logger.Debugf("🔍 连接 %s 健康检查：连接为空", connKey)
		return false
	}

	if state == StateDisconnected || state == StateStopped {
		c.logger.Debugf("🔍 连接 %s 健康检查：状态为 %v", connKey, state)
		return false
	}

	// 只有在Connected状态下才进行严格的超时检查
	if state == StateConnected {
		now := time.Now()
		pongDelay := now.Sub(lastPongTime)
		pingDelay := now.Sub(lastPingTime)

		// 使用更宽松的超时策略
		maxPongDelay := c.config.PingInterval * 10 // 10倍ping间隔
		maxPingDelay := c.config.PingInterval * 5  // 5倍ping间隔

		if pongDelay > maxPongDelay {
			c.logger.Errorf("💔 连接 %s pong超时: %.1fs (最大允许: %.1fs)",
				connKey, pongDelay.Seconds(), maxPongDelay.Seconds())
			return false
		}

		if pingDelay > maxPingDelay {
			c.logger.Warnf("⚠️ 连接 %s ping间隔过长: %.1fs", connKey, pingDelay.Seconds())
		}

		// 更新连接质量评估
		c.updateConnectionQuality(connKey, connInfo, pongDelay, pingDelay)
	}

	return true
}

// connectWithRetry 带重试的连接建立
func (c *BitgetClient) connectWithRetry(connKey string, connInfo *ConnectionInfo) error {
	maxAttempts := 3    // 每次重连周期内最多尝试3次
	var lastError error // 保存最后一个原始错误

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		c.logger.Infof("🔄 连接 %s 尝试第 %d/%d 次连接", connKey, attempt, maxAttempts)

		err := c.connect(connKey, connInfo)
		if err == nil {
			return nil // 连接成功
		}

		lastError = err // 保存原始错误
		c.logger.WithError(err).Warnf("⚠️ 连接 %s 第 %d 次尝试失败", connKey, attempt)

		// 如果不是最后一次尝试，等待一小段时间再重试
		if attempt < maxAttempts {
			retryDelay := time.Duration(attempt) * 2 * time.Second // 2s, 4s
			c.logger.Infof("⏳ 连接 %s 等待 %v 后进行第 %d 次尝试", connKey, retryDelay, attempt+1)

			select {
			case <-time.After(retryDelay):
				continue
			case <-c.ctx.Done():
				return fmt.Errorf("连接被取消")
			}
		}
	}

	// 返回原始错误而不是包装错误
	return lastError
}

// calculateSmartReconnectDelay 计算智能重连延迟
func (c *BitgetClient) calculateSmartReconnectDelay(connKey string, reconnectCount int) time.Duration {
	// 基础延迟
	baseDelay := c.getReconnectDelay(reconnectCount)

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(5000)) * time.Millisecond // 0-5秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connKey, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高，延迟更短
	} else if strings.Contains(connKey, "books1") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connKey, "ticker") {
		typeMultiplier = 1.2 // 资金费率数据优先级较低
	}

	// 根据重连次数动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连的连接增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	c.logger.Debugf("🧮 连接 %s 重连延迟计算: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connKey, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}

// updateConnectionQuality 更新连接质量评估
func (c *BitgetClient) updateConnectionQuality(connKey string, connInfo *ConnectionInfo, pongDelay, pingDelay time.Duration) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	// 更新平均pong延迟（使用指数移动平均）
	if connInfo.avgPongDelay == 0 {
		connInfo.avgPongDelay = pongDelay
	} else {
		// 使用0.2的权重进行平滑
		connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*0.8 + float64(pongDelay)*0.2)
	}

	// 检查是否超时
	isTimeout := pongDelay > c.config.PingInterval*4
	if isTimeout {
		connInfo.consecutiveTimeouts++
		c.logger.Warnf("⚠️ 连接 %s 超时 (第%d次): pong延迟=%.1fs",
			connKey, connInfo.consecutiveTimeouts, pongDelay.Seconds())
	} else {
		connInfo.consecutiveTimeouts = 0
	}

	// 计算连接质量评分 (0-1)
	quality := 1.0

	// 基于pong延迟的评分
	idealDelay := c.config.PingInterval
	if pongDelay > idealDelay {
		delayPenalty := float64(pongDelay-idealDelay) / float64(idealDelay*3)
		if delayPenalty > 1.0 {
			delayPenalty = 1.0
		}
		quality -= delayPenalty * 0.4 // 延迟占40%权重
	}

	// 基于连续超时的评分
	if connInfo.consecutiveTimeouts > 0 {
		timeoutPenalty := float64(connInfo.consecutiveTimeouts) / 5.0 // 5次超时为满分惩罚
		if timeoutPenalty > 1.0 {
			timeoutPenalty = 1.0
		}
		quality -= timeoutPenalty * 0.3 // 超时占30%权重
	}

	// 基于消息活跃度的评分
	messageAge := now.Sub(connInfo.lastMessageTime)
	if messageAge > 30*time.Second {
		agePenalty := float64(messageAge-30*time.Second) / float64(60*time.Second)
		if agePenalty > 1.0 {
			agePenalty = 1.0
		}
		quality -= agePenalty * 0.3 // 消息活跃度占30%权重
	}

	// 确保质量评分在合理范围内
	if quality < 0 {
		quality = 0
	}

	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	// 根据质量评分输出不同级别的日志
	if quality < 0.3 {
		// 为trade_batch_7提供更详细的诊断信息
		if connKey == "trade_batch_7" {
			c.logger.Errorf("💔 [Bitget] 连接 %s 质量极差: %.1f%% (延迟=%.1fs, 超时=%d次, 消息=%d, 消息间隔=%.1fs)",
				connKey, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts,
				connInfo.messageCount, messageAge.Seconds())
		} else {
			c.logger.Errorf("💔 连接 %s 质量极差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
				connKey, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
		}
	} else if quality < 0.6 {
		c.logger.Warnf("⚠️ 连接 %s 质量较差: %.1f%% (平均延迟=%.1fs, 连续超时=%d次)",
			connKey, quality*100, connInfo.avgPongDelay.Seconds(), connInfo.consecutiveTimeouts)
	} else if quality < 0.8 {
		c.logger.Infof("📊 连接 %s 质量一般: %.1f%% (平均延迟=%.1fs)",
			connKey, quality*100, connInfo.avgPongDelay.Seconds())
	} else {
		c.logger.Debugf("✅ 连接 %s 质量良好: %.1f%% (平均延迟=%.1fs)",
			connKey, quality*100, connInfo.avgPongDelay.Seconds())
	}
}

// cleanupConnection 清理连接
func (c *BitgetClient) cleanupConnection(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	c.logger.Debugf("🧹 清理连接 %s", connKey)

	if connInfo.conn != nil {
		// 发送关闭消息
		connInfo.conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		connInfo.conn.Close()
		connInfo.conn = nil
	}

	// 停止心跳，使用非阻塞方式
	select {
	case connInfo.heartbeatStop <- struct{}{}:
	default:
	}

	connInfo.state = StateDisconnected
}

// buildSubscribeMessage 构建订阅消息
func (c *BitgetClient) buildSubscribeMessage(symbols []BitgetSymbol, channel string) []byte {
	var args []map[string]string

	for _, symbol := range symbols {
		args = append(args, map[string]string{
			"instType": "USDT-FUTURES",
			"channel":  channel,
			"instId":   symbol.Symbol,
		})
	}

	subscribeMsg := map[string]interface{}{
		"op":   "subscribe",
		"args": args,
	}

	data, _ := json.Marshal(subscribeMsg)
	return data
}

// handleTradeMessage 处理交易消息
func (c *BitgetClient) handleTradeMessage(conn *websocket.Conn, message []byte) {
	// 解析消息
	if gjson.GetBytes(message, "event").String() == "subscribe" {
		return
	}

	// 检查是否为数据消息
	if !gjson.GetBytes(message, "data").Exists() {
		return
	}

	// 从arg中获取instId
	instId := gjson.GetBytes(message, "arg.instId").String()

	// 解析交易数据
	dataArray := gjson.GetBytes(message, "data").Array()
	for _, item := range dataArray {
		var tradeData BitgetTradeData
		if err := json.Unmarshal([]byte(item.Raw), &tradeData); err != nil {
			c.logger.WithError(err).Error("解析交易数据失败")
			continue
		}

		// 设置instId
		tradeData.InstID = instId

		// 处理数据
		c.dataHandler.HandleTradeData(&tradeData)
		atomic.AddInt64(&c.tradeCount, 1)

		// 发送到Kafka
		if c.kafkaProducer != nil {
			c.kafkaProducer.SendTradeData(tradeData.InstID, &tradeData)
		}
	}
}

// handleBooks1Message 处理一档盘口消息
func (c *BitgetClient) handleBooks1Message(conn *websocket.Conn, message []byte) {
	// 解析消息
	if gjson.GetBytes(message, "event").String() == "subscribe" {
		return
	}

	// 检查是否为数据消息
	if !gjson.GetBytes(message, "data").Exists() {
		return
	}

	// 从arg中获取instId
	instId := gjson.GetBytes(message, "arg.instId").String()

	// 解析一档盘口数据
	dataArray := gjson.GetBytes(message, "data").Array()
	for _, item := range dataArray {
		var books1Data BitgetBooks1Data
		if err := json.Unmarshal([]byte(item.Raw), &books1Data); err != nil {
			c.logger.WithError(err).Error("解析一档盘口数据失败")
			continue
		}

		// 设置instId
		books1Data.InstID = instId

		// 处理数据
		c.dataHandler.HandleBooks1Data(&books1Data)
		atomic.AddInt64(&c.tickerCount, 1)

		// 发送到Kafka
		if c.kafkaProducer != nil {
			c.kafkaProducer.SendOrderbookData(books1Data.InstID, &books1Data)
		}
	}
}

// handleTickerMessage 处理ticker消息（包含资金费率）
func (c *BitgetClient) handleTickerMessage(conn *websocket.Conn, message []byte) {
	// 解析消息
	if gjson.GetBytes(message, "event").String() == "subscribe" {
		return
	}

	// 检查是否为数据消息
	if !gjson.GetBytes(message, "data").Exists() {
		return
	}

	// 解析ticker数据
	dataArray := gjson.GetBytes(message, "data").Array()

	for _, item := range dataArray {
		// 提取基本信息
		instId := gjson.Get(item.Raw, "instId").String()
		fundingRate := gjson.Get(item.Raw, "fundingRate").String()
		nextFundingTime := gjson.Get(item.Raw, "nextFundingTime").String()
		timestamp := gjson.Get(item.Raw, "ts").String()

		// 检查是否包含资金费率信息
		if fundingRate != "" && fundingRate != "0" && nextFundingTime != "" {
			fundingData := BitgetFundingData{
				InstID:          instId,
				FundingRate:     fundingRate,
				NextFundingTime: nextFundingTime,
				Timestamp:       timestamp,
			}

			// 处理资金费率数据
			c.dataHandler.HandleFundingData(&fundingData)
			atomic.AddInt64(&c.fundingCount, 1)

			// 发送到Kafka
			if c.kafkaProducer != nil {
				c.kafkaProducer.SendFundingData(fundingData.InstID, &fundingData)
			}
		}
	}
}

// printStatistics 打印统计信息
func (c *BitgetClient) printStatistics() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	startTime := time.Now()

	for {
		select {
		case <-ticker.C:
			runtime := time.Since(startTime)
			tradeCount := atomic.LoadInt64(&c.tradeCount)
			tickerCount := atomic.LoadInt64(&c.tickerCount)
			fundingCount := atomic.LoadInt64(&c.fundingCount)

			c.connectionMutex.RLock()
			activeConnections := 0
			totalConnections := len(c.connections)
			tradeConnections := 0
			books1Connections := 0
			fundingConnections := 0

			for connKey, connInfo := range c.connections {
				connInfo.mutex.RLock()
				if connInfo.state == StateConnected {
					activeConnections++
				}
				if strings.Contains(connKey, "trade") {
					tradeConnections++
				} else if strings.Contains(connKey, "books1") {
					books1Connections++
				} else if strings.Contains(connKey, "ticker") {
					fundingConnections++
				}
				connInfo.mutex.RUnlock()
			}
			c.connectionMutex.RUnlock()

			// 计算预期连接数（假设每批20个交易对）
			_ = (c.config.BatchSize + 19) / 20 * 3 // 交易数据 + 盘口数据 + 资金费率数据

			c.logger.WithFields(logrus.Fields{
				"runtime":             runtime.Round(time.Second),
				"active_connections":  fmt.Sprintf("%d/%d", activeConnections, totalConnections),
				"trade_connections":   tradeConnections,
				"books1_connections":  books1Connections,
				"funding_connections": fundingConnections,
				"trade_messages":      tradeCount,
				"books1_messages":     tickerCount,
				"funding_messages":    fundingCount,
				"total_messages":      tradeCount + tickerCount + fundingCount,
			}).Info("📊 [Bitget] 全局统计")

			// 数据流速率统计（每秒消息数）
			if runtime.Seconds() > 0 {
				tradeRate := float64(tradeCount) / runtime.Seconds()
				books1Rate := float64(tickerCount) / runtime.Seconds()
				fundingRate := float64(fundingCount) / runtime.Seconds()
				c.logger.WithFields(logrus.Fields{
					"trade_rate":   fmt.Sprintf("%.1f msg/s", tradeRate),
					"books1_rate":  fmt.Sprintf("%.1f msg/s", books1Rate),
					"funding_rate": fmt.Sprintf("%.1f msg/s", fundingRate),
				}).Info("📈 [Bitget] 数据流速率")
			}

			// Kafka发送统计
			if c.kafkaProducer != nil {
				kafkaTrades, kafkaOrderbooks, kafkaFunding, kafkaTotal, kafkaErrors := c.kafkaProducer.GetStats()
				c.logger.WithFields(logrus.Fields{
					"trades_sent":     kafkaTrades,
					"orderbooks_sent": kafkaOrderbooks,
					"funding_sent":    kafkaFunding,
					"total_sent":      kafkaTotal,
					"errors":          kafkaErrors,
					"uptime":          runtime.Round(time.Second),
				}).Infof("📊 [Bitget] Kafka发送统计 - 交易: %d条, 盘口: %d条, 资金费率: %d条, 总计: %d条, 错误: %d条, 运行时间: %v",
					kafkaTrades, kafkaOrderbooks, kafkaFunding, kafkaTotal, kafkaErrors, runtime.Round(time.Second))
			} else {
				c.logger.Warn("⚠️ [Bitget] Kafka生产者未初始化，无法发送数据")
			}

			// WebSocket连接健康状态
			if activeConnections == totalConnections && totalConnections > 0 {
				c.logger.Info("✅ [Bitget] 所有WebSocket连接健康")
			} else if activeConnections >= totalConnections*2/3 {
				c.logger.Warn("⚠️ [Bitget] 部分WebSocket连接异常")
			} else {
				c.logger.Error("❌ [Bitget] 大量WebSocket连接异常")
			}

			// 增强ping/pong统计
			if c.pingPongManager != nil {
				pingPongStats := c.pingPongManager.GetGlobalPingPongStats()
				qualityStats := c.pingPongManager.GetConnectionQualityStats()

				c.logger.WithFields(logrus.Fields{
					"total_pings":       pingPongStats["total_pings"],
					"total_pongs":       pingPongStats["total_pongs"],
					"total_timeouts":    pingPongStats["total_timeouts"],
					"total_reconnects":  pingPongStats["total_reconnects"],
					"avg_response_time": pingPongStats["avg_response_time"],
					"max_response_time": pingPongStats["max_response_time"],
					"min_response_time": pingPongStats["min_response_time"],
				}).Info("💓 [Bitget] Ping/Pong统计")

				c.logger.WithFields(logrus.Fields{
					"total_connections": qualityStats["total_connections"],
					"excellent_count":   qualityStats["excellent_count"],
					"good_count":        qualityStats["good_count"],
					"average_count":     qualityStats["average_count"],
					"poor_count":        qualityStats["poor_count"],
					"very_poor_count":   qualityStats["very_poor_count"],
					"average_quality":   fmt.Sprintf("%.1f%%", qualityStats["average_quality"].(float64)*100),
				}).Info("🎯 [Bitget] 连接质量统计")
			}

		case <-c.ctx.Done():
			return
		}
	}
}

// monitorConnections 监控连接状态
func (c *BitgetClient) monitorConnections() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.connectionMutex.RLock()

			var stateCount = make(map[ConnectionState]int)
			var totalReconnects int
			var connNames []string
			var tradeConnNames []string
			var books1ConnNames []string
			var fundingConnNames []string
			var qualityStats = make(map[string]float64) // 连接质量统计

			for connKey, connInfo := range c.connections {
				connInfo.mutex.RLock()
				state := connInfo.state
				reconnects := connInfo.reconnectCount
				lastPing := connInfo.lastPingTime
				lastPong := connInfo.lastPongTime
				quality := connInfo.connectionQuality
				avgDelay := connInfo.avgPongDelay
				timeouts := connInfo.consecutiveTimeouts
				msgCount := connInfo.messageCount
				lastMsg := connInfo.lastMessageTime
				connInfo.mutex.RUnlock()

				stateCount[state]++
				totalReconnects += reconnects
				connNames = append(connNames, connKey)
				qualityStats[connKey] = quality

				if strings.Contains(connKey, "trade") {
					tradeConnNames = append(tradeConnNames, connKey)
				} else if strings.Contains(connKey, "books1") {
					books1ConnNames = append(books1ConnNames, connKey)
				} else if strings.Contains(connKey, "ticker") {
					fundingConnNames = append(fundingConnNames, connKey)
				}

				// 增强的连接健康状态检查
				if state == StateConnected {
					pongDelay := time.Since(lastPong)
					pingDelay := time.Since(lastPing)
					msgAge := time.Since(lastMsg)

					// 连接质量分级报告
					if quality < 0.3 {
						c.logger.Errorf("💔 [Bitget] 连接 %s 质量极差: %.1f%% (延迟=%.1fs, 超时=%d次, 消息=%d, 消息间隔=%.1fs)",
							connKey, quality*100, avgDelay.Seconds(), timeouts, msgCount, msgAge.Seconds())
					} else if quality < 0.6 {
						c.logger.Warnf("⚠️ [Bitget] 连接 %s 质量较差: %.1f%% (延迟=%.1fs, 超时=%d次, 消息=%d)",
							connKey, quality*100, avgDelay.Seconds(), timeouts, msgCount)
					} else if pongDelay > c.config.PingInterval*4 {
						c.logger.Warnf("⚠️ [Bitget] 连接 %s 当前延迟较高: pong=%.1fs, ping=%.1fs (质量=%.1f%%)",
							connKey, pongDelay.Seconds(), pingDelay.Seconds(), quality*100)
					}

					// 检查重连次数，频繁重连的连接可能有问题
					if reconnects > 5 {
						c.logger.Warnf("🔄 [Bitget] 连接 %s 重连频繁: %d次 (质量=%.1f%%)", connKey, reconnects, quality*100)
					}

					// 检查消息活跃度
					if msgAge > 60*time.Second {
						c.logger.Warnf("📵 [Bitget] 连接 %s 消息停滞: %.1fs无新消息 (总消息=%d)",
							connKey, msgAge.Seconds(), msgCount)
					}
				}
			}

			c.connectionMutex.RUnlock()

			// 计算平均连接质量
			var totalQuality float64
			var qualityCount int
			var excellentConns, goodConns, poorConns, badConns int

			for _, quality := range qualityStats {
				if quality > 0 { // 只统计有质量数据的连接
					totalQuality += quality
					qualityCount++

					if quality >= 0.9 {
						excellentConns++
					} else if quality >= 0.7 {
						goodConns++
					} else if quality >= 0.4 {
						poorConns++
					} else {
						badConns++
					}
				}
			}

			avgQuality := 0.0
			if qualityCount > 0 {
				avgQuality = totalQuality / float64(qualityCount)
			}

			// 详细的连接监控信息
			c.logger.WithFields(logrus.Fields{
				"total_connections": len(connNames),
				"batch_size":        c.config.BatchSize,
				"connected":         stateCount[StateConnected],
				"connecting":        stateCount[StateConnecting],
				"reconnecting":      stateCount[StateReconnecting],
				"disconnected":      stateCount[StateDisconnected],
				"stopped":           stateCount[StateStopped],
				"total_reconnects":  totalReconnects,
				"avg_quality":       fmt.Sprintf("%.1f%%", avgQuality*100),
				"excellent_conns":   excellentConns,
				"good_conns":        goodConns,
				"poor_conns":        poorConns,
				"bad_conns":         badConns,
			}).Info("🔍 [Bitget] 连接监控")

			// 连接详情
			c.logger.WithFields(logrus.Fields{
				"trade_connections":  tradeConnNames,
				"books1_connections": books1ConnNames,
				"ticker_connections": fundingConnNames,
			}).Info("🔗 [Bitget] 连接详情")

			// 数据流健康检查
			tradeCount := atomic.LoadInt64(&c.tradeCount)
			tickerCount := atomic.LoadInt64(&c.tickerCount)
			fundingCount := atomic.LoadInt64(&c.fundingCount)
			totalConnections := len(connNames)

			if totalConnections > 0 && tradeCount == 0 && tickerCount == 0 && fundingCount == 0 {
				c.logger.Warn("⚠️ [Bitget] 数据流异常：有连接但没有数据")
			}

			// 连接效率检查
			if totalConnections > 0 {
				avgTradePerConn := float64(tradeCount) / float64(len(tradeConnNames))
				avgBooks1PerConn := float64(tickerCount) / float64(len(books1ConnNames))
				avgFundingPerConn := float64(fundingCount) / float64(len(fundingConnNames))
				c.logger.WithFields(logrus.Fields{
					"avg_trade_per_conn":   fmt.Sprintf("%.1f", avgTradePerConn),
					"avg_books1_per_conn":  fmt.Sprintf("%.1f", avgBooks1PerConn),
					"avg_funding_per_conn": fmt.Sprintf("%.1f", avgFundingPerConn),
				}).Info("📊 [Bitget] 连接效率")
			}

		case <-c.ctx.Done():
			return
		}
	}
}

// 辅助方法
func (c *BitgetClient) setConnectionState(connKey string, state ConnectionState) {
	c.connectionMutex.RLock()
	connInfo, exists := c.connections[connKey]
	c.connectionMutex.RUnlock()

	if exists {
		connInfo.mutex.Lock()
		connInfo.state = state
		connInfo.mutex.Unlock()
	}
}

func (c *BitgetClient) shouldReconnect(connInfo *ConnectionInfo) bool {
	if c.config.ReconnectStrategy == BitgetReconnectStrategyInfinite {
		return true
	}

	connInfo.mutex.RLock()
	count := connInfo.reconnectCount
	connInfo.mutex.RUnlock()

	return count < c.config.MaxReconnectAttempts
}

func (c *BitgetClient) getReconnectDelay(reconnectCount int) time.Duration {
	if c.config.ReconnectStrategy != BitgetReconnectStrategyExponential {
		return c.config.ReconnectDelay
	}

	// 指数退避，最大延迟限制
	delay := c.config.ReconnectDelay * time.Duration(1<<uint(min(reconnectCount, 6)))
	if delay > c.config.MaxReconnectDelay {
		delay = c.config.MaxReconnectDelay
	}

	return delay
}

func (c *BitgetClient) incrementReconnectCount(connKey string) {
	c.connectionMutex.RLock()
	connInfo, exists := c.connections[connKey]
	c.connectionMutex.RUnlock()

	if exists {
		connInfo.mutex.Lock()
		connInfo.reconnectCount++
		connInfo.mutex.Unlock()
	}
}

func (c *BitgetClient) resetReconnectCount(connKey string) {
	c.connectionMutex.RLock()
	connInfo, exists := c.connections[connKey]
	c.connectionMutex.RUnlock()

	if exists {
		connInfo.mutex.Lock()
		connInfo.reconnectCount = 0
		connInfo.mutex.Unlock()
	}
}

// filterSymbols 过滤交易对
func (c *BitgetClient) filterSymbols(symbols []BitgetSymbol) []BitgetSymbol {
	if len(c.config.SymbolFilter) == 0 {
		return symbols
	}

	var filtered []BitgetSymbol
	filterMap := make(map[string]bool)

	for _, filter := range c.config.SymbolFilter {
		filterMap[strings.ToUpper(filter)] = true
	}

	for _, symbol := range symbols {
		if filterMap[strings.ToUpper(symbol.Symbol)] {
			filtered = append(filtered, symbol)
		}
	}

	return filtered
}

// Stop 停止客户端
func (c *BitgetClient) Stop() {
	c.logger.Info("🛑 正在停止Bitget客户端...")

	// 取消上下文
	c.cancel()

	// 关闭所有连接
	c.connectionMutex.Lock()
	for connKey, connInfo := range c.connections {
		c.logger.Infof("🔌 关闭连接: %s", connKey)
		c.cleanupConnection(connKey, connInfo)
	}
	c.connectionMutex.Unlock()

	// 关闭Kafka生产者
	if c.kafkaProducer != nil {
		c.kafkaProducer.Close()
	}

	// 等待所有goroutine结束
	c.wg.Wait()

	c.logger.Info("✅ Bitget客户端已停止")
}

func main() {
	// 加载配置
	config, err := LoadBitgetConfig("bitget_config.json")
	if err != nil {
		panic(fmt.Sprintf("❌ 加载配置失败: %v", err))
	}

	// 创建日志器
	logger := logrus.New()

	// 根据配置设置日志级别
	switch strings.ToLower(config.LogLevel) {
	case "debug":
		logger.SetLevel(logrus.DebugLevel)
	case "info":
		logger.SetLevel(logrus.InfoLevel)
	case "warn", "warning":
		logger.SetLevel(logrus.WarnLevel)
	case "error":
		logger.SetLevel(logrus.ErrorLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	logger.Debugf("🔧 [Bitget] 日志级别设置为: %s", config.LogLevel)

	// 设置日志输出到文件
	logFile, err := os.OpenFile("logs/bitget.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		// 如果无法打开日志文件，创建logs目录
		if err := os.MkdirAll("logs", 0755); err != nil {
			panic(fmt.Sprintf("❌ 创建日志目录失败: %v", err))
		}
		logFile, err = os.OpenFile("logs/bitget.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			panic(fmt.Sprintf("❌ 打开日志文件失败: %v", err))
		}
	}
	logger.SetOutput(logFile)

	// 创建数据处理器
	dataHandler := NewDefaultBitgetDataHandler(logger)

	// 创建Bitget客户端
	client := NewBitgetClient(config, logger, dataHandler)

	logger.Info("🚀 启动Bitget数据订阅程序")
	logger.Infof("📡 WebSocket地址: %s", config.WSBaseURL)
	logger.Infof("🔧 心跳间隔: %v", config.PingInterval)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据订阅
	if err := client.SubscribeData(); err != nil {
		logger.Fatalf("❌ 启动订阅失败: %v", err)
	}

	logger.Info("🎉 所有订阅已启动，等待数据...")
	logger.Info("💡 程序具备以下健壮性保障：")
	logger.Info("   • 数据类型分离：交易数据和盘口数据使用独立连接")
	logger.Info("   • 自动重连机制：连接断开后自动重连")
	logger.Info("   • 心跳检测：定期发送心跳，检测连接状态")
	logger.Info("   • 连接监控：实时监控连接健康状态")
	logger.Info("   • 批次处理：每批20个交易对，减少连接压力")

	// 等待信号
	<-sigChan
	logger.Info("📡 收到停止信号")

	// 停止客户端
	client.Stop()
}
