#!/bin/bash

# Bitget 程序监控脚本
# 监控程序健康状态，自动重启异常程序

PROGRAM_NAME="bitget-subscriber-enhanced"
PID_FILE="bitget-subscriber-enhanced.pid"
LOG_FILE="logs/bitget.log"
MONITOR_LOG_FILE="logs/monitor.log"
RESTART_SCRIPT="./run_stable.sh"

# 创建日志目录
mkdir -p logs

echo "🔍 Bitget 程序监控器启动"
echo "========================"

# 检查程序是否在运行
check_program_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 程序在运行
        fi
    fi
    return 1  # 程序未运行
}

# 检查程序健康状态
check_program_health() {
    if [ ! -f "$LOG_FILE" ]; then
        return 1  # 日志文件不存在
    fi
    
    # 检查最近5分钟是否有统计信息输出
    recent_stats=$(tail -n 100 "$LOG_FILE" | grep "全局统计" | tail -1)
    if [ -z "$recent_stats" ]; then
        return 1  # 没有统计信息
    fi
    
    # 检查最近的时间戳
    last_timestamp=$(tail -n 10 "$LOG_FILE" | grep -o 'time="[^"]*"' | tail -1 | cut -d'"' -f2)
    if [ -n "$last_timestamp" ]; then
        # 转换时间戳并检查是否在最近5分钟内
        last_time=$(date -d "$last_timestamp" +%s 2>/dev/null || echo 0)
        current_time=$(date +%s)
        time_diff=$((current_time - last_time))
        
        if [ $time_diff -gt 300 ]; then  # 5分钟 = 300秒
            return 1  # 程序可能卡住了
        fi
    fi
    
    return 0  # 程序健康
}

# 重启程序
restart_program() {
    log_monitor "🔄 重启程序..."
    
    # 停止现有程序
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        kill "$PID" 2>/dev/null || true
        sleep 3
        kill -9 "$PID" 2>/dev/null || true
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    # 启动新程序
    if [ -x "$RESTART_SCRIPT" ]; then
        $RESTART_SCRIPT >> "$MONITOR_LOG_FILE" 2>&1
        log_monitor "✅ 程序已重启"
    else
        log_monitor "❌ 重启脚本不存在或不可执行"
        return 1
    fi
}

# 记录监控日志
log_monitor() {
    local message="$1"
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $message" | tee -a "$MONITOR_LOG_FILE"
}

# 主监控循环
monitor_loop() {
    log_monitor "🔍 监控器启动"
    log_monitor "💡 监控间隔: 60秒"
    log_monitor "💡 健康检查: 统计信息更新 + 日志时间戳"
    log_monitor "📄 监控日志保存到: $MONITOR_LOG_FILE"
    echo ""
    
    while true; do
        current_time=$(date "+%Y-%m-%d %H:%M:%S")
        
        if ! check_program_running; then
            log_monitor "⚠️  程序未运行，尝试重启..."
            restart_program
        elif ! check_program_health; then
            log_monitor "⚠️  程序健康检查失败，尝试重启..."
            restart_program
        else
            log_monitor "✅ 程序运行正常"
        fi
        
        sleep 60  # 每分钟检查一次
    done
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动监控"
    echo "  check     检查程序状态"
    echo "  restart   重启程序"
    echo "  stop      停止监控和程序"
    echo ""
}

# 主逻辑
case "${1:-start}" in
    "start")
        monitor_loop
        ;;
    "check")
        if check_program_running; then
            if check_program_health; then
                echo "✅ 程序运行正常"
                exit 0
            else
                echo "⚠️  程序运行但健康检查失败"
                exit 1
            fi
        else
            echo "❌ 程序未运行"
            exit 1
        fi
        ;;
    "restart")
        restart_program
        ;;
    "stop")
        echo "🛑 停止监控和程序..."
        pkill -f "monitor.sh" 2>/dev/null || true
        pkill -f "$PROGRAM_NAME" 2>/dev/null || true
        rm -f "$PID_FILE"
        echo "✅ 已停止"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
