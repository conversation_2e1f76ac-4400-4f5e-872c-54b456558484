#!/bin/bash

echo "🔧 临时禁用稳定性管理器的并发限制"
echo "=================================="

# 备份原文件
cp enhanced_stability_manager.go enhanced_stability_manager.go.backup
echo "📁 已备份原文件为 enhanced_stability_manager.go.backup"

# 创建临时修改版本
cat > enhanced_stability_manager_temp.go << 'EOF'
package main

import (
	"context"
	"math"
	"math/rand"
	"net"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// EnhancedStabilityManager 增强稳定性管理器（简化版）
type EnhancedStabilityManager struct {
	client *BitgetClient
	logger *logrus.Logger
	config *BitgetConfig
}

// StabilityInfo 连接稳定性信息
type StabilityInfo struct {
	ConnKey             string
	LastStableTime      time.Time
	StabilityScore      float64
	ReconnectFrequency  float64
	NetworkLatency      time.Duration
	ErrorPattern        []string
	RecoveryStrategy    string
	AdaptiveDelay       time.Duration
}

// NewEnhancedStabilityManager 创建增强稳定性管理器
func NewEnhancedStabilityManager(client *BitgetClient, logger *logrus.Logger, config *BitgetConfig) *EnhancedStabilityManager {
	manager := &EnhancedStabilityManager{
		client: client,
		logger: logger,
		config: config,
	}

	logger.Info("✅ 增强稳定性管理器初始化完成（无并发限制模式）")
	return manager
}

// CreateStabilizedWebSocketDialer 创建稳定化的WebSocket拨号器
func (m *EnhancedStabilityManager) CreateStabilizedWebSocketDialer(connKey string) *websocket.Dialer {
	return &websocket.Dialer{
		HandshakeTimeout:  60 * time.Second,
		EnableCompression: false,
		ReadBufferSize:    16384,
		WriteBufferSize:   16384,
		NetDialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			dialer := &net.Dialer{
				Timeout:   45 * time.Second,
				KeepAlive: 60 * time.Second,
			}
			return dialer.DialContext(ctx, network, addr)
		},
	}
}

// CalculateAdaptiveReconnectDelay 计算自适应重连延迟
func (m *EnhancedStabilityManager) CalculateAdaptiveReconnectDelay(connKey string, failureCount int) time.Duration {
	// 简化的延迟计算
	baseDelay := time.Duration(math.Min(
		float64(3*time.Second)*math.Pow(1.3, float64(failureCount)), 
		float64(2*time.Minute),
	))

	// 连接类型优先级调整
	priorityMultiplier := 1.0
	if strings.Contains(connKey, "trade") {
		priorityMultiplier = 0.5 // 交易数据最高优先级
	} else if strings.Contains(connKey, "books1") {
		priorityMultiplier = 0.7 // 盘口数据次优先级
	}

	// 随机抖动
	jitterPercent := 0.1 + rand.Float64()*0.2
	jitter := time.Duration(float64(baseDelay) * jitterPercent)

	finalDelay := time.Duration(float64(baseDelay)*priorityMultiplier) + jitter

	// 限制延迟范围
	minDelay := 2 * time.Second
	maxDelay := 3 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	m.logger.Infof("🧮 [稳定性] 连接 %s 简化延迟: 基础=%v, 优先级倍数=%.1f, 抖动=%v, 最终=%v",
		connKey, baseDelay, priorityMultiplier, jitter, finalDelay)

	return finalDelay
}

// ShouldAllowReconnect 判断是否允许重连（无限制模式）
func (m *EnhancedStabilityManager) ShouldAllowReconnect(connKey string) bool {
	// 无并发限制，总是允许重连
	m.logger.Debugf("✅ [稳定性] 连接 %s 获得重连许可（无限制模式）", connKey)
	return true
}

// OnReconnectComplete 重连完成回调
func (m *EnhancedStabilityManager) OnReconnectComplete(connKey string, success bool) {
	if success {
		m.logger.Debugf("✅ [稳定性] 连接 %s 重连成功", connKey)
	} else {
		m.logger.Debugf("❌ [稳定性] 连接 %s 重连失败", connKey)
	}
}
EOF

# 替换文件
mv enhanced_stability_manager_temp.go enhanced_stability_manager.go

echo "✅ 已应用简化版稳定性管理器"
echo "🔧 主要变化："
echo "   • 移除并发连接限制"
echo "   • 移除重连节流"
echo "   • 简化延迟计算"
echo "   • 保持优先级调度"
echo ""
echo "🚀 现在重新编译程序..."

# 重新编译
if go build -o bitget-subscriber-enhanced .; then
    echo "✅ 编译成功"
    echo "💡 使用以下命令恢复原版本："
    echo "   cp enhanced_stability_manager.go.backup enhanced_stability_manager.go"
else
    echo "❌ 编译失败，恢复原文件"
    cp enhanced_stability_manager.go.backup enhanced_stability_manager.go
    exit 1
fi
EOF

chmod +x bitget/disable_throttle.sh
