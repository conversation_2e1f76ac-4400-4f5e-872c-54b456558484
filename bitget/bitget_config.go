package main

import (
	"encoding/json"
	"os"
	"time"
)

// BitgetReconnectStrategy 重连策略
type BitgetReconnectStrategy string

const (
	// BitgetReconnectStrategyLimited 有限重连：达到最大次数后停止该连接
	BitgetReconnectStrategyLimited BitgetReconnectStrategy = "limited"
	// BitgetReconnectStrategyInfinite 无限重连：永远尝试重连
	BitgetReconnectStrategyInfinite BitgetReconnectStrategy = "infinite"
	// BitgetReconnectStrategyExponential 指数退避重连：重连间隔逐渐增加
	BitgetReconnectStrategyExponential BitgetReconnectStrategy = "exponential"
)

// BitgetConfig Bitget配置结构
type BitgetConfig struct {
	// API配置
	APIBaseURL string `json:"api_base_url"`
	WSBaseURL  string `json:"ws_base_url"`

	// 连接配置
	BatchSize            int                     `json:"batch_size"`
	MaxReconnectAttempts int                     `json:"max_reconnect_attempts"`
	ReconnectDelay       time.Duration           `json:"-"`
	ReconnectDelayStr    string                  `json:"reconnect_delay"`
	MaxReconnectDelay    time.Duration           `json:"-"`
	MaxReconnectDelayStr string                  `json:"max_reconnect_delay"`
	ReconnectStrategy    BitgetReconnectStrategy `json:"reconnect_strategy"`

	// WebSocket配置
	ConnectionTimeout    time.Duration `json:"-"`
	ConnectionTimeoutStr string        `json:"connection_timeout"`
	ReadTimeout          time.Duration `json:"-"`
	ReadTimeoutStr       string        `json:"read_timeout"`
	WriteTimeout         time.Duration `json:"-"`
	WriteTimeoutStr      string        `json:"write_timeout"`
	PingInterval         time.Duration `json:"-"`
	PingIntervalStr      string        `json:"ping_interval"`
	PongTimeout          time.Duration `json:"-"`
	PongTimeoutStr       string        `json:"pong_timeout"`

	// 订阅配置
	EnableTradeData      bool `json:"enable_trade_data"`
	EnableBookTicker     bool `json:"enable_book_ticker"`
	EnableDepthData      bool `json:"enable_depth_data"`
	EnableDataValidation bool `json:"enable_data_validation"`

	// 日志配置
	LogLevel string `json:"log_level"`

	// 过滤器配置
	SymbolFilter []string `json:"symbol_filter"`

	// Kafka配置
	Kafka KafkaConfig `json:"kafka"`
}

// KafkaConfig Kafka配置结构
type KafkaConfig struct {
	Enabled          bool                `json:"enabled"`
	Brokers          []string            `json:"brokers"`
	SecurityProtocol string              `json:"security_protocol"`
	SASLMechanism    string              `json:"sasl_mechanism"`
	SASLUsername     string              `json:"sasl_username"`
	SASLPassword     string              `json:"sasl_password"`
	Topics           KafkaTopics         `json:"topics"`
	ProducerConfig   KafkaProducerConfig `json:"producer_config"`
}

// KafkaTopics Kafka主题配置
type KafkaTopics struct {
	Trades     string `json:"trades"`
	Orderbooks string `json:"orderbooks"`
	Funding    string `json:"funding"`
}

// KafkaProducerConfig Kafka生产者配置
type KafkaProducerConfig struct {
	Acks         string `json:"acks"`
	Retries      int    `json:"retries"`
	BatchSize    int    `json:"batch_size"`
	LingerMs     int    `json:"linger_ms"`
	BufferMemory int    `json:"buffer_memory"`
}

// DefaultBitgetConfig 默认配置
func DefaultBitgetConfig() *BitgetConfig {
	return &BitgetConfig{
		APIBaseURL:           "https://api.bitget.com",
		WSBaseURL:            "wss://ws.bitget.com/v2/ws/public",
		BatchSize:            50,
		MaxReconnectAttempts: 10,
		ReconnectDelay:       5 * time.Second,
		MaxReconnectDelay:    300 * time.Second,
		ReconnectStrategy:    BitgetReconnectStrategyInfinite,
		ConnectionTimeout:    30 * time.Second,
		ReadTimeout:          300 * time.Second,
		WriteTimeout:         10 * time.Second,
		PingInterval:         30 * time.Second,
		PongTimeout:          10 * time.Second,
		EnableTradeData:      true,
		EnableBookTicker:     true,
		EnableDepthData:      false,
		LogLevel:             "info",
		SymbolFilter:         []string{},
	}
}

// LoadBitgetConfig 加载配置文件
func LoadBitgetConfig(filename string) (*BitgetConfig, error) {
	// 如果文件不存在，创建默认配置文件
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		config := DefaultBitgetConfig()
		if err := SaveBitgetConfig(config, filename); err != nil {
			return nil, err
		}
		return config, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	// 解析配置
	config := DefaultBitgetConfig()
	if err := json.Unmarshal(data, config); err != nil {
		return nil, err
	}

	// 解析时间字符串
	if err := config.parseDurations(); err != nil {
		return nil, err
	}

	return config, nil
}

// SaveBitgetConfig 保存配置文件
func SaveBitgetConfig(config *BitgetConfig, filename string) error {
	// 设置时间字符串
	config.setDurationStrings()

	// 序列化配置
	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(filename, data, 0644)
}

// parseDurations 解析时间字符串
func (c *BitgetConfig) parseDurations() error {
	var err error

	if c.ReconnectDelayStr != "" {
		c.ReconnectDelay, err = time.ParseDuration(c.ReconnectDelayStr)
		if err != nil {
			return err
		}
	}

	if c.MaxReconnectDelayStr != "" {
		c.MaxReconnectDelay, err = time.ParseDuration(c.MaxReconnectDelayStr)
		if err != nil {
			return err
		}
	}

	if c.ConnectionTimeoutStr != "" {
		c.ConnectionTimeout, err = time.ParseDuration(c.ConnectionTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.ReadTimeoutStr != "" {
		c.ReadTimeout, err = time.ParseDuration(c.ReadTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.WriteTimeoutStr != "" {
		c.WriteTimeout, err = time.ParseDuration(c.WriteTimeoutStr)
		if err != nil {
			return err
		}
	}

	if c.PingIntervalStr != "" {
		c.PingInterval, err = time.ParseDuration(c.PingIntervalStr)
		if err != nil {
			return err
		}
	}

	if c.PongTimeoutStr != "" {
		c.PongTimeout, err = time.ParseDuration(c.PongTimeoutStr)
		if err != nil {
			return err
		}
	}

	return nil
}

// setDurationStrings 设置时间字符串
func (c *BitgetConfig) setDurationStrings() {
	c.ReconnectDelayStr = c.ReconnectDelay.String()
	c.MaxReconnectDelayStr = c.MaxReconnectDelay.String()
	c.ConnectionTimeoutStr = c.ConnectionTimeout.String()
	c.ReadTimeoutStr = c.ReadTimeout.String()
	c.WriteTimeoutStr = c.WriteTimeout.String()
	c.PingIntervalStr = c.PingInterval.String()
	c.PongTimeoutStr = c.PongTimeout.String()
}
