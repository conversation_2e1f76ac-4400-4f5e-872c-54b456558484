#!/bin/bash

echo "🔄 快速重启 Bitget 增强版程序"
echo "================================"

# 停止现有程序
echo "🛑 停止现有程序..."
pkill -f "bitget-subscriber-enhanced" 2>/dev/null || true
sleep 2

# 编译程序
echo "🔨 重新编译..."
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 备份日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%H%M%S")
    cp logs/bitget.log logs/bitget_backup_${timestamp}.log
    echo "📁 日志已备份为: logs/bitget_backup_${timestamp}.log"
fi

# 清空当前日志
> logs/bitget.log

echo "🚀 启动程序..."
echo "📊 关键指标监控:"
echo "   • 并发连接限制: 8个 (已优化)"
echo "   • 重连节流时间: 3秒 (已优化)"
echo "   • 批次大小: 50个交易对"
echo ""

# 启动程序
./bitget-subscriber-enhanced &
PID=$!

echo "✅ 程序已启动 (PID: $PID)"
echo "📝 使用 Ctrl+C 停止程序"
echo "📊 实时监控重要日志..."
echo ""

# 监控关键日志
tail -f logs/bitget.log | grep --line-buffered -E "(稳定性|连接|错误|统计|质量|重连)" | while read line; do
    # 高亮显示重要信息
    if echo "$line" | grep -q "错误\|异常\|失败"; then
        echo -e "\033[31m$line\033[0m"  # 红色
    elif echo "$line" | grep -q "成功\|健康\|优秀"; then
        echo -e "\033[32m$line\033[0m"  # 绿色
    elif echo "$line" | grep -q "等待\|节流\|限制"; then
        echo -e "\033[33m$line\033[0m"  # 黄色
    else
        echo "$line"
    fi
done
