# Bitget永续合约数据订阅程序 Makefile

# 变量定义
APP_NAME = bitget-subscriber
BUILD_DIR = build
CONFIG_FILE = bitget_config.json

# Go相关变量
GO = go
GOOS ?= $(shell go env GOOS)
GOARCH ?= $(shell go env GOARCH)

# 版本信息
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME = $(shell date -u '+%Y-%m-%d_%H:%M:%S')
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME)"

# 默认目标
.PHONY: all
all: build

# 安装依赖
.PHONY: deps
deps:
	@echo "📦 安装依赖..."
	$(GO) mod download
	$(GO) mod tidy

# 构建程序
.PHONY: build
build: deps
	@echo "🔨 构建程序..."
	@mkdir -p $(BUILD_DIR)
	$(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) .
	@echo "✅ 构建完成: $(BUILD_DIR)/$(APP_NAME)"

# 交叉编译
.PHONY: build-linux
build-linux: deps
	@echo "🔨 构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux-amd64 .
	@echo "✅ Linux版本构建完成: $(BUILD_DIR)/$(APP_NAME)-linux-amd64"

.PHONY: build-windows
build-windows: deps
	@echo "🔨 构建Windows版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe .
	@echo "✅ Windows版本构建完成: $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe"

.PHONY: build-darwin
build-darwin: deps
	@echo "🔨 构建macOS版本..."
	@mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-amd64 .
	GOOS=darwin GOARCH=arm64 $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-darwin-arm64 .
	@echo "✅ macOS版本构建完成"

.PHONY: build-all
build-all: build-linux build-windows build-darwin
	@echo "✅ 所有平台构建完成"

# 运行程序
.PHONY: run
run: build
	@echo "🚀 启动程序..."
	./$(BUILD_DIR)/$(APP_NAME)

# 开发模式运行
.PHONY: dev
dev:
	@echo "🔧 开发模式运行..."
	$(GO) run .

# 测试
.PHONY: test
test:
	@echo "🧪 运行测试..."
	$(GO) test -v ./...

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	@which golangci-lint > /dev/null || (echo "请安装golangci-lint" && exit 1)
	golangci-lint run

# 格式化代码
.PHONY: fmt
fmt:
	@echo "📝 格式化代码..."
	$(GO) fmt ./...
	@which goimports > /dev/null && goimports -w . || echo "建议安装goimports"

# 清理
.PHONY: clean
clean:
	@echo "🧹 清理构建文件..."
	rm -rf $(BUILD_DIR)
	$(GO) clean

# 安装到系统
.PHONY: install
install: build
	@echo "📥 安装到系统..."
	cp $(BUILD_DIR)/$(APP_NAME) /usr/local/bin/
	@echo "✅ 安装完成"

# 创建发布包
.PHONY: package
package: build-all
	@echo "📦 创建发布包..."
	@mkdir -p $(BUILD_DIR)/release
	@for binary in $(BUILD_DIR)/$(APP_NAME)-*; do \
		if [ -f "$$binary" ]; then \
			basename=$$(basename $$binary); \
			platform=$$(echo $$basename | sed 's/$(APP_NAME)-//'); \
			mkdir -p $(BUILD_DIR)/release/$$platform; \
			cp $$binary $(BUILD_DIR)/release/$$platform/; \
			cp $(CONFIG_FILE) $(BUILD_DIR)/release/$$platform/ 2>/dev/null || true; \
			cp README.md $(BUILD_DIR)/release/$$platform/ 2>/dev/null || true; \
			cd $(BUILD_DIR)/release && tar -czf $$platform.tar.gz $$platform/; \
			cd ../..; \
		fi \
	done
	@echo "✅ 发布包创建完成: $(BUILD_DIR)/release/"

# 显示帮助
.PHONY: help
help:
	@echo "Bitget永续合约数据订阅程序 - 可用命令:"
	@echo ""
	@echo "  deps         - 安装依赖"
	@echo "  build        - 构建程序"
	@echo "  build-linux  - 构建Linux版本"
	@echo "  build-windows- 构建Windows版本"
	@echo "  build-darwin - 构建macOS版本"
	@echo "  build-all    - 构建所有平台版本"
	@echo "  run          - 构建并运行程序"
	@echo "  dev          - 开发模式运行"
	@echo "  test         - 运行测试"
	@echo "  lint         - 代码检查"
	@echo "  fmt          - 格式化代码"
	@echo "  clean        - 清理构建文件"
	@echo "  install      - 安装到系统"
	@echo "  package      - 创建发布包"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make build   # 构建程序"
	@echo "  make run     # 运行程序"
	@echo "  make dev     # 开发模式" 