#!/bin/bash

# Bitget 稳定版启动脚本
# 专注于稳定性，减少复杂功能

echo "🚀 启动 Bitget 稳定版程序"
echo "=========================="

# 停止现有进程
echo "🛑 停止现有进程..."
pkill -f "bitget-subscriber-enhanced" 2>/dev/null || true
sleep 3

# 强制停止残留进程
if pgrep -f "bitget-subscriber-enhanced" > /dev/null; then
    echo "⚠️  强制停止残留进程..."
    pkill -9 -f "bitget-subscriber-enhanced" 2>/dev/null || true
    sleep 2
fi

# 编译程序
echo "🔨 编译程序..."
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 创建日志目录
mkdir -p logs

# 备份旧日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv logs/bitget.log logs/bitget_stable_${timestamp}.log
    echo "📁 旧日志已备份"
fi

# 显示稳定性配置
echo ""
echo "🔧 稳定性配置:"
echo "   • 批次大小: 30 (减少负载)"
echo "   • 重连延迟: 10秒 (更保守)"
echo "   • 连接超时: 60秒 (更宽松)"
echo "   • 心跳间隔: 60秒 (减少频率)"
echo "   • 读取超时: 10分钟 (更宽松)"
echo "   • 日志级别: debug (详细调试)"
echo ""

# 启动程序
echo "🚀 启动程序..."
./bitget-subscriber-enhanced &
PID=$!

# 保存PID
echo $PID > bitget-subscriber-enhanced.pid

echo "✅ 程序已启动 (PID: $PID)"
echo ""
echo "💡 监控命令:"
echo "   查看实时日志: tail -f logs/bitget.log"
echo "   查看错误日志: tail -f logs/bitget.log | grep -E 'ERROR|ERRO|panic|异常'"
echo "   查看统计信息: tail -f logs/bitget.log | grep '全局统计'"
echo "   查看内存使用: tail -f logs/bitget.log | grep '内存使用'"
echo ""
echo "💡 控制命令:"
echo "   停止程序: kill $PID"
echo "   重启程序: ./run_stable.sh"
echo ""
echo "🎯 程序已启动，专注于稳定运行"
