#!/bin/bash

# Bitget永续合约数据订阅程序启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 程序配置
APP_NAME="bitget-subscriber"
BUILD_DIR="."
CONFIG_FILE="bitget_config.json"
LOG_DIR="logs"
PID_FILE="$APP_NAME.pid"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查程序是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 函数：构建程序
build_app() {
    print_message $BLUE "🔨 构建程序..."
    
    if ! command -v go &> /dev/null; then
        print_message $RED "❌ Go未安装，请先安装Go"
        exit 1
    fi
    
    # 安装依赖
    print_message $YELLOW "📦 安装依赖..."
    go mod tidy
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    
    # 构建程序
    go build -o "$BUILD_DIR/$APP_NAME" .
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 构建成功"
    else
        print_message $RED "❌ 构建失败"
        exit 1
    fi
}

# 函数：启动程序
start_app() {
    if is_running; then
        print_message $YELLOW "⚠️  程序已在运行中 (PID: $(cat $PID_FILE))"
        return 0
    fi
    
    # 检查可执行文件
    if [ ! -f "$BUILD_DIR/$APP_NAME" ]; then
        print_message $YELLOW "📦 可执行文件不存在，开始构建..."
        build_app
    fi
    
    # 创建日志目录
    mkdir -p "$LOG_DIR"
    
    # 启动程序
    print_message $BLUE "🚀 启动程序..."
    
    # 后台运行程序
    nohup "./$BUILD_DIR/$APP_NAME" > "$LOG_DIR/$APP_NAME.log" 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    
    # 等待一下确保程序启动
    sleep 2
    
    if is_running; then
        print_message $GREEN "✅ 程序启动成功 (PID: $pid)"
        print_message $BLUE "📋 日志文件: $LOG_DIR/$APP_NAME.log"
        print_message $BLUE "📋 配置文件: $CONFIG_FILE"
    else
        print_message $RED "❌ 程序启动失败"
        if [ -f "$LOG_DIR/$APP_NAME.log" ]; then
            print_message $YELLOW "📋 错误日志:"
            tail -10 "$LOG_DIR/$APP_NAME.log"
        fi
        exit 1
    fi
}

# 函数：停止程序
stop_app() {
    local stopped=false
    
    # 方法1：通过PID文件停止
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            print_message $BLUE "🛑 通过PID文件停止程序 (PID: $pid)..."
            
            # 发送SIGTERM信号
            if kill -TERM "$pid" 2>/dev/null; then
                # 等待程序优雅停止
                local count=0
                while [ $count -lt 10 ]; do
                    if ! ps -p "$pid" > /dev/null 2>&1; then
                        stopped=true
                        break
                    fi
                    sleep 1
                    count=$((count + 1))
                done
                
                # 如果程序仍在运行，强制杀死
                if [ "$stopped" = false ] && ps -p "$pid" > /dev/null 2>&1; then
                    print_message $YELLOW "⚠️  强制停止程序..."
                    kill -KILL "$pid" 2>/dev/null && stopped=true
                fi
            fi
        fi
        rm -f "$PID_FILE"
    fi
    
    # 方法2：如果PID文件方法失败，按进程名查找并停止
    if [ "$stopped" = false ]; then
        print_message $BLUE "🔍 按进程名查找并停止程序..."
        local pids=$(pgrep -f "$APP_NAME" 2>/dev/null)
        if [ -n "$pids" ]; then
            for pid in $pids; do
                print_message $BLUE "🛑 停止进程 (PID: $pid)..."
                if kill -TERM "$pid" 2>/dev/null; then
                    sleep 2
                    if ps -p "$pid" > /dev/null 2>&1; then
                        kill -KILL "$pid" 2>/dev/null
                    fi
                    stopped=true
                fi
            done
        fi
    fi
    
    # 方法3：最后尝试killall
    if [ "$stopped" = false ]; then
        print_message $BLUE "🔍 尝试killall停止程序..."
        if command -v killall &> /dev/null; then
            killall "$APP_NAME" 2>/dev/null && stopped=true
        fi
    fi
    
    if [ "$stopped" = true ]; then
        print_message $GREEN "✅ 程序已停止"
    else
        print_message $YELLOW "⚠️  程序未运行或停止失败"
    fi
}

# 函数：重启程序
restart_app() {
    print_message $BLUE "🔄 重启程序..."
    stop_app
    sleep 2
    start_app
}

# 函数：查看状态
status_app() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_message $GREEN "✅ 程序正在运行 (PID: $pid)"
        
        # 显示进程信息
        if command -v ps &> /dev/null; then
            print_message $BLUE "📊 进程信息:"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        fi
        
        # 显示最近日志
        if [ -f "$LOG_DIR/$APP_NAME.log" ]; then
            print_message $BLUE "📋 最近日志 (最后10行):"
            tail -10 "$LOG_DIR/$APP_NAME.log"
        fi
    else
        print_message $RED "❌ 程序未运行"
    fi
}

# 函数：查看日志
logs_app() {
    if [ -f "$LOG_DIR/$APP_NAME.log" ]; then
        if [ "$1" = "-f" ]; then
            print_message $BLUE "📋 实时日志 (按Ctrl+C退出):"
            tail -f "$LOG_DIR/$APP_NAME.log"
        else
            print_message $BLUE "📋 程序日志:"
            cat "$LOG_DIR/$APP_NAME.log"
        fi
    else
        print_message $YELLOW "⚠️  日志文件不存在: $LOG_DIR/$APP_NAME.log"
    fi
}

# 函数：显示帮助
show_help() {
    echo "Bitget永续合约数据订阅程序启动脚本"
    echo ""
    echo "用法: $0 {start|stop|restart|status|build|logs|help}"
    echo ""
    echo "命令:"
    echo "  start   - 启动程序"
    echo "  stop    - 停止程序"
    echo "  restart - 重启程序"
    echo "  status  - 查看程序状态"
    echo "  build   - 构建程序"
    echo "  logs    - 查看程序日志"
    echo "  logs -f - 实时查看程序日志"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "文件:"
    echo "  配置文件: $CONFIG_FILE"
    echo "  日志目录: $LOG_DIR/"
    echo "  PID文件:  $PID_FILE"
    echo ""
}

# 主逻辑
case "$1" in
    start)
        start_app
        ;;
    stop)
        stop_app
        ;;
    restart)
        restart_app
        ;;
    status)
        status_app
        ;;
    build)
        build_app
        ;;
    logs)
        logs_app "$2"
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_message $RED "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

exit 0 