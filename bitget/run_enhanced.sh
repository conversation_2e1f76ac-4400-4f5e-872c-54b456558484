#!/bin/bash

# Bitget Enhanced Subscriber 编译和启动脚本
# 功能：停止旧进程、编译、启动新进程

set -e  # 遇到错误立即退出

echo "🚀 Bitget Enhanced Subscriber 启动脚本"
echo "======================================"

# 1. 停止现有进程
echo "🛑 停止现有进程..."
pkill -f "bitget-subscriber-enhanced" 2>/dev/null || true
sleep 2

# 再次检查并强制停止
if pgrep -f "bitget-subscriber-enhanced" > /dev/null; then
    echo "⚠️  强制停止残留进程..."
    pkill -9 -f "bitget-subscriber-enhanced" 2>/dev/null || true
    sleep 1
fi

# 2. 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    exit 1
fi

# 3. 更新依赖
echo "📦 更新Go模块依赖..."
go mod tidy

# 4. 编译程序
echo "🔨 编译 bitget-subscriber-enhanced..."
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 5. 创建日志目录
mkdir -p logs

# 6. 备份旧日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv logs/bitget.log logs/bitget_${timestamp}.log
    echo "📁 旧日志已备份为: logs/bitget_${timestamp}.log"
fi

# 7. 显示配置信息
echo ""
echo "⚙️  当前配置:"
if [ -f "bitget_config.json" ]; then
    echo "   • 批次大小: $(grep -o '"batch_size": [0-9]*' bitget_config.json | grep -o '[0-9]*' || echo 'N/A')"
    echo "   • 重连策略: $(grep -o '"reconnect_strategy": "[^"]*"' bitget_config.json | cut -d'"' -f4 || echo 'N/A')"
    echo "   • 重连延迟: $(grep -o '"reconnect_delay": "[^"]*"' bitget_config.json | cut -d'"' -f4 || echo 'N/A')"
    echo "   • Ping间隔: $(grep -o '"ping_interval": "[^"]*"' bitget_config.json | cut -d'"' -f4 || echo 'N/A')"
else
    echo "   ⚠️  配置文件 bitget_config.json 不存在"
fi

echo ""
echo "🔧 增强功能状态:"
echo "   ✅ 无并发连接限制"
echo "   ✅ 智能错误处理"
echo "   ✅ 自适应重连延迟"
echo "   ✅ 连接优先级调度"

# 8. 启动程序
echo ""
echo "🚀 启动 bitget-subscriber-enhanced..."
./bitget-subscriber-enhanced &
PID=$!

# 保存PID到文件
echo $PID > bitget-subscriber-enhanced.pid

echo "✅ 程序已启动 (PID: $PID)"
echo "📄 PID已保存到: bitget-subscriber-enhanced.pid"
echo ""

# 9. 显示控制命令
echo "💡 控制命令:"
echo "   查看日志: tail -f logs/bitget.log"
echo "   停止程序: kill $PID 或 pkill -f bitget-subscriber-enhanced"
echo "   重启程序: ./run_enhanced.sh"
echo ""

# 10. 监控启动状态
echo "📊 监控启动状态 (前15秒)..."
timeout 15 tail -f logs/bitget.log 2>/dev/null | while read line; do
    if echo "$line" | grep -q "启动完成\|连接建立\|订阅成功"; then
        echo "✅ $line"
    elif echo "$line" | grep -q "错误\|失败\|异常"; then
        echo "❌ $line"
    elif echo "$line" | grep -q "全局统计"; then
        echo "📊 $line"
        break  # 看到统计信息说明启动成功
    fi
done 2>/dev/null || true

echo ""
echo "🎯 启动完成！程序正在后台运行"
echo "📈 使用 'tail -f logs/bitget.log' 查看实时日志"
