#!/bin/bash

echo "🔄 强制重启 Bitget 程序（无并发限制版本）"
echo "============================================"

# 强制停止所有相关进程
echo "🛑 强制停止所有相关进程..."
pkill -9 -f "bitget" 2>/dev/null || true
pkill -9 -f "enhanced" 2>/dev/null || true
sleep 5

# 检查是否还有进程在运行
if pgrep -f "bitget" > /dev/null; then
    echo "⚠️ 仍有进程在运行，再次尝试停止..."
    pkill -9 -f "bitget" 2>/dev/null || true
    sleep 3
fi

# 清理旧的可执行文件
echo "🧹 清理旧文件..."
rm -f bitget-subscriber-enhanced bitget-subscriber

# 检查当前稳定性管理器版本
echo "🔍 检查稳定性管理器版本..."
if grep -q "ShouldAllowReconnect.*bool.*{" enhanced_stability_manager.go; then
    if grep -q "return true" enhanced_stability_manager.go; then
        echo "✅ 使用无限制版本的稳定性管理器"
    else
        echo "⚠️ 使用限制版本的稳定性管理器"
    fi
else
    echo "❓ 无法确定稳定性管理器版本"
fi

# 重新编译
echo "🔨 重新编译程序..."
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 备份日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%H%M%S")
    cp logs/bitget.log logs/bitget_force_restart_${timestamp}.log
    echo "📁 日志已备份为: logs/bitget_force_restart_${timestamp}.log"
fi

# 清空日志
> logs/bitget.log

echo ""
echo "🚀 启动新版本程序..."
echo "📊 预期效果："
echo "   ✅ 不再有并发限制警告"
echo "   ✅ 所有30个连接都能建立"
echo "   ✅ active_connections 接近 30/30"
echo ""

# 启动程序
./bitget-subscriber-enhanced &
PID=$!

echo "✅ 程序已启动 (PID: $PID)"
echo "📝 监控前30秒的关键日志..."
echo ""

# 监控前30秒的日志
timeout 30 tail -f logs/bitget.log | while read line; do
    current_time=$(date +"%H:%M:%S")
    
    # 检查是否还有并发限制警告
    if echo "$line" | grep -q "等待.*活跃重连数"; then
        echo -e "[$current_time] \033[31m❌ 仍有并发限制: $line\033[0m"
    elif echo "$line" | grep -q "获得重连许可.*无限制模式"; then
        echo -e "[$current_time] \033[32m✅ 无限制模式: $line\033[0m"
    elif echo "$line" | grep -q "连接.*建立成功"; then
        echo -e "[$current_time] \033[32m✅ 连接成功: $line\033[0m"
    elif echo "$line" | grep -q "全局统计.*active_connections"; then
        echo -e "[$current_time] \033[36m📊 统计: $line\033[0m"
    elif echo "$line" | grep -q "错误\|异常\|失败"; then
        echo -e "[$current_time] \033[31m❌ 错误: $line\033[0m"
    elif echo "$line" | grep -q "稳定性.*简化延迟"; then
        echo -e "[$current_time] \033[35m🧮 延迟: $line\033[0m"
    fi
done

echo ""
echo "📊 30秒监控完成，程序继续在后台运行"
echo "💡 使用 'tail -f logs/bitget.log' 查看完整日志"
echo "🛑 使用 'pkill -f bitget' 停止程序"
