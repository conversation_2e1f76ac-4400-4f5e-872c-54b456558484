#!/bin/bash

# Bitget守护进程安装脚本 - 支持任意路径部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SERVICE_NAME="bitget-data"
SERVICE_TEMPLATE="bitget-data.service"
APP_NAME="bitget-subscriber"
CURRENT_DIR=$(pwd)
SYSTEMD_PATH="/etc/systemd/system"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_message $RED "❌ 此操作需要root权限，请使用sudo运行"
        exit 1
    fi
}

# 函数：生成服务文件
generate_service_file() {
    local working_dir="$1"
    local service_file="$2"
    
    cat > "$service_file" << EOF
[Unit]
Description=Bitget Cryptocurrency Data Subscription Service
Documentation=https://github.com/yourusername/exchange_data
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=$working_dir
ExecStart=$working_dir/bitget-subscriber
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30s

# 守护进程核心配置
Restart=always
RestartSec=10s
StartLimitInterval=60s
StartLimitBurst=3

# 日志配置
StandardOutput=append:$working_dir/logs/bitget-subscriber.log
StandardError=append:$working_dir/logs/bitget-subscriber-error.log
SyslogIdentifier=bitget-subscriber

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=$working_dir/logs

# 环境配置
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=GO111MODULE=on

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

[Install]
WantedBy=multi-user.target
EOF
}

# 函数：安装systemd服务
install_service() {
    check_root
    
    print_message $BLUE "🔧 安装systemd服务..."
    
    # 获取当前绝对路径
    local working_dir=$(realpath "$CURRENT_DIR")
    print_message $BLUE "📁 工作目录: $working_dir"
    
    # 检查可执行文件是否存在
    if [ ! -f "$working_dir/$APP_NAME" ]; then
        print_message $YELLOW "📦 可执行文件不存在，开始构建..."
        if [ -f "./start.sh" ]; then
            ./start.sh build
        else
            print_message $RED "❌ 启动脚本不存在，请手动构建程序"
            exit 1
        fi
    fi
    
    # 创建日志目录
    mkdir -p "$working_dir/logs"
    
    # 生成服务文件
    local temp_service="/tmp/${SERVICE_NAME}.service"
    generate_service_file "$working_dir" "$temp_service"
    
    # 复制服务文件到systemd目录
    cp "$temp_service" "$SYSTEMD_PATH/${SERVICE_NAME}.service"
    rm -f "$temp_service"
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    print_message $GREEN "✅ systemd服务安装成功"
    print_message $BLUE "📋 服务文件: $SYSTEMD_PATH/${SERVICE_NAME}.service"
    print_message $BLUE "📁 工作目录: $working_dir"
}

# 函数：卸载systemd服务
uninstall_service() {
    check_root
    
    print_message $BLUE "🗑️  卸载systemd服务..."
    
    # 停止服务
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $BLUE "🛑 停止服务..."
        systemctl stop "$SERVICE_NAME"
    fi
    
    # 禁用服务
    if systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $BLUE "🔧 禁用服务..."
        systemctl disable "$SERVICE_NAME"
    fi
    
    # 删除服务文件
    if [ -f "$SYSTEMD_PATH/${SERVICE_NAME}.service" ]; then
        rm -f "$SYSTEMD_PATH/${SERVICE_NAME}.service"
        print_message $BLUE "🗑️  删除服务文件..."
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    
    print_message $GREEN "✅ systemd服务卸载成功"
}

# 函数：启动服务
start_service() {
    check_root
    
    if ! systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $YELLOW "⚠️  服务未安装，正在安装..."
        install_service
    fi
    
    print_message $BLUE "🚀 启动守护进程服务..."
    systemctl start "$SERVICE_NAME"
    
    sleep 2
    
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $GREEN "✅ 守护进程服务启动成功"
        show_status
    else
        print_message $RED "❌ 守护进程服务启动失败"
        systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 函数：停止服务
stop_service() {
    check_root
    
    print_message $BLUE "🛑 停止守护进程服务..."
    
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        systemctl stop "$SERVICE_NAME"
        print_message $GREEN "✅ 守护进程服务已停止"
    else
        print_message $YELLOW "⚠️  守护进程服务未运行"
    fi
}

# 函数：重启服务
restart_service() {
    check_root
    
    print_message $BLUE "🔄 重启守护进程服务..."
    systemctl restart "$SERVICE_NAME"
    
    sleep 2
    
    if systemctl is-active "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $GREEN "✅ 守护进程服务重启成功"
        show_status
    else
        print_message $RED "❌ 守护进程服务重启失败"
        systemctl status "$SERVICE_NAME" --no-pager -l
        exit 1
    fi
}

# 函数：查看服务状态
show_status() {
    print_message $BLUE "📊 守护进程服务状态:"
    systemctl status "$SERVICE_NAME" --no-pager -l
    
    print_message $BLUE "\n📋 服务配置信息:"
    if systemctl is-enabled "$SERVICE_NAME" >/dev/null 2>&1; then
        print_message $GREEN "  ✅ 开机自启: 已启用"
    else
        print_message $YELLOW "  ⚠️  开机自启: 未启用"
    fi
    
    local working_dir=$(systemctl show "$SERVICE_NAME" --property=WorkingDirectory --value 2>/dev/null || echo "N/A")
    print_message $BLUE "  📁 工作目录: $working_dir"
}

# 函数：查看服务日志
show_logs() {
    print_message $BLUE "📋 守护进程服务日志:"
    
    if [ "$1" = "-f" ]; then
        print_message $BLUE "📋 实时日志 (按Ctrl+C退出):"
        journalctl -u "$SERVICE_NAME" -f --no-pager
    else
        journalctl -u "$SERVICE_NAME" --no-pager -l | tail -50
    fi
}

# 函数：检查环境
check_environment() {
    print_message $BLUE "🔍 检查部署环境..."
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        print_message $RED "❌ systemd未安装，此脚本仅支持systemd系统"
        exit 1
    fi
    
    # 检查Go程序
    if [ ! -f "$APP_NAME" ]; then
        print_message $YELLOW "⚠️  可执行文件不存在: $APP_NAME"
        if [ -f "start.sh" ]; then
            print_message $BLUE "💡 提示: 可运行 ./start.sh build 构建程序"
        fi
    else
        print_message $GREEN "✅ 可执行文件存在: $APP_NAME"
    fi
    
    # 检查配置文件
    if [ -f "bitget_config.json" ]; then
        print_message $GREEN "✅ 配置文件存在: bitget_config.json"
    else
        print_message $YELLOW "⚠️  配置文件不存在: bitget_config.json"
    fi
    
    # 检查日志目录
    if [ -d "logs" ]; then
        print_message $GREEN "✅ 日志目录存在: logs/"
    else
        print_message $BLUE "📁 将创建日志目录: logs/"
    fi
    
    print_message $BLUE "📁 当前工作目录: $(pwd)"
    print_message $BLUE "📁 绝对路径: $(realpath .)"
}

# 函数：显示帮助
show_help() {
    echo "Bitget守护进程安装脚本 - 支持任意路径部署"
    echo ""
    echo "用法: $0 {install|uninstall|start|stop|restart|status|logs|check|help}"
    echo ""
    echo "命令:"
    echo "  install   - 安装systemd服务 (自动检测当前路径)"
    echo "  uninstall - 卸载systemd服务"
    echo "  start     - 启动守护进程服务"
    echo "  stop      - 停止守护进程服务"
    echo "  restart   - 重启守护进程服务"
    echo "  status    - 查看服务状态"
    echo "  logs      - 查看服务日志"
    echo "  logs -f   - 实时查看服务日志"
    echo "  check     - 检查部署环境"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "特性:"
    echo "  ✅ 路径自适应: 自动检测当前目录并生成服务文件"
    echo "  ✅ 自动重启: 程序崩溃后自动重启"
    echo "  ✅ 开机自启: 系统重启后自动启动"
    echo "  ✅ 资源限制: 防止程序消耗过多资源"
    echo "  ✅ 日志管理: systemd日志 + 应用日志"
    echo "  ✅ 进程监控: systemd监控进程状态"
    echo ""
    echo "使用示例:"
    echo "  cd /path/to/bitget"
    echo "  sudo ./install-daemon.sh install    # 安装守护进程"
    echo "  sudo ./install-daemon.sh start      # 启动服务"
    echo "  sudo ./install-daemon.sh status     # 查看状态"
    echo ""
}

# 主逻辑
case "$1" in
    install)
        install_service
        ;;
    uninstall)
        uninstall_service
        ;;
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    check)
        check_environment
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        if [ -z "$1" ]; then
            show_help
        else
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
        fi
        ;;
esac

exit 0 