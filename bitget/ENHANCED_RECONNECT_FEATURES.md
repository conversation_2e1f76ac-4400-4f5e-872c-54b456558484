# Bitget WebSocket 增强重连策略实现

## 🚀 新增功能概述

本次更新实现了两大核心增强策略，解决了长时间连接不稳定导致程序自动停止的问题：

### 1. 增强重连策略
- **无限重连**: 程序永远不会因为重连次数达到上限而停止
- **智能退避**: 基于连接健康评分的动态延迟调整
- **优先级重连**: 交易数据优先级最高，获得更多重试机会
- **健康评分系统**: 实时评估连接质量，调整重连策略

### 2. 连接分散策略
- **并发控制**: 最多同时连接3个，避免连接风暴
- **时间分散**: 每2秒分发一批连接，减少服务器压力
- **随机抖动**: 20%-50%的随机延迟，避免同步重连
- **分组管理**: 按数据类型分组管理连接

## 📊 核心组件

### EnhancedReconnectManager
增强重连管理器，负责：
- 连接健康状态监控
- 智能重连延迟计算
- 连接分散控制
- 全局重连统计

### ConnectionInfo 增强字段
```go
// 增强重连策略字段
lastReconnectTime     time.Time     // 上次重连时间
consecutiveFailures   int           // 连续失败次数
totalConnectAttempts  int           // 总连接尝试次数
healthScore          float64       // 健康评分 (0-1)
stabilityScore       float64       // 稳定性评分 (0-1)
reconnectBackoffBase  time.Duration // 重连退避基础时间
lastSuccessTime      time.Time     // 上次成功时间
connectionLifetime   time.Duration // 连接生存时间

// 连接分散策略字段
connectionGroup      string        // 连接组标识
priorityLevel        int           // 优先级 (1-5, 1最高)
disperseDelay        time.Duration // 分散延迟
maxBurstConnections  int           // 最大突发连接数
```

## 🎯 优先级系统

### 连接优先级分级
1. **优先级1 (trade)**: 交易数据 - 最高优先级
   - 重试次数: 基础次数 × 3
   - 延迟倍数: 0.3
   - 重连间隔: 最短

2. **优先级2 (books1)**: 盘口数据 - 次优先级
   - 重试次数: 基础次数 × 2
   - 延迟倍数: 0.6
   - 重连间隔: 中等

3. **优先级3 (ticker)**: 资金费率数据 - 较低优先级
   - 重试次数: 基础次数 × 1
   - 延迟倍数: 1.2
   - 重连间隔: 较长

## 🔄 智能重连算法

### 延迟计算公式
```
最终延迟 = (基础延迟 × 健康倍数 × 优先级倍数) + 分散延迟 + 随机抖动
```

### 健康评分影响
- **健康评分 > 0.8**: 延迟倍数 0.5 (健康连接快速重连)
- **健康评分 0.6-0.8**: 延迟倍数 1.0 (正常重连)
- **健康评分 0.3-0.6**: 延迟倍数 2.0 (问题连接延长间隔)
- **健康评分 < 0.3**: 延迟倍数 3.0 (不健康连接大幅延长)

## 🚦 连接分散控制

### 并发限制
- **最大并发连接**: 3个
- **分散间隔**: 2秒
- **突发允许**: 2个连接

### 分散策略
1. 检查当前活跃连接数
2. 如果超过限制，等待最早连接完成
3. 应用分散间隔延迟
4. 添加随机抖动避免同步

## 📈 健康评分系统

### 评分维度
1. **连接成功率** (权重: 基础评分)
2. **最近失败频率** (权重: -0.1 × 失败次数)
3. **连接时间稳定性** (基于时间变化)

### 评分更新
- **成功连接**: 提升成功率，记录连接时间
- **失败连接**: 降低成功率，记录失败时间
- **自动清理**: 只保留最近1小时的失败记录

## 🛡️ 故障恢复机制

### 连接风暴防护
- 限制同时重连数量
- 强制分散重连时间
- 基于健康评分调整策略

### 永不停止保障
- 无限重连策略 (`reconnect_strategy: "infinite"`)
- 智能延迟避免资源浪费
- 优先级确保重要数据优先恢复

## 📊 监控和日志

### 增强日志信息
```
🔄 连接 trade_batch_1 开始第 5 次连接尝试...
🧮 连接 trade_batch_1 重连延迟计算: 基础=6s, 健康倍数=2.0, 优先级倍数=0.3, 分散延迟=2s, 抖动=1.2s, 最终=5.8s
📊 连接 trade_batch_1 健康更新: 成功=false, 健康评分=0.45, 稳定性=0.67, 成功率=0.60, 最近失败=3
```

### 统计信息
- 总重连次数
- 成功/失败重连比例
- 平均重连时间
- 连接健康分布

## 🔧 配置建议

### 推荐配置
```json
{
  "max_reconnect_attempts": 15,
  "reconnect_delay": "3s",
  "max_reconnect_delay": "10m",
  "reconnect_strategy": "infinite",
  "ping_interval": "20s",
  "batch_size": 75
}
```

### 关键参数说明
- `reconnect_strategy: "infinite"`: 启用无限重连
- `max_reconnect_delay: "10m"`: 最大延迟10分钟
- `batch_size: 75`: 每批75个交易对，平衡性能和稳定性

## 🎉 预期效果

1. **程序永不停止**: 无论网络多么不稳定，程序都会持续尝试重连
2. **智能资源使用**: 根据连接健康状况调整重连频率，避免浪费资源
3. **优先级保障**: 重要数据（交易数据）优先恢复
4. **网络友好**: 分散重连避免对服务器造成冲击
5. **自适应调整**: 系统会学习连接模式，自动优化重连策略

## 🚨 注意事项

1. 程序现在会无限期运行，需要手动停止 (Ctrl+C)
2. 在网络极度不稳定时，可能会有较长的重连延迟
3. 建议监控日志中的健康评分，了解连接质量趋势
4. 如需调整策略，可修改 `EnhancedReconnectManager` 中的参数

---

**实施时间**: 2025年1月
**版本**: Enhanced v2.0
**状态**: ✅ 已实现并测试 