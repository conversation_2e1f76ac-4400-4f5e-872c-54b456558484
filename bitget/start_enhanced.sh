#!/bin/bash

# Bitget 增强重连策略启动脚本
# 作者: AI Assistant
# 日期: 2025-01-10

echo "🚀 启动 Bitget 增强重连策略程序"
echo "=================================="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
go mod tidy

# 创建日志目录
mkdir -p logs

# 备份旧日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv logs/bitget.log logs/bitget_${timestamp}.log
    echo "📁 旧日志已备份为: logs/bitget_${timestamp}.log"
fi

# 显示配置信息
echo "⚙️ 当前配置:"
echo "   • 批次大小: $(grep -o '"batch_size": [0-9]*' bitget_config.json | grep -o '[0-9]*')"
echo "   • 重连策略: $(grep -o '"reconnect_strategy": "[^"]*"' bitget_config.json | cut -d'"' -f4)"
echo "   • 重连延迟: $(grep -o '"reconnect_delay": "[^"]*"' bitget_config.json | cut -d'"' -f4)"
echo "   • 最大延迟: $(grep -o '"max_reconnect_delay": "[^"]*"' bitget_config.json | cut -d'"' -f4)"
echo "   • Ping间隔: $(grep -o '"ping_interval": "[^"]*"' bitget_config.json | cut -d'"' -f4)"

echo ""
echo "🔧 增强功能:"
echo "   ✅ 智能重连策略 - 基于连接健康评分的动态延迟"
echo "   ✅ 连接分散控制 - 避免重连风暴"
echo "   ✅ 错误模式识别 - 针对不同错误类型的专门处理"
echo "   ✅ 网络质量监控 - 自适应调整重连参数"
echo "   ✅ 优先级重连 - 交易数据优先恢复"
echo ""

# 编译程序
echo "🔨 编译程序..."
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"

# 设置信号处理
trap 'echo ""; echo "🛑 收到停止信号，正在优雅关闭..."; kill $PID 2>/dev/null; wait $PID 2>/dev/null; echo "✅ 程序已停止"; exit 0' INT TERM

# 启动程序
echo "🎯 启动增强版 Bitget 订阅程序..."
echo "💡 使用 Ctrl+C 优雅停止程序"
echo "📊 实时日志将显示在终端，详细日志保存在 logs/bitget.log"
echo ""

# 启动程序并显示实时日志
./bitget-subscriber-enhanced &
PID=$!

# 等待程序启动
sleep 2

# 显示实时日志（过滤重要信息）
tail -f logs/bitget.log | while read line; do
    # 只显示重要的日志信息
    if echo "$line" | grep -E "(启动|连接|错误|统计|健康|重连|稳定性)" > /dev/null; then
        echo "$line"
    fi
done &

# 等待主程序结束
wait $PID

echo "🏁 程序执行完毕"
