#!/bin/bash

# Kafka优化脚本 - 解决超时问题

echo "🔧 Kafka配置优化"
echo "================"

echo "📊 当前问题分析:"
echo "   • Kafka消息超时错误频发"
echo "   • 错误率: 20.88%"
echo "   • 队列状态: 拥堵"
echo "   • 主要影响: bitget-funding topic"
echo ""

echo "🔧 已应用的优化:"
echo "   ✅ 队列大小: 2GB → 5GB"
echo "   ✅ 队列消息数: 2000万 → 5000万"
echo "   ✅ 请求超时: 60秒 → 120秒"
echo "   ✅ 交付超时: 5分钟 → 10分钟"
echo "   ✅ 网络缓冲区: 2MB → 8MB"
echo "   ✅ 错误日志抑制: 每100个错误记录1个"
echo ""

echo "💡 建议的系统级优化:"
echo ""

# 检查系统内存
total_mem=$(free -h | awk '/^Mem:/ {print $2}')
echo "🔍 系统内存: $total_mem"

# 检查可用磁盘空间
disk_space=$(df -h . | awk 'NR==2 {print $4}')
echo "🔍 可用磁盘: $disk_space"

# 检查网络连接
echo "🔍 检查Kafka连接..."
if command -v telnet >/dev/null 2>&1; then
    # 这里需要替换为实际的Kafka broker地址
    echo "   请手动检查Kafka broker连接性"
else
    echo "   telnet未安装，无法检查连接"
fi

echo ""
echo "🚀 推荐的运行参数:"
echo ""

# 生成优化的启动命令
cat > run_optimized.sh << 'EOF'
#!/bin/bash

echo "🚀 启动优化版 Bitget 程序"
echo "========================"

# 设置Go运行时参数
export GOGC=100                    # 垃圾回收频率
export GOMAXPROCS=4                # 最大CPU核心数
export GOMEMLIMIT=8GiB             # 内存限制

# 停止现有进程
pkill -f "bitget-subscriber-enhanced" 2>/dev/null || true
sleep 3

# 编译
if ! go build -o bitget-subscriber-enhanced .; then
    echo "❌ 编译失败"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 备份日志
if [ -f "logs/bitget.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv logs/bitget.log logs/bitget_optimized_${timestamp}.log
fi

echo "🔧 优化配置:"
echo "   • Kafka队列: 5GB / 5000万消息"
echo "   • 超时时间: 10分钟"
echo "   • 网络缓冲: 8MB"
echo "   • 错误抑制: 启用"
echo "   • Go内存限制: 8GB"
echo ""

# 启动程序
./bitget-subscriber-enhanced &
PID=$!
echo $PID > bitget-subscriber-enhanced.pid

echo "✅ 程序已启动 (PID: $PID)"
echo ""
echo "📊 监控命令:"
echo "   查看Kafka错误: tail -f logs/bitget.log | grep 'Kafka.*失败'"
echo "   查看队列状态: tail -f logs/bitget.log | grep '队列:'"
echo "   查看错误率:   tail -f logs/bitget.log | grep 'Kafka自身统计'"
echo ""
EOF

chmod +x run_optimized.sh

echo "✅ 已生成优化启动脚本: run_optimized.sh"
echo ""

echo "📋 故障排除步骤:"
echo ""
echo "1. 🔄 重启程序使用新配置:"
echo "   ./run_optimized.sh"
echo ""
echo "2. 📊 监控Kafka错误率:"
echo "   tail -f logs/bitget.log | grep 'Kafka自身统计'"
echo ""
echo "3. 🔍 如果错误率仍然很高，检查:"
echo "   • Kafka broker是否健康"
echo "   • 网络连接是否稳定"
echo "   • 系统资源是否充足"
echo ""
echo "4. 🎯 预期效果:"
echo "   • 错误率从20.88%降低到<5%"
echo "   • 队列状态从'拥堵'变为'健康'"
echo "   • 减少超时错误"
echo ""

echo "💡 如果问题持续，可能需要:"
echo "   • 增加Kafka分区数"
echo "   • 优化Kafka broker配置"
echo "   • 考虑数据采样或过滤"
