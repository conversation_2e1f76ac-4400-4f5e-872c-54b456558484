package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// BitgetTradeData Bitget交易数据结构
type BitgetTradeData struct {
	InstID    string `json:"instId"`  // 交易对
	TradeID   string `json:"tradeId"` // 交易ID
	Price     string `json:"price"`   // 成交价格
	Size      string `json:"size"`    // 成交数量
	Side      string `json:"side"`    // 交易方向 buy/sell
	Timestamp string `json:"ts"`      // 时间戳
}

// BitgetBooks1Data Bitget一档盘口数据结构
type BitgetBooks1Data struct {
	InstID string     `json:"instId"` // 交易对
	Bids   [][]string `json:"bids"`   // 买一档 [价格, 数量]
	Asks   [][]string `json:"asks"`   // 卖一档 [价格, 数量]
	Ts     string     `json:"ts"`     // 时间戳
}

// BitgetTickerData Bitget盘口数据结构（保留兼容性）
type BitgetTickerData struct {
	InstID    string `json:"instId"`    // 交易对
	BestBidPx string `json:"bestBidPx"` // 最优买价
	BestBidSz string `json:"bestBidSz"` // 最优买量
	BestAskPx string `json:"bestAskPx"` // 最优卖价
	BestAskSz string `json:"bestAskSz"` // 最优卖量
	Timestamp string `json:"ts"`        // 时间戳
}

// BitgetDepthData Bitget深度数据结构
type BitgetDepthData struct {
	InstID    string     `json:"instId"`   // 交易对
	Bids      [][]string `json:"bids"`     // 买单深度 [价格, 数量]
	Asks      [][]string `json:"asks"`     // 卖单深度 [价格, 数量]
	Timestamp string     `json:"ts"`       // 时间戳
	Checksum  int        `json:"checksum"` // 校验和
}

// BitgetFundingData Bitget资金费率数据结构
type BitgetFundingData struct {
	InstID          string `json:"instId"`          // 交易对
	FundingRate     string `json:"fundingRate"`     // 资金费率
	NextFundingTime string `json:"nextFundingTime"` // 下次资金费率时间
	Timestamp       string `json:"ts"`              // 时间戳
}

// BitgetDataHandler Bitget数据处理器接口
type BitgetDataHandler interface {
	HandleTradeData(data *BitgetTradeData)     // 处理交易数据
	HandleBooks1Data(data *BitgetBooks1Data)   // 处理一档盘口数据
	HandleTickerData(data *BitgetTickerData)   // 处理盘口数据（保留兼容性）
	HandleDepthData(data *BitgetDepthData)     // 处理深度数据
	HandleFundingData(data *BitgetFundingData) // 处理资金费率数据
}

// DefaultBitgetDataHandler 默认Bitget数据处理器
type DefaultBitgetDataHandler struct {
	logger *logrus.Logger
}

// NewDefaultBitgetDataHandler 创建默认Bitget数据处理器
func NewDefaultBitgetDataHandler(logger *logrus.Logger) *DefaultBitgetDataHandler {
	return &DefaultBitgetDataHandler{
		logger: logger,
	}
}

// HandleTradeData 处理交易数据
func (h *DefaultBitgetDataHandler) HandleTradeData(data *BitgetTradeData) {
	// 只处理数据，不输出详细日志
}

// HandleBooks1Data 处理一档盘口数据
func (h *DefaultBitgetDataHandler) HandleBooks1Data(data *BitgetBooks1Data) {
	// 只处理数据，不输出详细日志
}

// HandleTickerData 处理盘口数据
func (h *DefaultBitgetDataHandler) HandleTickerData(data *BitgetTickerData) {
	// 只处理数据，不输出详细日志
}

// HandleDepthData 处理深度数据
func (h *DefaultBitgetDataHandler) HandleDepthData(data *BitgetDepthData) {
	// 只处理数据，不输出详细日志
}

// HandleFundingData 处理资金费率数据
func (h *DefaultBitgetDataHandler) HandleFundingData(data *BitgetFundingData) {
	// 只处理数据，不输出详细日志
}

// getBestBitgetPrice 获取最优价格
func getBestBitgetPrice(levels [][]string, isBid bool) string {
	if len(levels) == 0 {
		return "N/A"
	}
	return levels[0][0] // 第一档价格
}

// FileBitgetDataHandler 文件数据处理器
type FileBitgetDataHandler struct {
	logger     *logrus.Logger
	tradeFile  string
	tickerFile string
	depthFile  string
}

// NewFileBitgetDataHandler 创建文件数据处理器
func NewFileBitgetDataHandler(logger *logrus.Logger, tradeFile, tickerFile, depthFile string) *FileBitgetDataHandler {
	return &FileBitgetDataHandler{
		logger:     logger,
		tradeFile:  tradeFile,
		tickerFile: tickerFile,
		depthFile:  depthFile,
	}
}

// HandleTradeData 处理交易数据并写入文件
func (h *FileBitgetDataHandler) HandleTradeData(data *BitgetTradeData) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📈 Bitget交易数据写入文件 %s: %s", h.tradeFile, string(jsonData))
}

// HandleBooks1Data 处理一档盘口数据并写入文件
func (h *FileBitgetDataHandler) HandleBooks1Data(data *BitgetBooks1Data) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📊 Bitget一档盘口数据写入文件 %s: %s", h.tickerFile, string(jsonData))
}

// HandleTickerData 处理盘口数据并写入文件
func (h *FileBitgetDataHandler) HandleTickerData(data *BitgetTickerData) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📊 Bitget盘口数据写入文件 %s: %s", h.tickerFile, string(jsonData))
}

// HandleDepthData 处理深度数据并写入文件
func (h *FileBitgetDataHandler) HandleDepthData(data *BitgetDepthData) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("📋 Bitget深度数据写入文件 %s: %s", h.depthFile, string(jsonData))
}

// HandleFundingData 处理资金费率数据并写入文件
func (h *FileBitgetDataHandler) HandleFundingData(data *BitgetFundingData) {
	jsonData, _ := json.Marshal(data)
	h.logger.Debugf("💰 Bitget资金费率数据写入文件: %s", string(jsonData))
}

// DatabaseBitgetDataHandler 数据库数据处理器
type DatabaseBitgetDataHandler struct {
	logger *logrus.Logger
}

// NewDatabaseBitgetDataHandler 创建数据库数据处理器
func NewDatabaseBitgetDataHandler(logger *logrus.Logger) *DatabaseBitgetDataHandler {
	return &DatabaseBitgetDataHandler{
		logger: logger,
	}
}

// HandleTradeData 处理交易数据并写入数据库
func (h *DatabaseBitgetDataHandler) HandleTradeData(data *BitgetTradeData) {
	h.logger.Debugf("📈 Bitget交易数据写入数据库: %+v", data)
}

// HandleBooks1Data 处理一档盘口数据并写入数据库
func (h *DatabaseBitgetDataHandler) HandleBooks1Data(data *BitgetBooks1Data) {
	h.logger.Debugf("📊 Bitget一档盘口数据写入数据库: %+v", data)
}

// HandleTickerData 处理盘口数据并写入数据库
func (h *DatabaseBitgetDataHandler) HandleTickerData(data *BitgetTickerData) {
	h.logger.Debugf("📊 Bitget盘口数据写入数据库: %+v", data)
}

// HandleDepthData 处理深度数据并写入数据库
func (h *DatabaseBitgetDataHandler) HandleDepthData(data *BitgetDepthData) {
	h.logger.Debugf("📋 Bitget深度数据写入数据库: %+v", data)
}

// HandleFundingData 处理资金费率数据并写入数据库
func (h *DatabaseBitgetDataHandler) HandleFundingData(data *BitgetFundingData) {
	h.logger.Debugf("💰 Bitget资金费率数据写入数据库: %+v", data)
}

// parseBitgetTradeData 解析Bitget交易数据
func parseBitgetTradeData(message []byte) (*BitgetTradeData, error) {
	var data BitgetTradeData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析Bitget交易数据失败: %v", err)
	}
	return &data, nil
}

// parseBitgetTickerData 解析Bitget盘口数据
func parseBitgetTickerData(message []byte) (*BitgetTickerData, error) {
	var data BitgetTickerData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析Bitget盘口数据失败: %v", err)
	}
	return &data, nil
}

// parseBitgetDepthData 解析Bitget深度数据
func parseBitgetDepthData(message []byte) (*BitgetDepthData, error) {
	var data BitgetDepthData
	if err := json.Unmarshal(message, &data); err != nil {
		return nil, fmt.Errorf("解析Bitget深度数据失败: %v", err)
	}
	return &data, nil
}

// parseTimestamp 解析时间戳
func parseTimestamp(ts string) (int64, error) {
	if ts == "" {
		return time.Now().UnixMilli(), nil
	}

	// 尝试解析为整数时间戳
	var timestamp int64
	if err := json.Unmarshal([]byte(ts), &timestamp); err != nil {
		return time.Now().UnixMilli(), err
	}
	return timestamp, nil
}
