package main

import (
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/sirupsen/logrus"
)

// KafkaProducer Kafka生产者
type KafkaProducer struct {
	producer *kafka.Producer
	config   *KafkaConfig
	logger   *logrus.Logger

	// 统计计数器
	tradesSent     int64
	orderbooksSent int64
	fundingSent    int64
	totalSent      int64
	errorCount     int64

	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// KafkaMessage Kafka消息结构
type KafkaMessage struct {
	Exchange  string      `json:"exchange"`
	Symbol    string      `json:"symbol"`
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp int64       `json:"timestamp"`
}

// NewKafkaProducer 创建新的Kafka生产者
func NewKafkaProducer(config *KafkaConfig, logger *logrus.Logger) (*KafkaProducer, error) {
	if !config.Enabled {
		logger.Info("🔧 [Bitget] Kafka生产者被禁用")
		return nil, nil
	}

	logger.WithFields(logrus.Fields{
		"brokers":   config.Brokers,
		"username":  config.SASLUsername,
		"mechanism": config.SASLMechanism,
		"protocol":  config.SecurityProtocol,
	}).Info("🔧 [Bitget] 开始创建Kafka生产者配置")

	// 构建Kafka配置 - 优化高吞吐量场景，确保数据完整性
	configMap := &kafka.ConfigMap{
		"bootstrap.servers": fmt.Sprintf("%s", config.Brokers[0]),
		"security.protocol": config.SecurityProtocol,
		"sasl.mechanism":    config.SASLMechanism,
		"sasl.username":     config.SASLUsername,
		"sasl.password":     config.SASLPassword,
		"acks":              config.ProducerConfig.Acks,
		"retries":           config.ProducerConfig.Retries,
		"batch.size":        config.ProducerConfig.BatchSize,
		"linger.ms":         config.ProducerConfig.LingerMs,

		// 大幅增加队列大小以处理高频数据，确保数据不丢失
		"queue.buffering.max.kbytes":   2097152,  // 2GB队列缓冲区（KB为单位）
		"queue.buffering.max.messages": 20000000, // 增加到2000万条消息

		// 优化压缩和批处理
		"compression.type":                      "snappy", // 使用snappy压缩算法，平衡压缩率和速度
		"max.in.flight.requests.per.connection": 5,        // 幂等性要求≤5

		// 优化超时设置 - 给更多时间确保发送成功
		"request.timeout.ms":  60000,  // 1分钟请求超时
		"delivery.timeout.ms": 300000, // 5分钟交付超时
		"message.timeout.ms":  300000, // 5分钟消息超时

		// 启用幂等性以提高可靠性
		"enable.idempotence": true,

		// 优化网络缓冲区
		"socket.send.buffer.bytes":    2097152, // 2MB发送缓冲区
		"socket.receive.buffer.bytes": 2097152, // 2MB接收缓冲区

		// 确保数据完整性的设置
		"retry.backoff.ms":     1000, // 重试间隔1秒
		"reconnect.backoff.ms": 1000, // 重连间隔1秒
	}

	// 打印完整配置（除了密码）
	logger.Debug("🔧 [Bitget] Kafka ConfigMap详情:")
	for key, value := range *configMap {
		if key == "sasl.password" {
			logger.Debugf("   %s: [隐藏]", key)
		} else {
			logger.Debugf("   %s: %v", key, value)
		}
	}

	// 创建生产者
	logger.Info("🔧 [Bitget] 正在创建Kafka生产者实例...")
	producer, err := kafka.NewProducer(configMap)
	if err != nil {
		logger.WithError(err).Error("❌ [Bitget] Kafka生产者实例创建失败")
		return nil, fmt.Errorf("创建Kafka生产者失败: %v", err)
	}

	logger.Info("✅ [Bitget] Kafka生产者实例创建成功")

	kp := &KafkaProducer{
		producer: producer,
		config:   config,
		logger:   logger,
		stopChan: make(chan struct{}),
	}

	// 启动事件处理协程
	kp.wg.Add(1)
	go kp.handleEvents()

	// 启动统计协程
	kp.wg.Add(1)
	go kp.statisticsLoop()

	logger.WithFields(logrus.Fields{
		"brokers":            config.Brokers,
		"topics":             fmt.Sprintf("trades=%s, orderbooks=%s, funding=%s", config.Topics.Trades, config.Topics.Orderbooks, config.Topics.Funding),
		"queue_max_messages": 20000000,
		"queue_max_kbytes":   2097152,
		"compression":        "snappy",
		"max_in_flight":      5,
		"acks":               "all",
		"idempotence":        true,
	}).Info("🚀 [Bitget] Kafka生产者已启动 - 高可靠性配置")

	return kp, nil
}

// SendTradeData 发送交易数据
func (kp *KafkaProducer) SendTradeData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "bitget",
		Symbol:    symbol,
		Type:      "trade",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Trades, message, &kp.tradesSent)
}

// SendOrderbookData 发送盘口数据
func (kp *KafkaProducer) SendOrderbookData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "bitget",
		Symbol:    symbol,
		Type:      "orderbook",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Orderbooks, message, &kp.orderbooksSent)
}

// SendFundingData 发送资金费率数据
func (kp *KafkaProducer) SendFundingData(symbol string, data interface{}) {
	if kp == nil || kp.producer == nil {
		return
	}

	message := KafkaMessage{
		Exchange:  "bitget",
		Symbol:    symbol,
		Type:      "funding",
		Data:      data,
		Timestamp: time.Now().UnixMilli(),
	}

	kp.sendMessage(kp.config.Topics.Funding, message, &kp.fundingSent)
}

// sendMessage 发送消息到Kafka
func (kp *KafkaProducer) sendMessage(topic string, message KafkaMessage, counter *int64) {
	jsonData, err := json.Marshal(message)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		kp.logger.Errorf("❌ [Bitget] 序列化Kafka消息失败: %v", err)
		return
	}

	kafkaMessage := &kafka.Message{
		TopicPartition: kafka.TopicPartition{Topic: &topic, Partition: kafka.PartitionAny},
		Value:          jsonData,
		Key:            []byte(message.Symbol),
	}

	// 非阻塞式发送，使用事件回调处理结果
	err = kp.producer.Produce(kafkaMessage, nil)
	if err != nil {
		// 如果是队列满，记录警告但不重试（依赖Kafka客户端内部重试）
		if err.Error() == "Local: Queue full" {
			kp.logger.Warnf("⚠️ [Bitget] Kafka队列满，等待重试以确保数据完整性 - Topic: %s, Symbol: %s", topic, message.Symbol)
			// 尝试强制flush一些消息
			kp.producer.Flush(100) // 100ms flush

			// 再次尝试发送
			err = kp.producer.Produce(kafkaMessage, nil)
			if err != nil {
				atomic.AddInt64(&kp.errorCount, 1)
				kp.logger.Errorf("❌ [Bitget] Kafka队列仍然满，消息暂时丢失 - Topic: %s, Symbol: %s", topic, message.Symbol)
				return
			}
		} else {
			// 其他类型的错误，记录并退出
			atomic.AddInt64(&kp.errorCount, 1)
			kp.logger.Errorf("❌ [Bitget] 发送Kafka消息失败 - Topic: %s, Error: %v", topic, err)
			return
		}
	}

	// 发送成功（或已入队列）
	atomic.AddInt64(counter, 1)
	atomic.AddInt64(&kp.totalSent, 1)
}

// handleEvents 处理Kafka事件
func (kp *KafkaProducer) handleEvents() {
	defer kp.wg.Done()

	for {
		select {
		case <-kp.stopChan:
			return
		case e := <-kp.producer.Events():
			switch ev := e.(type) {
			case *kafka.Message:
				if ev.TopicPartition.Error != nil {
					atomic.AddInt64(&kp.errorCount, 1)
					kp.logger.Errorf("❌ [Bitget] Kafka消息发送失败 - Topic: %s, Error: %v", *ev.TopicPartition.Topic, ev.TopicPartition.Error)
				}
			case kafka.Error:
				atomic.AddInt64(&kp.errorCount, 1)
				kp.logger.Errorf("❌ [Bitget] Kafka错误: %v", ev)
			}
		}
	}
}

// statisticsLoop 统计循环
func (kp *KafkaProducer) statisticsLoop() {
	defer kp.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	var lastTrades, lastOrderbooks, lastFunding, lastTotal, lastErrors int64
	startTime := time.Now()

	for {
		select {
		case <-kp.stopChan:
			return
		case <-ticker.C:
			trades := atomic.LoadInt64(&kp.tradesSent)
			orderbooks := atomic.LoadInt64(&kp.orderbooksSent)
			funding := atomic.LoadInt64(&kp.fundingSent)
			total := atomic.LoadInt64(&kp.totalSent)
			errors := atomic.LoadInt64(&kp.errorCount)

			// 计算速率
			tradeRate := trades - lastTrades
			orderbookRate := orderbooks - lastOrderbooks
			fundingRate := funding - lastFunding
			totalRate := total - lastTotal
			errorRate := errors - lastErrors

			// 计算错误率
			var errorPercentage float64
			if total > 0 {
				errorPercentage = float64(errors) / float64(total+errors) * 100
			}

			// 队列健康状态
			queueStatus := "健康"
			if errorRate > 100 {
				queueStatus = "拥堵"
			} else if errorRate > 10 {
				queueStatus = "繁忙"
			}

			// 使用 logrus 但格式化为简洁格式
			kp.logger.WithFields(logrus.Fields{
				"trades":     trades,
				"orderbooks": orderbooks,
				"funding":    funding,
				"total":      total,
				"errors":     errors,
				"queue":      queueStatus,
				"uptime":     time.Since(startTime).Round(time.Second),
			}).Infof("📊 [Bitget] Kafka自身统计 - 交易: %d条 (%d/10s), 盘口: %d条 (%d/10s), 资金费率: %d条 (%d/10s), 总计: %d条 (%d/10s), 错误: %d条 (%d/10s, %.2f%%), 队列: %s",
				trades, tradeRate, orderbooks, orderbookRate, funding, fundingRate, total, totalRate, errors, errorRate, errorPercentage, queueStatus)

			lastTrades = trades
			lastOrderbooks = orderbooks
			lastFunding = funding
			lastTotal = total
			lastErrors = errors
		}
	}
}

// GetStats 获取统计信息
func (kp *KafkaProducer) GetStats() (trades, orderbooks, funding, total, errors int64) {
	if kp == nil {
		return 0, 0, 0, 0, 0
	}
	return atomic.LoadInt64(&kp.tradesSent),
		atomic.LoadInt64(&kp.orderbooksSent),
		atomic.LoadInt64(&kp.fundingSent),
		atomic.LoadInt64(&kp.totalSent),
		atomic.LoadInt64(&kp.errorCount)
}

// Close 关闭Kafka生产者
func (kp *KafkaProducer) Close() {
	if kp == nil || kp.producer == nil {
		return
	}

	kp.logger.Info("🔄 [Bitget] 正在关闭Kafka生产者...")

	// 停止统计和事件处理协程
	close(kp.stopChan)

	// 等待所有消息发送完成
	kp.producer.Flush(5000) // 5秒超时

	// 关闭生产者
	kp.producer.Close()

	// 等待协程结束
	kp.wg.Wait()

	// 输出最终统计
	trades, orderbooks, funding, total, errors := kp.GetStats()
	kp.logger.Infof("📊 [Bitget] Kafka生产者已关闭 - 最终统计: 交易: %d条, 盘口: %d条, 资金费率: %d条, 总计: %d条, 错误: %d条",
		trades, orderbooks, funding, total, errors)
}
