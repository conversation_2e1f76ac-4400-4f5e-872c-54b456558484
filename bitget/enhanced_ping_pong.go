package main

import (
	"context"
	"math"
	"math/rand"
	"net"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// EnhancedPingPongManager 增强的ping/pong管理器
type EnhancedPingPongManager struct {
	client *BitgetClient
	logger *logrus.Logger
	config *BitgetConfig

	// 连接质量统计
	qualityStats struct {
		totalConnections int
		excellentCount   int // 90%+
		goodCount        int // 70-90%
		averageCount     int // 40-70%
		poorCount        int // 30-60%
		veryPoorCount    int // <30%
		averageQuality   float64
		mutex            sync.RWMutex
	}

	// 全局统计
	globalStats struct {
		totalPings      int64
		totalPongs      int64
		totalTimeouts   int64
		totalReconnects int64
		avgResponseTime time.Duration
		maxResponseTime time.Duration
		minResponseTime time.Duration
		mutex           sync.RWMutex
	}
}

// NewEnhancedPingPongManager 创建增强ping/pong管理器
func NewEnhancedPingPongManager(client *BitgetClient, logger *logrus.Logger, config *BitgetConfig) *EnhancedPingPongManager {
	manager := &EnhancedPingPongManager{
		client: client,
		logger: logger,
		config: config,
	}

	// 初始化最小响应时间
	manager.globalStats.minResponseTime = time.Hour

	return manager
}

// StartEnhancedHeartbeat 启动增强心跳机制
func (m *EnhancedPingPongManager) StartEnhancedHeartbeat(connKey string, connInfo *ConnectionInfo) {
	// 主动ping定时器
	pingTicker := time.NewTicker(m.config.PingInterval)
	defer pingTicker.Stop()

	// 连接健康检查定时器（更频繁）
	healthTicker := time.NewTicker(m.config.PingInterval / 3)
	defer healthTicker.Stop()

	// 连接质量评估定时器
	qualityTicker := time.NewTicker(10 * time.Second)
	defer qualityTicker.Stop()

	m.logger.Infof("💓 [Bitget] 连接 %s 增强心跳启动 - ping间隔: %v, 健康检查: %v",
		connKey, m.config.PingInterval, m.config.PingInterval/3)

	for {
		select {
		case <-pingTicker.C:
			// 主动发送ping
			m.sendEnhancedPing(connKey, connInfo)

		case <-healthTicker.C:
			// 连接健康检查
			if !m.checkEnhancedConnectionHealth(connKey, connInfo) {
				m.logger.Errorf("💔 [Bitget] 连接 %s 健康检查失败，触发重连", connKey)
				m.triggerReconnection(connKey, connInfo)
				return
			}

		case <-qualityTicker.C:
			// 连接质量评估
			m.assessConnectionQuality(connKey, connInfo)

		case <-connInfo.heartbeatStop:
			m.logger.Debugf("💓 [Bitget] 连接 %s 心跳停止", connKey)
			return

		case <-m.client.ctx.Done():
			m.logger.Debugf("💓 [Bitget] 连接 %s 心跳因上下文取消而停止", connKey)
			return
		}
	}
}

// sendEnhancedPing 发送增强ping消息
func (m *EnhancedPingPongManager) sendEnhancedPing(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	connInfo.mutex.RUnlock()

	if conn == nil || state != StateConnected {
		return
	}

	startTime := time.Now()

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		m.logger.Debugf("📨 [Bitget] 连接 %s 收到服务器ping，发送pong响应", connKey)

		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			m.logger.WithError(err).Errorf("💔 [Bitget] 连接 %s pong响应发送失败", connKey)
			return err
		}
		return nil
	})

	// 设置pong处理器（处理我们ping的响应）
	conn.SetPongHandler(func(appData string) error {
		responseTime := time.Since(startTime)

		connInfo.mutex.Lock()
		connInfo.lastPongTime = time.Now()
		connInfo.mutex.Unlock()

		m.updateGlobalStats(responseTime, true)
		m.logger.Debugf("📨 [Bitget] 连接 %s 收到pong响应，延迟: %v", connKey, responseTime)
		return nil
	})

	// 发送WebSocket ping帧（主动ping）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.PingMessage, []byte("bitget-enhanced-ping")); err != nil {
		m.logger.WithError(err).Errorf("💔 [Bitget] 连接 %s WebSocket ping发送失败", connKey)
		m.updateGlobalStats(0, false)
		return
	}

	// 同时发送应用层ping消息（双重保障）
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	if err := conn.WriteMessage(websocket.TextMessage, []byte("ping")); err != nil {
		m.logger.WithError(err).Errorf("💔 [Bitget] 连接 %s 应用层ping发送失败", connKey)
		m.updateGlobalStats(0, false)
		return
	}

	connInfo.mutex.Lock()
	connInfo.lastPingTime = time.Now()
	connInfo.mutex.Unlock()

	m.updateGlobalStats(0, true) // 记录ping发送
	m.logger.Debugf("💓 [Bitget] 连接 %s 发送增强双重ping", connKey)
}

// checkEnhancedConnectionHealth 增强连接健康检查
func (m *EnhancedPingPongManager) checkEnhancedConnectionHealth(connKey string, connInfo *ConnectionInfo) bool {
	connInfo.mutex.RLock()
	conn := connInfo.conn
	state := connInfo.state
	lastPongTime := connInfo.lastPongTime
	lastPingTime := connInfo.lastPingTime
	lastMessageTime := connInfo.lastMessageTime
	messageCount := connInfo.messageCount
	connInfo.mutex.RUnlock()

	if conn == nil {
		m.logger.Debugf("🔍 [Bitget] 连接 %s 健康检查：连接为空", connKey)
		return false
	}

	if state != StateConnected {
		m.logger.Debugf("🔍 [Bitget] 连接 %s 健康检查：状态为 %v", connKey, state)
		return false
	}

	now := time.Now()

	// 检查pong响应超时
	pongDelay := now.Sub(lastPongTime)
	maxPongDelay := m.config.PingInterval * 8 // 8倍ping间隔

	if pongDelay > maxPongDelay {
		m.logger.Errorf("💔 [Bitget] 连接 %s pong超时: %.1fs (最大: %.1fs)",
			connKey, pongDelay.Seconds(), maxPongDelay.Seconds())

		connInfo.mutex.Lock()
		connInfo.consecutiveTimeouts++
		connInfo.mutex.Unlock()

		m.updateGlobalStats(0, false) // 记录超时
		return false
	}

	// 检查消息活跃度
	messageDelay := now.Sub(lastMessageTime)
	maxMessageDelay := 5 * time.Minute // 5分钟无消息认为异常

	if messageDelay > maxMessageDelay && messageCount > 0 {
		m.logger.Warnf("⚠️ [Bitget] 连接 %s 消息活跃度低: %.1f分钟无消息",
			connKey, messageDelay.Minutes())
	}

	// 检查ping频率
	pingDelay := now.Sub(lastPingTime)
	maxPingDelay := m.config.PingInterval * 3

	if pingDelay > maxPingDelay {
		m.logger.Warnf("⚠️ [Bitget] 连接 %s ping间隔过长: %.1fs", connKey, pingDelay.Seconds())
	}

	// 重置连续超时计数
	connInfo.mutex.Lock()
	connInfo.consecutiveTimeouts = 0
	connInfo.mutex.Unlock()

	return true
}

// assessConnectionQuality 评估连接质量
func (m *EnhancedPingPongManager) assessConnectionQuality(connKey string, connInfo *ConnectionInfo) {
	connInfo.mutex.Lock()
	defer connInfo.mutex.Unlock()

	now := time.Now()

	// 计算各项指标
	pongDelay := now.Sub(connInfo.lastPongTime)
	messageDelay := now.Sub(connInfo.lastMessageTime)

	// 延迟评分 (40%)
	var delayScore float64 = 1.0
	if pongDelay > 0 {
		idealDelay := m.config.PingInterval
		delayRatio := float64(pongDelay) / float64(idealDelay)
		delayScore = math.Max(0, 1.0-delayRatio/4.0) // 4倍理想延迟时评分为0
	}

	// 超时评分 (30%)
	timeoutScore := math.Max(0, 1.0-float64(connInfo.consecutiveTimeouts)/5.0)

	// 消息活跃度评分 (30%)
	var activityScore float64 = 1.0
	if connInfo.messageCount > 0 && messageDelay > 0 {
		idealActivity := 30 * time.Second // 理想情况下30秒内有消息
		activityRatio := float64(messageDelay) / float64(idealActivity)
		activityScore = math.Max(0, 1.0-activityRatio/10.0) // 10倍理想时间时评分为0
	}

	// 综合质量评分
	quality := delayScore*0.4 + timeoutScore*0.3 + activityScore*0.3
	connInfo.connectionQuality = quality
	connInfo.lastQualityCheck = now

	// 更新平均pong延迟（指数移动平均）
	if pongDelay > 0 && pongDelay < time.Minute {
		alpha := 0.1 // 平滑因子
		if connInfo.avgPongDelay == 0 {
			connInfo.avgPongDelay = pongDelay
		} else {
			connInfo.avgPongDelay = time.Duration(float64(connInfo.avgPongDelay)*(1-alpha) + float64(pongDelay)*alpha)
		}
	}

	// 记录质量等级
	qualityLevel := m.getQualityLevel(quality)

	if quality < 0.3 {
		m.logger.Warnf("⚠️ [Bitget] 连接 %s 质量较差: %.1f%% (%s) - 延迟:%.1f%%, 超时:%.1f%%, 活跃度:%.1f%%",
			connKey, quality*100, qualityLevel, delayScore*100, timeoutScore*100, activityScore*100)
	} else if quality < 0.7 {
		m.logger.Debugf("🔍 [Bitget] 连接 %s 质量一般: %.1f%% (%s)", connKey, quality*100, qualityLevel)
	}
}

// getQualityLevel 获取质量等级描述
func (m *EnhancedPingPongManager) getQualityLevel(quality float64) string {
	if quality >= 0.9 {
		return "优秀"
	} else if quality >= 0.7 {
		return "良好"
	} else if quality >= 0.4 {
		return "一般"
	} else if quality >= 0.3 {
		return "较差"
	} else {
		return "极差"
	}
}

// triggerReconnection 触发重连
func (m *EnhancedPingPongManager) triggerReconnection(connKey string, connInfo *ConnectionInfo) {
	m.logger.Infof("🔄 [Bitget] 连接 %s 触发增强重连机制", connKey)

	// 清理当前连接
	m.client.cleanupConnection(connKey, connInfo)

	// 增加重连计数
	m.client.incrementReconnectCount(connKey)

	// 更新全局统计
	m.updateGlobalStats(0, false)
	m.globalStats.mutex.Lock()
	m.globalStats.totalReconnects++
	m.globalStats.mutex.Unlock()

	// 启动重连
	go m.client.subscribeWithRetry(connKey, connInfo.symbols, connInfo.channel, connInfo.messageHandler)
}

// updateGlobalStats 更新全局统计
func (m *EnhancedPingPongManager) updateGlobalStats(responseTime time.Duration, success bool) {
	m.globalStats.mutex.Lock()
	defer m.globalStats.mutex.Unlock()

	if success && responseTime > 0 {
		// 更新pong统计
		m.globalStats.totalPongs++

		// 更新响应时间统计
		if m.globalStats.avgResponseTime == 0 {
			m.globalStats.avgResponseTime = responseTime
		} else {
			// 指数移动平均
			alpha := 0.1
			m.globalStats.avgResponseTime = time.Duration(float64(m.globalStats.avgResponseTime)*(1-alpha) + float64(responseTime)*alpha)
		}

		if responseTime > m.globalStats.maxResponseTime {
			m.globalStats.maxResponseTime = responseTime
		}

		if responseTime < m.globalStats.minResponseTime {
			m.globalStats.minResponseTime = responseTime
		}
	} else if success {
		// ping发送成功
		m.globalStats.totalPings++
	} else {
		// 超时或失败
		m.globalStats.totalTimeouts++
	}
}

// GetConnectionQualityStats 获取连接质量统计
func (m *EnhancedPingPongManager) GetConnectionQualityStats() map[string]interface{} {
	m.qualityStats.mutex.Lock()
	defer m.qualityStats.mutex.Unlock()

	// 重新计算质量统计
	m.calculateQualityStats()

	return map[string]interface{}{
		"total_connections": m.qualityStats.totalConnections,
		"excellent_count":   m.qualityStats.excellentCount,
		"good_count":        m.qualityStats.goodCount,
		"average_count":     m.qualityStats.averageCount,
		"poor_count":        m.qualityStats.poorCount,
		"very_poor_count":   m.qualityStats.veryPoorCount,
		"average_quality":   m.qualityStats.averageQuality,
	}
}

// GetGlobalPingPongStats 获取全局ping/pong统计
func (m *EnhancedPingPongManager) GetGlobalPingPongStats() map[string]interface{} {
	m.globalStats.mutex.RLock()
	defer m.globalStats.mutex.RUnlock()

	return map[string]interface{}{
		"total_pings":       m.globalStats.totalPings,
		"total_pongs":       m.globalStats.totalPongs,
		"total_timeouts":    m.globalStats.totalTimeouts,
		"total_reconnects":  m.globalStats.totalReconnects,
		"avg_response_time": m.globalStats.avgResponseTime,
		"max_response_time": m.globalStats.maxResponseTime,
		"min_response_time": m.globalStats.minResponseTime,
	}
}

// calculateQualityStats 计算质量统计
func (m *EnhancedPingPongManager) calculateQualityStats() {
	m.client.connectionMutex.RLock()
	defer m.client.connectionMutex.RUnlock()

	totalQuality := 0.0
	totalConnections := 0
	excellentCount := 0
	goodCount := 0
	averageCount := 0
	poorCount := 0
	veryPoorCount := 0

	for _, connInfo := range m.client.connections {
		connInfo.mutex.RLock()
		quality := connInfo.connectionQuality
		state := connInfo.state
		connInfo.mutex.RUnlock()

		if state == StateConnected {
			totalConnections++
			totalQuality += quality

			if quality >= 0.9 {
				excellentCount++
			} else if quality >= 0.7 {
				goodCount++
			} else if quality >= 0.4 {
				averageCount++
			} else if quality >= 0.3 {
				poorCount++
			} else {
				veryPoorCount++
			}
		}
	}

	m.qualityStats.totalConnections = totalConnections
	m.qualityStats.excellentCount = excellentCount
	m.qualityStats.goodCount = goodCount
	m.qualityStats.averageCount = averageCount
	m.qualityStats.poorCount = poorCount
	m.qualityStats.veryPoorCount = veryPoorCount

	if totalConnections > 0 {
		m.qualityStats.averageQuality = totalQuality / float64(totalConnections)
	} else {
		m.qualityStats.averageQuality = 0
	}
}

// CreateEnhancedWebSocketDialer 创建增强的WebSocket拨号器
func (m *EnhancedPingPongManager) CreateEnhancedWebSocketDialer() *websocket.Dialer {
	return &websocket.Dialer{
		HandshakeTimeout: 45 * time.Second,
		ReadBufferSize:   8192,
		WriteBufferSize:  8192,
		NetDialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			dialer := &net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}
			return dialer.DialContext(ctx, network, addr)
		},
	}
}

// CalculateSmartReconnectDelay 计算智能重连延迟
func (m *EnhancedPingPongManager) CalculateSmartReconnectDelay(connKey string, reconnectCount int) time.Duration {
	// 基础延迟（指数退避）
	baseDelay := time.Duration(math.Min(float64(3*time.Second)*math.Pow(2, float64(reconnectCount)), float64(60*time.Second)))

	// 添加随机抖动避免雷群效应
	jitter := time.Duration(rand.Intn(3000)) * time.Millisecond // 0-3秒随机抖动

	// 根据连接类型调整延迟
	var typeMultiplier float64 = 1.0
	if strings.Contains(connKey, "trade") {
		typeMultiplier = 0.8 // 交易数据优先级更高
	} else if strings.Contains(connKey, "books1") {
		typeMultiplier = 1.0 // 盘口数据正常延迟
	} else if strings.Contains(connKey, "ticker") {
		typeMultiplier = 1.2 // ticker数据优先级较低
	}

	// 根据重连频率动态调整
	if reconnectCount > 10 {
		typeMultiplier *= 1.5 // 频繁重连增加延迟
	} else if reconnectCount > 5 {
		typeMultiplier *= 1.2
	}

	finalDelay := time.Duration(float64(baseDelay)*typeMultiplier) + jitter

	// 确保延迟在合理范围内
	minDelay := 3 * time.Second
	maxDelay := 5 * time.Minute

	if finalDelay < minDelay {
		finalDelay = minDelay
	}
	if finalDelay > maxDelay {
		finalDelay = maxDelay
	}

	m.logger.Debugf("🧮 [Bitget] 连接 %s 智能重连延迟: 基础=%v, 类型倍数=%.1f, 抖动=%v, 最终=%v",
		connKey, baseDelay, typeMultiplier, jitter, finalDelay)

	return finalDelay
}
