{"api_base_url": "https://api.bitget.com", "ws_base_url": "wss://ws.bitget.com/v2/ws/public", "batch_size": 50, "max_reconnect_attempts": 20, "reconnect_delay": "5s", "max_reconnect_delay": "5m0s", "reconnect_strategy": "infinite", "connection_timeout": "45s", "read_timeout": "5m0s", "write_timeout": "30s", "ping_interval": "30s", "pong_timeout": "60s", "enable_trade_data": true, "enable_book_ticker": true, "enable_depth_data": false, "enable_data_validation": true, "log_level": "debug", "symbol_filter": [], "kafka": {"enabled": true, "brokers": ["43.133.193.167:9092"], "security_protocol": "SASL_PLAINTEXT", "sasl_mechanism": "PLAIN", "sasl_username": "kafka", "sasl_password": "Abc112311", "topics": {"trades": "bitget-trades", "orderbooks": "bitget-orderbooks", "funding": "bitget-funding"}, "producer_config": {"acks": "all", "retries": 5, "batch_size": 131072, "linger_ms": 10, "buffer_memory": 1073741824}}}