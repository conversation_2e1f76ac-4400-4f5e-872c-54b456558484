# 加密货币交易所数据订阅程序集合 - 统一Makefile

# 支持的交易所
EXCHANGES := binance bitget bybit okx hyperliquid gateio

# 默认目标
.PHONY: help
help:
	@echo "🏗️  加密货币交易所数据订阅程序集合"
	@echo ""
	@echo "📋 可用命令:"
	@echo "  make build-all      - 编译所有交易所程序"
	@echo "  make start-all      - 启动所有交易所程序"
	@echo "  make stop-all       - 停止所有交易所程序"
	@echo "  make restart-all    - 重启所有交易所程序"
	@echo "  make status-all     - 查看所有交易所状态"
	@echo "  make logs-all       - 查看所有交易所日志"
	@echo "  make clean-all      - 清理所有交易所编译文件"
	@echo ""
	@echo "📈 单个交易所命令:"
	@echo "  make build-<exchange>    - 编译指定交易所 (如: make build-binance)"
	@echo "  make start-<exchange>    - 启动指定交易所"
	@echo "  make stop-<exchange>     - 停止指定交易所"
	@echo "  make restart-<exchange>  - 重启指定交易所"
	@echo "  make status-<exchange>   - 查看指定交易所状态"
	@echo "  make logs-<exchange>     - 查看指定交易所日志"
	@echo ""
	@echo "🔧 维护命令:"
	@echo "  make init-all       - 初始化所有交易所依赖"
	@echo "  make test-all       - 运行所有测试"
	@echo "  make force-stop-all - 强制停止所有程序"
	@echo ""
	@echo "📊 支持的交易所: $(EXCHANGES)"

# 编译所有交易所
.PHONY: build-all
build-all:
	@echo "🔨 编译所有交易所程序..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 编译 $$exchange..."; \
			(cd $$exchange && go mod tidy && go build -o $$exchange-subscriber .) || echo "❌ $$exchange 编译失败"; \
		else \
			echo "⚠️  目录不存在: $$exchange"; \
		fi; \
	done
	@echo "✅ 所有交易所编译完成"

# 启动所有交易所
.PHONY: start-all
start-all:
	@echo "🚀 启动所有交易所程序..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 启动 $$exchange..."; \
			(cd $$exchange && ./start.sh start) || echo "❌ $$exchange 启动失败"; \
		fi; \
	done
	@echo "✅ 所有交易所启动完成"

# 停止所有交易所
.PHONY: stop-all
stop-all:
	@echo "🛑 停止所有交易所程序..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 停止 $$exchange..."; \
			(cd $$exchange && ./start.sh stop) || true; \
		fi; \
	done
	@echo "✅ 所有交易所停止完成"

# 重启所有交易所
.PHONY: restart-all
restart-all:
	@echo "🔄 重启所有交易所程序..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 重启 $$exchange..."; \
			(cd $$exchange && ./start.sh restart) || echo "❌ $$exchange 重启失败"; \
		fi; \
	done
	@echo "✅ 所有交易所重启完成"

# 查看所有交易所状态
.PHONY: status-all
status-all:
	@echo "📊 查看所有交易所状态..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 $$exchange 状态:"; \
			(cd $$exchange && ./start.sh status) || echo "❌ $$exchange 状态检查失败"; \
			echo ""; \
		fi; \
	done

# 查看所有交易所日志
.PHONY: logs-all
logs-all:
	@echo "📋 查看所有交易所日志..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 $$exchange 日志:"; \
			(cd $$exchange && ./start.sh logs) || echo "❌ $$exchange 日志查看失败"; \
			echo ""; \
		fi; \
	done

# 清理所有编译文件
.PHONY: clean-all
clean-all:
	@echo "🧹 清理所有编译文件..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 清理 $$exchange..."; \
			(cd $$exchange && rm -f $$exchange-subscriber *.pid) || true; \
		fi; \
	done
	@echo "✅ 清理完成"

# 初始化所有依赖
.PHONY: init-all
init-all:
	@echo "🔧 初始化所有交易所依赖..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 初始化 $$exchange..."; \
			(cd $$exchange && go mod tidy) || echo "❌ $$exchange 初始化失败"; \
		fi; \
	done
	@echo "✅ 所有依赖初始化完成"

# 强制停止所有程序
.PHONY: force-stop-all
force-stop-all:
	@echo "💀 强制停止所有程序..."
	@killall binance-subscriber 2>/dev/null || true
	@killall bitget-subscriber 2>/dev/null || true
	@killall bybit-subscriber 2>/dev/null || true
	@killall okx-subscriber 2>/dev/null || true
	@killall hyperliquid-subscriber 2>/dev/null || true
	@killall gateio-subscriber 2>/dev/null || true
	@echo "✅ 强制停止完成"

# 单个交易所的构建规则
.PHONY: $(addprefix build-,$(EXCHANGES))
$(addprefix build-,$(EXCHANGES)):
	@exchange=$(patsubst build-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "🔨 编译 $$exchange..."; \
		(cd $$exchange && go mod tidy && go build -o $$exchange-subscriber .) && echo "✅ $$exchange 编译成功" || echo "❌ $$exchange 编译失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 单个交易所的启动规则
.PHONY: $(addprefix start-,$(EXCHANGES))
$(addprefix start-,$(EXCHANGES)):
	@exchange=$(patsubst start-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "🚀 启动 $$exchange..."; \
		(cd $$exchange && ./start.sh start) && echo "✅ $$exchange 启动成功" || echo "❌ $$exchange 启动失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 单个交易所的停止规则
.PHONY: $(addprefix stop-,$(EXCHANGES))
$(addprefix stop-,$(EXCHANGES)):
	@exchange=$(patsubst stop-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "🛑 停止 $$exchange..."; \
		(cd $$exchange && ./start.sh stop) && echo "✅ $$exchange 停止成功" || echo "❌ $$exchange 停止失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 单个交易所的重启规则
.PHONY: $(addprefix restart-,$(EXCHANGES))
$(addprefix restart-,$(EXCHANGES)):
	@exchange=$(patsubst restart-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "🔄 重启 $$exchange..."; \
		(cd $$exchange && ./start.sh restart) && echo "✅ $$exchange 重启成功" || echo "❌ $$exchange 重启失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 单个交易所的状态规则
.PHONY: $(addprefix status-,$(EXCHANGES))
$(addprefix status-,$(EXCHANGES)):
	@exchange=$(patsubst status-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "📊 $$exchange 状态:"; \
		(cd $$exchange && ./start.sh status) || echo "❌ $$exchange 状态检查失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 单个交易所的日志规则
.PHONY: $(addprefix logs-,$(EXCHANGES))
$(addprefix logs-,$(EXCHANGES)):
	@exchange=$(patsubst logs-%,%,$@); \
	if [ -d "$$exchange" ]; then \
		echo "📋 $$exchange 日志:"; \
		(cd $$exchange && ./start.sh logs) || echo "❌ $$exchange 日志查看失败"; \
	else \
		echo "❌ 目录不存在: $$exchange"; \
	fi

# 运行所有测试
.PHONY: test-all
test-all:
	@echo "🧪 运行所有测试..."
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "📈 测试 $$exchange..."; \
			(cd $$exchange && go test -v ./...) || echo "❌ $$exchange 测试失败"; \
		fi; \
	done
	@echo "✅ 所有测试完成"

# 显示项目信息
.PHONY: info
info:
	@echo "📊 项目信息:"
	@echo "  项目名称: 加密货币交易所数据订阅程序集合"
	@echo "  支持交易所: $(EXCHANGES)"
	@echo "  总交易所数量: $(words $(EXCHANGES))"
	@echo ""
	@echo "📁 目录结构:"
	@for exchange in $(EXCHANGES); do \
		if [ -d "$$exchange" ]; then \
			echo "  ✅ $$exchange/"; \
		else \
			echo "  ❌ $$exchange/ (不存在)"; \
		fi; \
	done 