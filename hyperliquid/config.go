package main

import (
	"encoding/json"
	"fmt"
	"os"
	"time"
)

type KafkaTopics struct {
	Trades     string `json:"trades"`
	Orderbooks string `json:"orderbooks"`
	Funding    string `json:"funding"`
}

type KafkaConfig struct {
	Enabled          bool        `json:"enabled"`
	BootstrapServers string      `json:"bootstrap_servers"`
	ClientID         string      `json:"client_id"`
	Acks             string      `json:"acks"`
	Retries          int         `json:"retries"`
	RetryBackoffMs   int         `json:"retry_backoff_ms"`
	CompressionType  string      `json:"compression_type"`
	BatchSize        int         `json:"batch_size"`
	LingerMs         int         `json:"linger_ms"`
	SecurityProtocol string      `json:"security_protocol"`
	SaslMechanism    string      `json:"sasl_mechanism"`
	SaslUsername     string      `json:"sasl_username"`
	SaslPassword     string      `json:"sasl_password"`
	Topics           KafkaTopics `json:"topics"`
}

type Config struct {
	Exchange       string      `json:"exchange"`
	WebSocketURL   string      `json:"websocket_url"`
	RestAPIURL     string      `json:"rest_api_url"`
	ReconnectDelay string      `json:"reconnect_delay"`
	PingInterval   string      `json:"ping_interval"`
	LogFile        string      `json:"log_file"`
	LogLevel       string      `json:"log_level"`
	Kafka          KafkaConfig `json:"kafka"`

	// 运行时解析的时间字段
	reconnectDelay time.Duration
	pingInterval   time.Duration
}

func loadConfig(filename string) (*Config, error) {
	// 默认配置
	config := &Config{
		Exchange:       "hyperliquid",
		WebSocketURL:   "wss://api.hyperliquid.xyz/ws",
		RestAPIURL:     "https://api.hyperliquid.xyz",
		ReconnectDelay: "5s",
		PingInterval:   "20s",
		LogFile:        "logs/hyperliquid.log",
		LogLevel:       "info",
	}

	// 尝试加载配置文件
	if _, err := os.Stat(filename); err == nil {
		file, err := os.Open(filename)
		if err != nil {
			return config, fmt.Errorf("无法打开配置文件: %v", err)
		}
		defer file.Close()

		decoder := json.NewDecoder(file)
		if err := decoder.Decode(config); err != nil {
			return config, fmt.Errorf("配置文件解析失败: %v", err)
		}
	}

	// 解析时间字段
	if err := config.parseDurations(); err != nil {
		return config, err
	}

	return config, nil
}

func (c *Config) parseDurations() error {
	var err error

	c.reconnectDelay, err = time.ParseDuration(c.ReconnectDelay)
	if err != nil {
		return fmt.Errorf("解析重连延迟失败: %v", err)
	}

	c.pingInterval, err = time.ParseDuration(c.PingInterval)
	if err != nil {
		return fmt.Errorf("解析心跳间隔失败: %v", err)
	}

	return nil
}

func (c *Config) GetReconnectDelay() time.Duration {
	return c.reconnectDelay
}

func (c *Config) GetPingInterval() time.Duration {
	return c.pingInterval
}
