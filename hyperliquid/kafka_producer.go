package main

import (
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"sync/atomic"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

type KafkaProducer struct {
	producer *kafka.Producer
	config   *KafkaConfig
	enabled  bool

	// 统计计数器
	tradeMessageCount     int64
	orderbookMessageCount int64
	fundingMessageCount   int64
	totalMessageCount     int64
	errorCount            int64
}

type KafkaMessage struct {
	Exchange  string      `json:"exchange"`
	Symbol    string      `json:"symbol"`
	Type      string      `json:"type"`
	Timestamp int64       `json:"timestamp"`
	Data      interface{} `json:"data"`
}

func NewKafkaProducer(config *KafkaConfig) (*KafkaProducer, error) {
	if !config.Enabled {
		log.Printf("📤 [Kafka] Kafka功能已禁用")
		return &KafkaProducer{enabled: false}, nil
	}

	// 构建Kafka配置
	kafkaConfig := &kafka.ConfigMap{
		"bootstrap.servers": config.BootstrapServers,
		"client.id":         config.ClientID,
		"acks":              config.Acks,
		"retries":           config.Retries,
		"retry.backoff.ms":  config.RetryBackoffMs,
		"compression.type":  config.CompressionType,
		"batch.size":        config.BatchSize,
		"linger.ms":         config.LingerMs,
		"security.protocol": config.SecurityProtocol,
		"sasl.mechanism":    config.SaslMechanism,
		"sasl.username":     config.SaslUsername,
		"sasl.password":     config.SaslPassword,
	}

	producer, err := kafka.NewProducer(kafkaConfig)
	if err != nil {
		return nil, fmt.Errorf("创建Kafka生产者失败: %v", err)
	}

	kp := &KafkaProducer{
		producer: producer,
		config:   config,
		enabled:  true,
	}

	// 启动事件处理goroutine
	go kp.handleEvents()

	log.Printf("✅ [Kafka] Kafka生产者初始化成功，服务器: %s", config.BootstrapServers)
	return kp, nil
}

func (kp *KafkaProducer) handleEvents() {
	if !kp.enabled {
		return
	}

	for e := range kp.producer.Events() {
		switch ev := e.(type) {
		case *kafka.Message:
			if ev.TopicPartition.Error != nil {
				atomic.AddInt64(&kp.errorCount, 1)
				log.Printf("❌ [Kafka] 消息发送失败: %v", ev.TopicPartition.Error)
			}
		case kafka.Error:
			atomic.AddInt64(&kp.errorCount, 1)
			log.Printf("❌ [Kafka] 生产者错误: %v", ev)
		}
	}
}

func (kp *KafkaProducer) SendTradeMessage(symbol string, data interface{}) error {
	if !kp.enabled {
		return nil
	}

	message := KafkaMessage{
		Exchange:  "hyperliquid",
		Symbol:    symbol,
		Type:      "trade",
		Timestamp: time.Now().UnixMilli(),
		Data:      data,
	}

	err := kp.sendMessage(kp.config.Topics.Trades, message)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		return err
	}

	atomic.AddInt64(&kp.tradeMessageCount, 1)
	atomic.AddInt64(&kp.totalMessageCount, 1)
	return nil
}

func (kp *KafkaProducer) SendOrderbookMessage(symbol string, data interface{}) error {
	if !kp.enabled {
		return nil
	}

	message := KafkaMessage{
		Exchange:  "hyperliquid",
		Symbol:    symbol,
		Type:      "orderbook",
		Timestamp: time.Now().UnixMilli(),
		Data:      data,
	}

	err := kp.sendMessage(kp.config.Topics.Orderbooks, message)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		return err
	}

	atomic.AddInt64(&kp.orderbookMessageCount, 1)
	atomic.AddInt64(&kp.totalMessageCount, 1)
	return nil
}

func (kp *KafkaProducer) SendFundingMessage(symbol string, data interface{}) error {
	if !kp.enabled {
		return nil
	}

	message := KafkaMessage{
		Exchange:  "hyperliquid",
		Symbol:    symbol,
		Type:      "funding",
		Timestamp: time.Now().UnixMilli(),
		Data:      data,
	}

	err := kp.sendMessage(kp.config.Topics.Funding, message)
	if err != nil {
		atomic.AddInt64(&kp.errorCount, 1)
		return err
	}

	atomic.AddInt64(&kp.fundingMessageCount, 1)
	atomic.AddInt64(&kp.totalMessageCount, 1)
	return nil
}

func (kp *KafkaProducer) sendMessage(topic string, message KafkaMessage) error {
	if !kp.enabled {
		return nil
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	kafkaMessage := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Topic:     &topic,
			Partition: kafka.PartitionAny,
		},
		Key:   []byte(message.Symbol),
		Value: jsonData,
		Headers: []kafka.Header{
			{Key: "exchange", Value: []byte(message.Exchange)},
			{Key: "type", Value: []byte(message.Type)},
			{Key: "timestamp", Value: []byte(strconv.FormatInt(message.Timestamp, 10))},
		},
	}

	err = kp.producer.Produce(kafkaMessage, nil)
	if err != nil {
		return fmt.Errorf("发送Kafka消息失败: %v", err)
	}

	return nil
}

func (kp *KafkaProducer) Close() {
	if !kp.enabled || kp.producer == nil {
		return
	}

	log.Printf("🔄 [Kafka] 正在关闭Kafka生产者...")

	// 等待所有消息发送完成
	kp.producer.Flush(5000) // 等待5秒

	kp.producer.Close()
	log.Printf("✅ [Kafka] Kafka生产者已关闭")
}

func (kp *KafkaProducer) GetStats() map[string]interface{} {
	if !kp.enabled || kp.producer == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	return map[string]interface{}{
		"enabled": true,
		"topics": map[string]string{
			"trades":     kp.config.Topics.Trades,
			"orderbooks": kp.config.Topics.Orderbooks,
			"funding":    kp.config.Topics.Funding,
		},
		"bootstrap_servers": kp.config.BootstrapServers,
		"statistics": map[string]int64{
			"trade_messages":     atomic.LoadInt64(&kp.tradeMessageCount),
			"orderbook_messages": atomic.LoadInt64(&kp.orderbookMessageCount),
			"funding_messages":   atomic.LoadInt64(&kp.fundingMessageCount),
			"total_messages":     atomic.LoadInt64(&kp.totalMessageCount),
			"error_count":        atomic.LoadInt64(&kp.errorCount),
		},
	}
}

// GetKafkaStats 获取Kafka统计信息
func (kp *KafkaProducer) GetKafkaStats() (int64, int64, int64, int64, int64) {
	if !kp.enabled {
		return 0, 0, 0, 0, 0
	}

	return atomic.LoadInt64(&kp.tradeMessageCount),
		atomic.LoadInt64(&kp.orderbookMessageCount),
		atomic.LoadInt64(&kp.fundingMessageCount),
		atomic.LoadInt64(&kp.totalMessageCount),
		atomic.LoadInt64(&kp.errorCount)
}
