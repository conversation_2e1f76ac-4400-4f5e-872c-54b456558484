package main

import (
	"time"

	"github.com/tidwall/gjson"
)

// HyperliquidTradeData 交易数据结构
type HyperliquidTradeData struct {
	Symbol    string `json:"symbol"`
	Price     string `json:"price"`
	Quantity  string `json:"quantity"`
	Side      string `json:"side"`
	Timestamp int64  `json:"timestamp"`
	TradeID   int64  `json:"trade_id"`
	Hash      string `json:"hash"`
}

// HyperliquidOrderbookData 盘口数据结构
type HyperliquidOrderbookData struct {
	Symbol       string `json:"symbol"`
	BestBidPrice string `json:"best_bid_price"`
	BestBidSize  string `json:"best_bid_size"`
	BestAskPrice string `json:"best_ask_price"`
	BestAskSize  string `json:"best_ask_size"`
	Timestamp    int64  `json:"timestamp"`
}

// DataHandler 数据处理器接口
type DataHandler interface {
	HandleTradeMessage(message []byte) error
	HandleOrderbookMessage(message []byte) error
	HandleFundingMessage(message []byte) error
}

// HyperliquidDataHandler Hyperliquid数据处理器
type HyperliquidDataHandler struct {
	tradeCount     int64
	orderbookCount int64
	fundingCount   int64
	lastLogTime    time.Time
}

// NewHyperliquidDataHandler 创建新的数据处理器
func NewHyperliquidDataHandler() *HyperliquidDataHandler {
	return &HyperliquidDataHandler{
		lastLogTime: time.Now(),
	}
}

// HandleTradeMessage 处理交易数据
func (h *HyperliquidDataHandler) HandleTradeMessage(message []byte) error {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()
	if channel != "trades" {
		return nil
	}

	// 解析数据
	dataArray := gjson.GetBytes(message, "data").Array()
	if len(dataArray) == 0 {
		return nil
	}

	// 获取交易对信息
	symbol := gjson.GetBytes(message, "data.0.coin").String()
	if symbol == "" {
		return nil
	}

	for _, data := range dataArray {
		h.tradeCount++

		_ = HyperliquidTradeData{
			Symbol:    symbol,
			Price:     data.Get("px").String(),
			Quantity:  data.Get("sz").String(),
			Side:      data.Get("side").String(),
			Timestamp: data.Get("time").Int(),
			TradeID:   data.Get("tid").Int(),
			Hash:      data.Get("hash").String(),
		}

		// 记录统计日志
		h.logStatistics()
	}

	return nil
}

// HandleOrderbookMessage 处理盘口数据
func (h *HyperliquidDataHandler) HandleOrderbookMessage(message []byte) error {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()
	if channel != "l2Book" {
		return nil
	}

	// 解析数据
	data := gjson.GetBytes(message, "data")
	symbol := data.Get("coin").String()
	if symbol == "" {
		return nil
	}

	h.orderbookCount++

	// 解析买卖盘数据
	levels := data.Get("levels").Array()
	if len(levels) >= 2 {
		bids := levels[0].Array()
		asks := levels[1].Array()

		var bestBidPrice, bestBidSize, bestAskPrice, bestAskSize string

		if len(bids) > 0 {
			bestBidPrice = bids[0].Get("px").String()
			bestBidSize = bids[0].Get("sz").String()
		}

		if len(asks) > 0 {
			bestAskPrice = asks[0].Get("px").String()
			bestAskSize = asks[0].Get("sz").String()
		}

		_ = HyperliquidOrderbookData{
			Symbol:       symbol,
			BestBidPrice: bestBidPrice,
			BestBidSize:  bestBidSize,
			BestAskPrice: bestAskPrice,
			BestAskSize:  bestAskSize,
			Timestamp:    data.Get("time").Int(),
		}
	}

	// 记录统计日志
	h.logStatistics()

	return nil
}

// HandleFundingMessage 处理资金费率数据
func (h *HyperliquidDataHandler) HandleFundingMessage(message []byte) error {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()
	if channel != "funding" {
		return nil
	}

	// 解析数据
	data := gjson.GetBytes(message, "data")
	symbol := data.Get("coin").String()
	if symbol == "" {
		return nil
	}

	h.fundingCount++

	// 解析资金费率数据
	fundingRate := data.Get("funding").String()
	nextFundingTime := data.Get("nextFunding").Int()

	// 不再输出详细的资金费率数据，统一在全局统计中显示
	_ = fundingRate
	_ = nextFundingTime

	// 记录统计日志
	h.logStatistics()

	return nil
}

// logStatistics 记录统计日志 - 移除详细统计，统一在全局统计中显示
func (h *HyperliquidDataHandler) logStatistics() {
	// 不再输出详细的数据统计，统一在main.go的全局统计中显示
}
