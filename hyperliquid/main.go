package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
)

// ConnectionState 连接状态
type ConnectionState int

const (
	StateDisconnected ConnectionState = iota
	StateConnecting
	StateConnected
	StateReconnecting
	StateStopped
)

func (s ConnectionState) String() string {
	switch s {
	case StateDisconnected:
		return "Disconnected"
	case StateConnecting:
		return "Connecting"
	case StateConnected:
		return "Connected"
	case StateReconnecting:
		return "Reconnecting"
	case StateStopped:
		return "Stopped"
	default:
		return "Unknown"
	}
}

// makeHTTPRequest 发送HTTP请求
func makeHTTPRequest(url string) (*http.Response, error) {
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	return client.Get(url)
}

// HyperliquidClient Hyperliquid客户端
type HyperliquidClient struct {
	config      *Config
	dataHandler DataHandler
	symbols     []string
	ctx         context.Context
	cancel      context.CancelFunc
	wg          sync.WaitGroup

	// 数据管理器
	tradeManager     *TradeDataManager
	orderbookManager *OrderbookDataManager
	fundingManager   *FundingDataManager

	// Kafka生产者
	kafkaProducer *KafkaProducer

	// 全局统计
	totalTradeCount     int64
	totalOrderbookCount int64
	totalFundingCount   int64
	startTime           time.Time
}

// TradeDataManager 交易数据管理器
type TradeDataManager struct {
	client      *HyperliquidClient
	connections map[string]*websocket.Conn
	connMutex   sync.RWMutex
	tradeCount  int64
}

// OrderbookDataManager 盘口数据管理器
type OrderbookDataManager struct {
	client         *HyperliquidClient
	connections    map[string]*websocket.Conn
	connMutex      sync.RWMutex
	orderbookCount int64
}

// FundingDataManager 资金费率数据管理器
type FundingDataManager struct {
	client       *HyperliquidClient
	connections  map[string]*websocket.Conn
	connMutex    sync.RWMutex
	fundingCount int64
}

// NewHyperliquidClient 创建Hyperliquid客户端
func NewHyperliquidClient(config *Config, dataHandler DataHandler) *HyperliquidClient {
	ctx, cancel := context.WithCancel(context.Background())

	client := &HyperliquidClient{
		config:      config,
		dataHandler: dataHandler,
		ctx:         ctx,
		cancel:      cancel,
		startTime:   time.Now(),
	}

	client.tradeManager = &TradeDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.orderbookManager = &OrderbookDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	client.fundingManager = &FundingDataManager{
		client:      client,
		connections: make(map[string]*websocket.Conn),
	}

	// 初始化Kafka生产者
	kafkaProducer, err := NewKafkaProducer(&config.Kafka)
	if err != nil {
		log.Printf("⚠️ [Kafka] Kafka生产者初始化失败: %v", err)
		// 不中断程序运行，只是禁用Kafka功能
		kafkaProducer = &KafkaProducer{enabled: false}
	}
	client.kafkaProducer = kafkaProducer

	return client
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetAllSymbols 获取所有活跃交易对
func (c *HyperliquidClient) GetAllSymbols() error {
	symbols, err := getActiveSymbols(c.config)
	if err != nil {
		return err
	}
	c.symbols = symbols
	return nil
}

// SubscribeData 订阅数据
func (c *HyperliquidClient) SubscribeData() error {
	log.Printf("🚀 开始分批订阅交易数据和盘口数据...")

	batchSize := 25 // Hyperliquid建议每批25个交易对
	totalBatches := (len(c.symbols) + batchSize - 1) / batchSize

	log.Printf("📊 分批订阅策略 - 总交易对: %d, 批次大小: %d, 总批次: %d",
		len(c.symbols), batchSize, totalBatches)

	batchCount := 0

	for i := 0; i < len(c.symbols); i += batchSize {
		end := i + batchSize
		if end > len(c.symbols) {
			end = len(c.symbols)
		}

		batch := c.symbols[i:end]
		batchCount++

		log.Printf("📡 处理批次 %d/%d - 交易对数量: %d", batchCount, totalBatches, len(batch))

		// 为每批创建三个连接：交易数据、盘口数据、资金费率数据
		tradeConnName := fmt.Sprintf("trade_batch_%d", batchCount)
		orderbookConnName := fmt.Sprintf("orderbook_batch_%d", batchCount)
		fundingConnName := fmt.Sprintf("funding_batch_%d", batchCount)

		// 订阅交易数据
		log.Printf("🔗 启动交易数据连接: %s", tradeConnName)
		c.wg.Add(1)
		go c.tradeManager.subscribeTradeData(tradeConnName, batch)

		// 等待一点时间再订阅盘口数据
		time.Sleep(200 * time.Millisecond)

		// 订阅盘口数据
		log.Printf("🔗 启动盘口数据连接: %s", orderbookConnName)
		c.wg.Add(1)
		go c.orderbookManager.subscribeOrderbookData(orderbookConnName, batch)

		// 等待一点时间再订阅资金费率数据
		time.Sleep(200 * time.Millisecond)

		// 订阅资金费率数据
		log.Printf("🔗 启动资金费率连接: %s", fundingConnName)
		c.wg.Add(1)
		go c.fundingManager.subscribeFundingData(fundingConnName, batch)

		// 批次间延迟，避免同时创建太多连接
		if batchCount < totalBatches {
			log.Printf("⏰ 批次 %d 完成，等待 1 秒后继续下一批次...", batchCount)
			time.Sleep(1 * time.Second)
		}
	}

	log.Printf("✅ 所有分批订阅已启动，共创建 %d 批连接 (%d 个连接)", totalBatches, totalBatches*3)

	// 启动全局统计
	c.wg.Add(1)
	go c.printGlobalStatistics()

	// 启动连接监控
	c.wg.Add(1)
	go c.monitorConnections()

	return nil
}

// subscribeTradeData 订阅交易数据
func (tm *TradeDataManager) subscribeTradeData(connName string, symbols []string) {
	defer tm.client.wg.Done()

	// 构建交易数据订阅参数
	subscriptions := make([]map[string]interface{}, len(symbols))
	for i, symbol := range symbols {
		subscriptions[i] = map[string]interface{}{
			"type": "trades",
			"coin": symbol,
		}
	}

	tm.subscribeWithRetry(connName, subscriptions, tm.handleTradeMessage)
}

// subscribeOrderbookData 订阅盘口数据
func (om *OrderbookDataManager) subscribeOrderbookData(connName string, symbols []string) {
	defer om.client.wg.Done()

	// 构建盘口数据订阅参数
	subscriptions := make([]map[string]interface{}, len(symbols))
	for i, symbol := range symbols {
		subscriptions[i] = map[string]interface{}{
			"type": "l2Book",
			"coin": symbol,
		}
	}

	om.subscribeWithRetry(connName, subscriptions, om.handleOrderbookMessage)
}

// subscribeFundingData 订阅资金费率数据
func (fm *FundingDataManager) subscribeFundingData(connName string, symbols []string) {
	defer fm.client.wg.Done()

	// 根据Hyperliquid官方文档，使用activeAssetCtx订阅获取资金费率信息
	subscriptions := make([]map[string]interface{}, len(symbols))
	for i, symbol := range symbols {
		subscriptions[i] = map[string]interface{}{
			"type": "activeAssetCtx",
			"coin": symbol,
		}
	}

	log.Printf("💰 [Hyperliquid] 资金费率订阅使用activeAssetCtx，交易对数量: %d", len(symbols))
	fm.subscribeWithRetry(connName, subscriptions, fm.handleFundingMessage)
}

// subscribeWithRetry 通用订阅方法（带重连机制）
func (tm *TradeDataManager) subscribeWithRetry(connName string, subscriptions []map[string]interface{}, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := tm.client.config.GetReconnectDelay()

	for {
		select {
		case <-tm.client.ctx.Done():
			log.Printf("🛑 交易数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if tm.connectAndListen(connName, subscriptions, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 交易数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 交易数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-tm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 盘口数据版本
func (om *OrderbookDataManager) subscribeWithRetry(connName string, subscriptions []map[string]interface{}, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := om.client.config.GetReconnectDelay()

	for {
		select {
		case <-om.client.ctx.Done():
			log.Printf("🛑 盘口数据连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if om.connectAndListen(connName, subscriptions, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 盘口数据连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 盘口数据连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-om.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// subscribeWithRetry 资金费率数据版本
func (fm *FundingDataManager) subscribeWithRetry(connName string, subscriptions []map[string]interface{}, handler func([]byte)) {
	reconnectAttempts := 0
	currentDelay := fm.client.config.GetReconnectDelay()

	for {
		select {
		case <-fm.client.ctx.Done():
			log.Printf("🛑 资金费率连接 %s 收到停止信号", connName)
			return
		default:
			// 尝试建立连接
			if fm.connectAndListen(connName, subscriptions, handler, &reconnectAttempts) {
				return
			}

			// 连接失败，准备重连
			reconnectAttempts++
			if reconnectAttempts >= 10 {
				log.Printf("❌ 资金费率连接 %s 重连次数超过限制，停止重连", connName)
				return
			}

			log.Printf("⚠️ 资金费率连接 %s 第 %d 次重连，等待 %v 后重试...", connName, reconnectAttempts, currentDelay)

			select {
			case <-fm.client.ctx.Done():
				return
			case <-time.After(currentDelay):
				// 继续重连
			}
		}
	}
}

// connectAndListen 交易数据连接和监听
func (tm *TradeDataManager) connectAndListen(connName string, subscriptions []map[string]interface{}, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接交易数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(subscriptions), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(tm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 交易数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	tm.connMutex.Lock()
	tm.connections[connName] = conn
	tm.connMutex.Unlock()

	defer func() {
		tm.connMutex.Lock()
		delete(tm.connections, connName)
		tm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 交易数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 交易数据连接 %s 建立成功", connName)

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"method": "subscribe",
		"subscription": map[string]interface{}{
			"type": "allMids",
		},
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送交易数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	// 分别订阅每个交易对的交易数据
	for _, sub := range subscriptions {
		subscribeMsg := map[string]interface{}{
			"method":       "subscribe",
			"subscription": sub,
		}

		if err := conn.WriteJSON(subscribeMsg); err != nil {
			log.Printf("❌ 发送交易数据订阅消息失败 %s: %v", connName, err)
			return false
		}

		time.Sleep(50 * time.Millisecond)
	}

	log.Printf("📡 交易数据订阅消息已发送 %s", connName)

	// 启动心跳
	go tm.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-tm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 交易数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// connectAndListen 盘口数据连接和监听
func (om *OrderbookDataManager) connectAndListen(connName string, subscriptions []map[string]interface{}, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接盘口数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(subscriptions), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(om.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 盘口数据WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	om.connMutex.Lock()
	om.connections[connName] = conn
	om.connMutex.Unlock()

	defer func() {
		om.connMutex.Lock()
		delete(om.connections, connName)
		om.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 盘口数据连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 盘口数据连接 %s 建立成功", connName)

	// 发送订阅消息
	subscribeMsg := map[string]interface{}{
		"method": "subscribe",
		"subscription": map[string]interface{}{
			"type": "allMids",
		},
	}

	if err := conn.WriteJSON(subscribeMsg); err != nil {
		log.Printf("❌ 发送盘口数据订阅消息失败 %s: %v", connName, err)
		return false
	}

	// 分别订阅每个交易对的盘口数据
	for _, sub := range subscriptions {
		subscribeMsg := map[string]interface{}{
			"method":       "subscribe",
			"subscription": sub,
		}

		if err := conn.WriteJSON(subscribeMsg); err != nil {
			log.Printf("❌ 发送盘口数据订阅消息失败 %s: %v", connName, err)
			return false
		}

		time.Sleep(50 * time.Millisecond)
	}

	log.Printf("📡 盘口数据订阅消息已发送 %s", connName)

	// 启动心跳
	go om.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-om.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 盘口数据读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// connectAndListen 资金费率数据连接和监听
func (fm *FundingDataManager) connectAndListen(connName string, subscriptions []map[string]interface{}, handler func([]byte), reconnectAttempts *int) bool {
	log.Printf("🔗 正在连接资金费率数据 %s，订阅 %d 个交易对 (尝试 %d)", connName, len(subscriptions), *reconnectAttempts+1)

	// 建立WebSocket连接
	conn, _, err := websocket.DefaultDialer.Dial(fm.client.config.WebSocketURL, nil)
	if err != nil {
		log.Printf("❌ 资金费率WebSocket连接失败 %s: %v", connName, err)
		return false
	}

	// 重置重连计数器
	*reconnectAttempts = 0

	fm.connMutex.Lock()
	fm.connections[connName] = conn
	fm.connMutex.Unlock()

	defer func() {
		fm.connMutex.Lock()
		delete(fm.connections, connName)
		fm.connMutex.Unlock()
		conn.Close()
		log.Printf("🔌 资金费率连接 %s 已关闭", connName)
	}()

	log.Printf("✅ 资金费率连接 %s 建立成功", connName)

	// 分别订阅每个交易对的资金费率数据
	for i, sub := range subscriptions {
		subscribeMsg := map[string]interface{}{
			"method":       "subscribe",
			"subscription": sub,
		}

		// 只在第一个和每10个订阅时输出日志
		if i == 0 || (i+1)%10 == 0 || i == len(subscriptions)-1 {
			log.Printf("📤 [Hyperliquid] 发送资金费率订阅 %d/%d: coin=%s", i+1, len(subscriptions), sub["coin"])
		}

		if err := conn.WriteJSON(subscribeMsg); err != nil {
			log.Printf("❌ 发送资金费率订阅消息失败 %s: %v", connName, err)
			return false
		}

		time.Sleep(50 * time.Millisecond)
	}

	log.Printf("📡 资金费率订阅消息已发送 %s", connName)

	// 启动心跳
	go fm.startPing(conn, connName)

	// 监听消息
	for {
		select {
		case <-fm.client.ctx.Done():
			return true
		default:
			conn.SetReadDeadline(time.Now().Add(5 * time.Minute))
			_, message, err := conn.ReadMessage()
			if err != nil {
				log.Printf("❌ 资金费率读取消息失败 %s: %v", connName, err)
				return false
			}

			handler(message)
		}
	}
}

// startPing 交易数据心跳
func (tm *TradeDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(tm.client.config.GetPingInterval())
	defer ticker.Stop()

	// 连接质量监控
	healthTicker := time.NewTicker(tm.client.config.GetPingInterval() / 2)
	defer healthTicker.Stop()

	lastPongTime := time.Now()
	consecutiveTimeouts := 0

	// 设置pong处理器
	conn.SetPongHandler(func(appData string) error {
		lastPongTime = time.Now()
		consecutiveTimeouts = 0
		log.Printf("💓 [Hyperliquid] 交易数据连接 %s 收到pong响应", connName)
		return nil
	})

	// 设置ping处理器（被动响应服务器ping）
	conn.SetPingHandler(func(appData string) error {
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
		if err := conn.WriteMessage(websocket.PongMessage, []byte(appData)); err != nil {
			log.Printf("💔 [Hyperliquid] 交易数据连接 %s pong响应失败: %v", connName, err)
			return err
		}
		log.Printf("💓 [Hyperliquid] 交易数据连接 %s 响应服务器ping", connName)
		return nil
	})

	for {
		select {
		case <-tm.client.ctx.Done():
			return
		case <-ticker.C:
			// 发送WebSocket ping帧（主动ping）
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, []byte("hyperliquid-trade-ping")); err != nil {
				log.Printf("💔 [Hyperliquid] 交易数据WebSocket ping失败 %s: %v", connName, err)
				return
			}
			log.Printf("💓 [Hyperliquid] 交易数据连接 %s 发送增强ping", connName)

		case <-healthTicker.C:
			// 连接健康检查
			pongDelay := time.Since(lastPongTime)
			maxPongDelay := tm.client.config.GetPingInterval() * 8

			if pongDelay > maxPongDelay {
				consecutiveTimeouts++
				log.Printf("⚠️ [Hyperliquid] 交易数据连接 %s pong超时: %.1fs (第%d次)",
					connName, pongDelay.Seconds(), consecutiveTimeouts)

				if consecutiveTimeouts >= 3 {
					log.Printf("💔 [Hyperliquid] 交易数据连接 %s 连续超时，关闭连接", connName)
					return
				}
			}
		}
	}
}

// startPing 盘口数据心跳
func (om *OrderbookDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(om.client.config.GetPingInterval())
	defer ticker.Stop()

	for {
		select {
		case <-om.client.ctx.Done():
			return
		case <-ticker.C:
			// Hyperliquid使用WebSocket ping帧
			if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				log.Printf("💔 盘口数据发送心跳失败 %s: %v", connName, err)
				return
			}
		}
	}
}

// startPing 资金费率数据心跳
func (fm *FundingDataManager) startPing(conn *websocket.Conn, connName string) {
	ticker := time.NewTicker(fm.client.config.GetPingInterval())
	defer ticker.Stop()

	for {
		select {
		case <-fm.client.ctx.Done():
			return
		case <-ticker.C:
			// Hyperliquid使用WebSocket ping帧
			if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				log.Printf("💔 资金费率发送心跳失败 %s: %v", connName, err)
				return
			}
		}
	}
}

// handleFundingMessage 处理资金费率数据
func (fm *FundingDataManager) handleFundingMessage(message []byte) {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()

	// 检查是否是activeAssetCtx或activeSpotAssetCtx频道的消息
	if channel == "activeAssetCtx" || channel == "activeSpotAssetCtx" {
		// 检查是否有实际的数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() {
			// 提取资金费率信息
			// 对于永续合约，检查ctx.funding字段
			if channel == "activeAssetCtx" {
				ctx := gjson.Get(data.Raw, "ctx")
				coin := gjson.Get(data.Raw, "coin").String()
				if ctx.Exists() {
					funding := gjson.Get(ctx.Raw, "funding")
					// 如果有资金费率信息，则计数
					if funding.Exists() {
						atomic.AddInt64(&fm.fundingCount, 1)
						atomic.AddInt64(&fm.client.totalFundingCount, 1)

						// 发送到Kafka
						if coin != "" {
							if err := fm.client.kafkaProducer.SendFundingMessage(coin, data.Value()); err != nil {
								log.Printf("⚠️ [Kafka] 发送资金费率数据失败: %v", err)
							}
						}

						// 不再输出详细的资金费率信息，统一在全局统计中显示
					}
				}

			} else if channel == "activeSpotAssetCtx" {
				// 对于现货资产，也计数
				atomic.AddInt64(&fm.fundingCount, 1)
				atomic.AddInt64(&fm.client.totalFundingCount, 1)

				// 发送到Kafka
				coin := gjson.Get(data.Raw, "coin").String()
				if coin != "" {
					if err := fm.client.kafkaProducer.SendFundingMessage(coin, data.Value()); err != nil {
						log.Printf("⚠️ [Kafka] 发送现货资产数据失败: %v", err)
					}
				}

				// 不再输出详细的现货资产信息，统一在全局统计中显示
			}

			// 调用数据处理器
			fm.client.dataHandler.HandleFundingMessage(message)
		}
	} else if channel == "subscriptionResponse" {
		// 订阅确认，检查是否成功
		data := gjson.GetBytes(message, "data")
		if data.Exists() {
			subscriptionType := gjson.Get(data.Raw, "type").String()
			coin := gjson.Get(data.Raw, "coin").String()
			log.Printf("✅ [Hyperliquid] 资金费率订阅确认 - type: %s, coin: %s", subscriptionType, coin)
		} else {
			log.Printf("✅ [Hyperliquid] 资金费率订阅确认")
		}
	} else {
		// 记录其他消息类型，但减少日志频率
		if channel != "" && channel != "allMids" {
			log.Printf("ℹ️ [Hyperliquid] 资金费率连接收到其他频道消息: %s", channel)
		}
	}
}

// handleTradeMessage 处理交易数据
func (tm *TradeDataManager) handleTradeMessage(message []byte) {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()

	if strings.Contains(channel, "trades") {
		// 检查是否有实际的交易数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() && data.IsArray() && len(data.Array()) > 0 {
			atomic.AddInt64(&tm.tradeCount, 1)
			atomic.AddInt64(&tm.client.totalTradeCount, 1)

			// 发送到Kafka
			trades := data.Array()
			for _, trade := range trades {
				coin := gjson.Get(trade.Raw, "coin").String()
				if coin != "" {
					if err := tm.client.kafkaProducer.SendTradeMessage(coin, trade.Value()); err != nil {
						log.Printf("⚠️ [Kafka] 发送交易数据失败: %v", err)
					}
				}
			}

			// 调用数据处理器
			tm.client.dataHandler.HandleTradeMessage(message)
		}
	}
}

// handleOrderbookMessage 处理盘口数据
func (om *OrderbookDataManager) handleOrderbookMessage(message []byte) {
	// 检查消息类型
	channel := gjson.GetBytes(message, "channel").String()

	if strings.Contains(channel, "l2Book") {
		// 检查是否有实际的盘口数据
		data := gjson.GetBytes(message, "data")
		if data.Exists() {
			atomic.AddInt64(&om.orderbookCount, 1)
			atomic.AddInt64(&om.client.totalOrderbookCount, 1)

			// 发送到Kafka
			coin := gjson.Get(data.Raw, "coin").String()
			if coin != "" {
				if err := om.client.kafkaProducer.SendOrderbookMessage(coin, data.Value()); err != nil {
					log.Printf("⚠️ [Kafka] 发送盘口数据失败: %v", err)
				}
			}

			// 调用数据处理器
			om.client.dataHandler.HandleOrderbookMessage(message)
		}
	}
}

// printGlobalStatistics 打印全局统计
func (c *HyperliquidClient) printGlobalStatistics() {
	defer c.wg.Done()

	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			runtime := time.Since(c.startTime)
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)

			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 24) / 25 // 每批25个交易对

			log.Printf("📊 [Hyperliquid] 全局统计 - 运行时间: %v, 交易连接: %d/%d, 盘口连接: %d/%d, 资金费率连接: %d/%d, 交易消息: %d, 盘口消息: %d, 资金费率消息: %d, 总消息: %d",
				runtime.Round(time.Second),
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				totalTrade,
				totalOrderbook,
				totalFunding,
				totalTrade+totalOrderbook+totalFunding,
			)

			// 数据流速率统计（每秒消息数）
			if runtime.Seconds() > 0 {
				tradeRate := float64(totalTrade) / runtime.Seconds()
				orderbookRate := float64(totalOrderbook) / runtime.Seconds()
				fundingRate := float64(totalFunding) / runtime.Seconds()
				log.Printf("📈 [Hyperliquid] 数据流速率 - 交易: %.1f msg/s, 盘口: %.1f msg/s, 资金费率: %.1f msg/s",
					tradeRate, orderbookRate, fundingRate)
			}

			// Kafka统计信息
			if c.kafkaProducer != nil {
				kafkaTradeCount, kafkaOrderbookCount, kafkaFundingCount, kafkaTotalCount, kafkaErrorCount := c.kafkaProducer.GetKafkaStats()
				if kafkaTotalCount > 0 || kafkaErrorCount > 0 {
					log.Printf("📤 [Kafka] 发送统计 - 交易: %d, 盘口: %d, 资金费率: %d, 总计: %d, 错误: %d",
						kafkaTradeCount, kafkaOrderbookCount, kafkaFundingCount, kafkaTotalCount, kafkaErrorCount)

					// Kafka发送速率统计
					if runtime.Seconds() > 0 {
						kafkaTradeRate := float64(kafkaTradeCount) / runtime.Seconds()
						kafkaOrderbookRate := float64(kafkaOrderbookCount) / runtime.Seconds()
						kafkaFundingRate := float64(kafkaFundingCount) / runtime.Seconds()
						kafkaTotalRate := float64(kafkaTotalCount) / runtime.Seconds()
						log.Printf("📈 [Kafka] 发送速率 - 交易: %.1f msg/s, 盘口: %.1f msg/s, 资金费率: %.1f msg/s, 总计: %.1f msg/s",
							kafkaTradeRate, kafkaOrderbookRate, kafkaFundingRate, kafkaTotalRate)
					}
				}
			}

			// WebSocket连接健康状态
			totalConnections := tradeConnections + orderbookConnections + fundingConnections
			expectedTotal := expectedConnections * 3
			if totalConnections == expectedTotal {
				log.Printf("✅ [Hyperliquid] 所有WebSocket连接健康")
			} else if totalConnections >= expectedTotal*2/3 {
				log.Printf("⚠️ [Hyperliquid] 部分WebSocket连接异常")
			} else {
				log.Printf("❌ [Hyperliquid] 大量WebSocket连接异常")
			}
		}
	}
}

// monitorConnections 监控连接状态
func (c *HyperliquidClient) monitorConnections() {
	defer c.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.tradeManager.connMutex.RLock()
			tradeConnections := len(c.tradeManager.connections)
			tradeConnNames := make([]string, 0, len(c.tradeManager.connections))
			for name := range c.tradeManager.connections {
				tradeConnNames = append(tradeConnNames, name)
			}
			c.tradeManager.connMutex.RUnlock()

			c.orderbookManager.connMutex.RLock()
			orderbookConnections := len(c.orderbookManager.connections)
			orderbookConnNames := make([]string, 0, len(c.orderbookManager.connections))
			for name := range c.orderbookManager.connections {
				orderbookConnNames = append(orderbookConnNames, name)
			}
			c.orderbookManager.connMutex.RUnlock()

			c.fundingManager.connMutex.RLock()
			fundingConnections := len(c.fundingManager.connections)
			fundingConnNames := make([]string, 0, len(c.fundingManager.connections))
			for name := range c.fundingManager.connections {
				fundingConnNames = append(fundingConnNames, name)
			}
			c.fundingManager.connMutex.RUnlock()

			expectedConnections := (len(c.symbols) + 24) / 25 // 每批25个交易对

			log.Printf("🔍 [Hyperliquid] 连接监控 - 交易连接: %d/%d, 盘口连接: %d/%d, 资金费率连接: %d/%d, 交易对数: %d, 批次大小: 25",
				tradeConnections, expectedConnections,
				orderbookConnections, expectedConnections,
				fundingConnections, expectedConnections,
				len(c.symbols))

			// 详细连接状态
			log.Printf("🔗 [Hyperliquid] 连接详情 - 交易连接: %v", tradeConnNames)
			log.Printf("🔗 [Hyperliquid] 连接详情 - 盘口连接: %v", orderbookConnNames)
			log.Printf("🔗 [Hyperliquid] 连接详情 - 资金费率连接: %v", fundingConnNames)

			// 检查连接健康状态
			if tradeConnections < expectedConnections/2 {
				log.Printf("⚠️ [Hyperliquid] 交易数据连接数异常：当前 %d，预期 %d", tradeConnections, expectedConnections)
			}
			if orderbookConnections < expectedConnections/2 {
				log.Printf("⚠️ [Hyperliquid] 盘口数据连接数异常：当前 %d，预期 %d", orderbookConnections, expectedConnections)
			}
			if fundingConnections < expectedConnections/2 {
				log.Printf("⚠️ [Hyperliquid] 资金费率连接数异常：当前 %d，预期 %d", fundingConnections, expectedConnections)
			}

			// 数据流健康检查
			totalTrade := atomic.LoadInt64(&c.totalTradeCount)
			totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
			totalFunding := atomic.LoadInt64(&c.totalFundingCount)
			totalConnections := tradeConnections + orderbookConnections + fundingConnections

			if totalConnections > 0 && totalTrade == 0 && totalOrderbook == 0 && totalFunding == 0 {
				log.Printf("⚠️ [Hyperliquid] 数据流异常：有连接但没有数据")
			}

			// 连接效率检查
			if totalConnections > 0 {
				avgTradePerConn := float64(totalTrade) / float64(tradeConnections)
				avgOrderbookPerConn := float64(totalOrderbook) / float64(orderbookConnections)
				avgFundingPerConn := float64(totalFunding) / float64(fundingConnections)
				log.Printf("📊 [Hyperliquid] 连接效率 - 平均交易消息/连接: %.1f, 平均盘口消息/连接: %.1f, 平均资金费率消息/连接: %.1f",
					avgTradePerConn, avgOrderbookPerConn, avgFundingPerConn)
			}
		}
	}
}

// Stop 停止客户端
func (c *HyperliquidClient) Stop() {
	log.Printf("🛑 正在停止Hyperliquid客户端...")
	c.cancel()

	// 关闭所有交易数据连接
	c.tradeManager.connMutex.Lock()
	for name, conn := range c.tradeManager.connections {
		log.Printf("🔌 关闭交易数据连接: %s", name)
		conn.Close()
	}
	c.tradeManager.connMutex.Unlock()

	// 关闭所有盘口数据连接
	c.orderbookManager.connMutex.Lock()
	for name, conn := range c.orderbookManager.connections {
		log.Printf("🔌 关闭盘口数据连接: %s", name)
		conn.Close()
	}
	c.orderbookManager.connMutex.Unlock()

	// 关闭所有资金费率连接
	c.fundingManager.connMutex.Lock()
	for name, conn := range c.fundingManager.connections {
		log.Printf("🔌 关闭资金费率连接: %s", name)
		conn.Close()
	}
	c.fundingManager.connMutex.Unlock()

	// 等待所有goroutine结束
	c.wg.Wait()

	// 关闭Kafka生产者
	if c.kafkaProducer != nil {
		c.kafkaProducer.Close()
	}

	// 打印最终统计
	totalTrade := atomic.LoadInt64(&c.totalTradeCount)
	totalOrderbook := atomic.LoadInt64(&c.totalOrderbookCount)
	totalFunding := atomic.LoadInt64(&c.totalFundingCount)

	log.Printf("✅ Hyperliquid客户端已停止 - 总交易数据: %d 条, 总盘口数据: %d 条, 总资金费率: %d 条", totalTrade, totalOrderbook, totalFunding)

	// 打印最终Kafka统计
	if c.kafkaProducer != nil {
		kafkaTradeCount, kafkaOrderbookCount, kafkaFundingCount, kafkaTotalCount, kafkaErrorCount := c.kafkaProducer.GetKafkaStats()
		if kafkaTotalCount > 0 || kafkaErrorCount > 0 {
			log.Printf("📤 [Kafka] 最终发送统计 - 交易: %d, 盘口: %d, 资金费率: %d, 总计: %d, 错误: %d",
				kafkaTradeCount, kafkaOrderbookCount, kafkaFundingCount, kafkaTotalCount, kafkaErrorCount)
		}
	}
}

// getActiveSymbols 获取活跃交易对
func getActiveSymbols(config *Config) ([]string, error) {
	url := fmt.Sprintf("%s/info", config.RestAPIURL)

	// 构建请求体
	requestBody := map[string]interface{}{
		"type": "meta",
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("构建请求失败: %v", err)
	}

	// 发送POST请求
	resp, err := http.Post(url, "application/json", strings.NewReader(string(jsonData)))
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	var result struct {
		Universe []struct {
			Name string `json:"name"`
		} `json:"universe"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	var symbols []string
	for _, asset := range result.Universe {
		symbols = append(symbols, asset.Name)
	}

	log.Printf("📊 获取到 %d 个活跃交易对", len(symbols))
	return symbols, nil
}

// setupLogging 设置日志
func setupLogging(config *Config) error {
	// 创建日志目录
	logDir := filepath.Dir(config.LogFile)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 打开日志文件
	logFile, err := os.OpenFile(config.LogFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	// 设置日志输出到文件
	log.SetOutput(logFile)
	log.SetFlags(log.LstdFlags | log.Lshortfile)

	return nil
}

func main() {
	// 加载配置
	config, err := loadConfig("config.json")
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 设置日志
	if err := setupLogging(config); err != nil {
		log.Fatalf("❌ 设置日志失败: %v", err)
	}

	// 创建数据处理器
	dataHandler := NewHyperliquidDataHandler()

	// 创建Hyperliquid客户端
	client := NewHyperliquidClient(config, dataHandler)

	log.Printf("🚀 启动Hyperliquid数据订阅程序")
	log.Printf("📡 WebSocket地址: %s", config.WebSocketURL)
	log.Printf("🔧 心跳间隔: %v", config.GetPingInterval())

	// 获取活跃交易对
	if err := client.GetAllSymbols(); err != nil {
		log.Fatalf("❌ 获取交易对失败: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动数据订阅
	if err := client.SubscribeData(); err != nil {
		log.Fatalf("❌ 启动订阅失败: %v", err)
	}

	log.Printf("🎉 所有订阅已启动，等待数据...")
	log.Printf("💡 程序具备以下健壮性保障：")
	log.Printf("   • 数据类型分离：交易数据和盘口数据使用独立连接")
	log.Printf("   • 自动重连机制：连接断开后自动重连")
	log.Printf("   • 心跳检测：定期发送心跳，检测连接状态")
	log.Printf("   • 连接监控：实时监控连接健康状态")
	log.Printf("   • 批次处理：每批25个交易对，减少连接压力")

	// 等待信号
	<-sigChan
	log.Printf("📡 收到停止信号")

	// 停止客户端
	client.Stop()
}
